import 'package:flutter/material.dart';
import 'package:mlink/models/technician.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/models/service_rates.dart';
import 'package:mlink/widgets/app_logo.dart';
import 'package:mlink/widgets/location_selector.dart';
import 'package:mlink/screens/maps/location_picker_screen.dart';
import 'package:mlink/services/maps_service.dart';
import 'package:mlink/services/configuration_service.dart';

class TechnicianRequestScreen extends StatefulWidget {
  const TechnicianRequestScreen({super.key});

  @override
  State<TechnicianRequestScreen> createState() => _TechnicianRequestScreenState();
}

class _TechnicianRequestScreenState extends State<TechnicianRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  final _estimatedHoursController = TextEditingController();
  final _notesController = TextEditingController();
  
  final MapsService _mapsService = MapsService.instance;
  LocationPoint? _serviceLocation;
  
  // Location selector state
  String? _selectedRegion;
  String? _selectedDistrict;
  LocationPoint? _customLocation;
  
  // Rate selection state
  HourlyRate? _selectedRate;
  final _customRateController = TextEditingController();
  bool _showCustomRate = false;

  TechnicianCategory _selectedCategory = TechnicianCategory.electrician;
  PaymentMethod _selectedPayment = PaymentMethod.cash;
  bool _isUrgent = false;
  bool _isScheduled = false;
  DateTime? _scheduledDate;
  TimeOfDay? _scheduledTime;

  List<String> _requiredTools = [''];

  @override
  void dispose() {
    _descriptionController.dispose();
    _addressController.dispose();
    _estimatedHoursController.dispose();
    _notesController.dispose();
    _customRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Technician Service'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: AppLogo(
              width: AppLogoSizes.small,
              height: AppLogoSizes.small,
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.tertiaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.build,
                      color: Theme.of(context).colorScheme.tertiary,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Technician Service',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Get professional repair and maintenance services',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Service Category
              Text(
                'Service Category',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<TechnicianCategory>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.build_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  isExpanded: true,
                  menuMaxHeight: 250,
                  items: TechnicianCategory.values.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: FutureBuilder<String>(
                        future: _getCategoryDisplayName(category),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return Text(
                              snapshot.data!,
                              overflow: TextOverflow.ellipsis,
                            );
                          }
                          return Text(
                            category.name,
                            overflow: TextOverflow.ellipsis,
                          );
                        },
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedCategory = value!);
                  },
                ),
              ),
              const SizedBox(height: 16),

              // Problem Description
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: 'Problem Description',
                  prefixIcon: Icon(
                    Icons.description_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'Describe the issue you need help with',
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please describe the problem';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Service Location
              LocationSelector(
                label: 'Service Location',
                selectedRegion: _selectedRegion,
                selectedDistrict: _selectedDistrict,
                customLocation: _customLocation,
                onRegionChanged: (region) {
                  setState(() {
                    _selectedRegion = region;
                    _updateServiceLocation();
                  });
                },
                onDistrictChanged: (district) {
                  setState(() {
                    _selectedDistrict = district;
                    _updateServiceLocation();
                  });
                },
                onCustomLocationChanged: (location) {
                  setState(() {
                    _customLocation = location;
                    _updateServiceLocation();
                  });
                },
                hint: 'e.g., Mwenge, Sinza, Mbezi Beach',
              ),
              const SizedBox(height: 16),

              // Estimated Hours
              TextFormField(
                controller: _estimatedHoursController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Estimated Hours (Optional)',
                  prefixIcon: Icon(
                    Icons.access_time_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'How long do you think this will take?',
                ),
              ),
              const SizedBox(height: 16),

              // Hourly Rate
              Text(
                'Expected Hourly Rate',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: FutureBuilder<List<HourlyRate>>(
                  future: ConfigurationService.getCommonHourlyRates(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) {
                      return DropdownButtonFormField<HourlyRate>(
                        value: null,
                        decoration: InputDecoration(
                          prefixIcon: Icon(
                            Icons.currency_exchange_outlined,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          labelText: 'Loading rates...',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                        items: [],
                        onChanged: null,
                      );
                    }
                    
                    final rates = snapshot.data!;
                    // Ensure selected rate is valid for the loaded rates
                    if (_selectedRate != null && !rates.contains(_selectedRate)) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          setState(() {
                            _selectedRate = null;
                          });
                        }
                      });
                    }
                    
                    return DropdownButtonFormField<HourlyRate>(
                      value: _selectedRate,
                      decoration: InputDecoration(
                        prefixIcon: Icon(
                          Icons.currency_exchange_outlined,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        labelText: 'Select Rate Range',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      isExpanded: true,
                      menuMaxHeight: 250,
                      items: rates.map((rate) {
                        return DropdownMenuItem(
                          value: rate,
                          child: Text(
                            rate.displayText,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedRate = value;
                          _showCustomRate = value?.isCustom ?? false;
                          if (!_showCustomRate) {
                            _customRateController.clear();
                          }
                        });
                      },
                    );
                  },
                ),
              ),
              
              if (_showCustomRate) ...[
                const SizedBox(height: 12),
                TextFormField(
                  controller: _customRateController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.edit_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    labelText: 'Enter Custom Rate (TSh/hr)',
                    hintText: 'e.g., 8000',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  validator: (value) {
                    if (_showCustomRate && (value == null || value.trim().isEmpty)) {
                      return 'Please enter a custom rate';
                    }
                    if (_showCustomRate && double.tryParse(value!) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),
              ],
              const SizedBox(height: 24),

              // Required Tools
              Text(
                'Required Tools/Equipment',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              ...List.generate(_requiredTools.length, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: _requiredTools[index],
                          decoration: InputDecoration(
                            labelText: 'Tool ${index + 1}',
                            prefixIcon: Icon(
                              Icons.construction_outlined,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onChanged: (value) {
                            _requiredTools[index] = value;
                          },
                        ),
                      ),
                      if (_requiredTools.length > 1)
                        IconButton(
                          icon: const Icon(Icons.remove_circle_outline),
                          onPressed: () {
                            setState(() => _requiredTools.removeAt(index));
                          },
                        ),
                    ],
                  ),
                );
              }),
              
              OutlinedButton.icon(
                onPressed: () {
                  setState(() => _requiredTools.add(''));
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Tool'),
              ),
              const SizedBox(height: 24),

              // Service Options
              Text(
                'Service Options',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              CheckboxListTile(
                title: const Text('Urgent Service'),
                subtitle: const Text('Need immediate attention'),
                value: _isUrgent,
                onChanged: (value) {
                  setState(() => _isUrgent = value ?? false);
                },
              ),
              
              CheckboxListTile(
                title: const Text('Schedule for later'),
                value: _isScheduled,
                onChanged: (value) {
                  setState(() => _isScheduled = value ?? false);
                },
              ),

              if (_isScheduled) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _selectDate(context),
                        icon: const Icon(Icons.calendar_today),
                        label: Text(
                          _scheduledDate != null
                              ? '${_scheduledDate!.day}/${_scheduledDate!.month}/${_scheduledDate!.year}'
                              : 'Select Date',
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _selectTime(context),
                        icon: const Icon(Icons.access_time),
                        label: Text(
                          _scheduledTime != null
                              ? _scheduledTime!.format(context)
                              : 'Select Time',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 24),

              // Special Instructions
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: 'Special Instructions (Optional)',
                  prefixIcon: Icon(
                    Icons.note_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'Any specific requirements or safety concerns',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              // Payment Method
              Text(
                'Payment Method',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<PaymentMethod>(
                  value: _selectedPayment,
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.payment_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    labelText: 'Select Payment Method',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  isExpanded: true,
                  menuMaxHeight: 250,
                  items: PaymentMethod.values.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: FutureBuilder<String>(
                        future: _getPaymentDisplayName(method),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return Text(
                              snapshot.data!,
                              overflow: TextOverflow.ellipsis,
                            );
                          }
                          return Text(
                            method.name,
                            overflow: TextOverflow.ellipsis,
                          );
                        },
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedPayment = value!);
                  },
                ),
              ),
              const SizedBox(height: 24),

              // Service Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.tertiary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Service Process',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '1. Qualified technician will contact you\n'
                      '2. Technician will assess the problem\n'
                      '3. Quote will be provided before work starts\n'
                      '4. Work completed and payment processed',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                height: 56,
                child: ElevatedButton(
                  onPressed: _submitRequest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.tertiary,
                    foregroundColor: Theme.of(context).colorScheme.onTertiary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Submit Request',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (picked != null) {
      setState(() => _scheduledDate = picked);
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() => _scheduledTime = picked);
    }
  }

  Future<void> _selectLocation() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LocationPickerScreen(
          title: 'Select Service Location',
          initialLocation: _serviceLocation,
        ),
      ),
    );
    
    if (result != null && result is LocationPoint) {
      setState(() {
        _serviceLocation = result;
        _addressController.text = result.address ?? '';
      });
    }
  }
  
  Future<void> _getCurrentLocation() async {
    final currentLocation = await _mapsService.getCurrentLocationPoint();
    if (currentLocation != null) {
      setState(() {
        _serviceLocation = currentLocation;
        _addressController.text = currentLocation.address ?? '';
      });
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to get current location. Please check permissions.'),
          ),
        );
      }
    }
  }

  void _updateServiceLocation() {
    final selector = SelectedLocation(
      region: _selectedRegion,
      district: _selectedDistrict,
      customLocation: _customLocation,
    );
    _serviceLocation = selector.locationPoint;
  }

  void _submitRequest() {
    if (!_formKey.currentState!.validate()) return;

    if (_serviceLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a service location'),
        ),
      );
      return;
    }

    if (_isScheduled && (_scheduledDate == null || _scheduledTime == null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select date and time for scheduled service'),
        ),
      );
      return;
    }

    double? hourlyRate;
    if (_selectedRate != null) {
      if (_selectedRate!.isCustom) {
        hourlyRate = double.tryParse(_customRateController.text);
      } else {
        hourlyRate = _selectedRate!.value;
      }
    }

    // TODO: Submit technician request with hourly rate
    final rateText = hourlyRate != null ? ' (Rate: ${ServiceRates.formatCurrency(hourlyRate)}/hr)' : '';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Technician service request submitted successfully!$rateText'),
      ),
    );
    Navigator.of(context).pop();
  }

  Future<String> _getCategoryDisplayName(TechnicianCategory category) async {
    return await ConfigurationService.getTechnicianCategoryDisplayName(category.name);
  }

  Future<String> _getPaymentDisplayName(PaymentMethod method) async {
    return await ConfigurationService.getPaymentMethodDisplayName(method.name);
  }
}