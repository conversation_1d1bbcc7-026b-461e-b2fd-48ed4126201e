import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ReviewPayload {
  action: 'get_reviews' | 'create_review' | 'update_review' | 'delete_review' | 'get_user_rating'
  userId?: string
  reviewId?: string
  reviewData?: any
  targetUserId?: string
  requestId?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, userId, reviewId, reviewData, targetUserId, requestId } = await req.json() as ReviewPayload

    switch (action) {
      case 'get_reviews':
        return await getUserReviews(supabase, targetUserId!)
      case 'create_review':
        return await createReview(supabase, reviewData!)
      case 'update_review':
        return await updateReview(supabase, reviewId!, reviewData!)
      case 'delete_review':
        return await deleteReview(supabase, reviewId!, userId!)
      case 'get_user_rating':
        return await getUserRating(supabase, targetUserId!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Review operation error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function getUserReviews(supabase: any, targetUserId: string) {
  const { data: reviews, error } = await supabase
    .from('reviews')
    .select(`
      *,
      reviewer:users!reviews_reviewer_id_fkey(id, full_name, avatar_url),
      reviewee:users!reviews_reviewee_id_fkey(id, full_name),
      request:requests!reviews_request_id_fkey(id, type, title)
    `)
    .eq('reviewee_id', targetUserId)
    .order('created_at', { ascending: false })

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, reviews }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function createReview(supabase: any, reviewData: any) {
  // Check if reviewer has already reviewed this request/user
  const { data: existingReview } = await supabase
    .from('reviews')
    .select('id')
    .eq('reviewer_id', reviewData.reviewer_id)
    .eq('reviewee_id', reviewData.reviewee_id)
    .eq('request_id', reviewData.request_id)
    .maybeSingle()

  if (existingReview) {
    throw new Error('You have already reviewed this user for this request')
  }

  // Verify that the reviewer was part of this request
  const { data: request, error: requestError } = await supabase
    .from('requests')
    .select('customer_id, agent_id, technician_id')
    .eq('id', reviewData.request_id)
    .single()

  if (requestError) throw new Error('Request not found')

  const isAuthorized = request.customer_id === reviewData.reviewer_id ||
                      request.agent_id === reviewData.reviewer_id ||
                      request.technician_id === reviewData.reviewer_id

  if (!isAuthorized) {
    throw new Error('You are not authorized to review this request')
  }

  // Create the review
  const { data: review, error } = await supabase
    .from('reviews')
    .insert({
      ...reviewData,
      created_at: new Date().toISOString(),
    })
    .select(`
      *,
      reviewer:users!reviews_reviewer_id_fkey(id, full_name, avatar_url),
      reviewee:users!reviews_reviewee_id_fkey(id, full_name)
    `)
    .single()

  if (error) throw error

  // Update the user's average rating
  await updateUserRating(supabase, reviewData.reviewee_id)

  // Create notification for the reviewee
  await supabase.from('notifications').insert({
    user_id: reviewData.reviewee_id,
    title: 'New Review Received',
    message: `You received a ${reviewData.rating}-star review: "${reviewData.comment || 'No comment provided'}"`,
    type: 'new_review',
    review_id: review.id,
    created_at: new Date().toISOString(),
  })

  return new Response(
    JSON.stringify({ success: true, review }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function updateReview(supabase: any, reviewId: string, reviewData: any) {
  const { data: review, error } = await supabase
    .from('reviews')
    .update({
      ...reviewData,
      updated_at: new Date().toISOString(),
    })
    .eq('id', reviewId)
    .select(`
      *,
      reviewer:users!reviews_reviewer_id_fkey(id, full_name, avatar_url),
      reviewee:users!reviews_reviewee_id_fkey(id, full_name)
    `)
    .single()

  if (error) throw error

  // Update the user's average rating
  await updateUserRating(supabase, review.reviewee_id)

  return new Response(
    JSON.stringify({ success: true, review }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function deleteReview(supabase: any, reviewId: string, userId: string) {
  // Get the review to check ownership and get reviewee_id
  const { data: review, error: getError } = await supabase
    .from('reviews')
    .select('reviewer_id, reviewee_id')
    .eq('id', reviewId)
    .single()

  if (getError) throw new Error('Review not found')

  // Check if the user is authorized to delete this review
  if (review.reviewer_id !== userId) {
    throw new Error('You can only delete your own reviews')
  }

  // Delete the review
  const { error } = await supabase
    .from('reviews')
    .delete()
    .eq('id', reviewId)

  if (error) throw error

  // Update the user's average rating
  await updateUserRating(supabase, review.reviewee_id)

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getUserRating(supabase: any, targetUserId: string) {
  const { data: ratingData, error } = await supabase
    .from('reviews')
    .select('rating')
    .eq('reviewee_id', targetUserId)

  if (error) throw error

  const totalReviews = ratingData?.length || 0
  const averageRating = totalReviews > 0 
    ? ratingData.reduce((sum: number, review: any) => sum + review.rating, 0) / totalReviews
    : 0

  return new Response(
    JSON.stringify({ 
      success: true, 
      rating: {
        average: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        total: totalReviews,
      }
    }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function updateUserRating(supabase: any, userId: string) {
  // Calculate new average rating
  const { data: reviews, error: reviewsError } = await supabase
    .from('reviews')
    .select('rating')
    .eq('reviewee_id', userId)

  if (reviewsError) {
    console.error('Failed to fetch reviews for rating update:', reviewsError)
    return
  }

  const totalReviews = reviews?.length || 0
  const averageRating = totalReviews > 0 
    ? reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / totalReviews
    : 0

  // Get user's role to determine which table to update
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('role')
    .eq('id', userId)
    .single()

  if (userError) {
    console.error('Failed to get user role for rating update:', userError)
    return
  }

  // Update rating in the appropriate table
  if (user.role === 'agent') {
    await supabase
      .from('agents')
      .update({ 
        rating: Math.round(averageRating * 10) / 10,
        total_reviews: totalReviews,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
  } else if (user.role === 'technician') {
    await supabase
      .from('technicians')
      .update({ 
        rating: Math.round(averageRating * 10) / 10,
        total_reviews: totalReviews,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
  }
}