import 'package:flutter/material.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/services/admin/admin_service.dart';
import 'package:intl/intl.dart';
import 'package:data_table_2/data_table_2.dart';

class UserManager extends StatefulWidget {
  const UserManager({super.key});

  @override
  State<UserManager> createState() => _UserManagerState();
}

class _UserManagerState extends State<UserManager> {
  List<User> users = [];
  bool isLoading = true;
  UserRole? filterRole;
  UserStatus? filterStatus;
  String searchQuery = '';
  final searchController = TextEditingController();
  final dateFormatter = DateFormat('MMM dd, yyyy');

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final data = await AdminService.instance.getAllUsers();
      setState(() {
        users = data;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading users: $e');
      setState(() => isLoading = false);
    }
  }

  List<User> get filteredUsers {
    return users.where((user) {
      // Filter by role
      if (filterRole != null && user.role != filterRole) {
        return false;
      }
      // Filter by status
      if (filterStatus != null && user.status != filterStatus) {
        return false;
      }
      // Filter by search query
      if (searchQuery.isNotEmpty) {
        final query = searchQuery.toLowerCase();
        final matchesName = user.fullName?.toLowerCase().contains(query) ?? false;
        final matchesEmail = user.email.toLowerCase().contains(query);
        final matchesPhone = user.phoneNumber?.toLowerCase().contains(query) ?? false;
        if (!matchesName && !matchesEmail && !matchesPhone) {
          return false;
        }
      }
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'User Manager',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          SizedBox(
            width: 300,
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: 'Search users...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          searchController.clear();
                          setState(() => searchQuery = '');
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
              onChanged: (value) => setState(() => searchQuery = value),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Filters and Stats
                _buildFiltersAndStats(),
                
                // Users Table
                Expanded(
                  child: _buildUsersTable(),
                ),
              ],
            ),
    );
  }

  Widget _buildFiltersAndStats() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Role Filter
          DropdownButton<UserRole?>(
            value: filterRole,
            hint: const Text('Filter by Role'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Roles')),
              ...UserRole.values.map((role) => DropdownMenuItem(
                value: role,
                child: Text(role.name.toUpperCase()),
              )),
            ],
            onChanged: (value) => setState(() => filterRole = value),
          ),
          
          const SizedBox(width: 16),
          
          // Status Filter
          DropdownButton<UserStatus?>(
            value: filterStatus,
            hint: const Text('Filter by Status'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Statuses')),
              ...UserStatus.values.map((status) => DropdownMenuItem(
                value: status,
                child: Text(status.name.toUpperCase()),
              )),
            ],
            onChanged: (value) => setState(() => filterStatus = value),
          ),
          
          // Clear filters button
          if (filterRole != null || filterStatus != null) ...[
            const SizedBox(width: 16),
            OutlinedButton.icon(
              onPressed: () => setState(() {
                filterRole = null;
                filterStatus = null;
              }),
              icon: const Icon(Icons.clear),
              label: const Text('Clear Filters'),
            ),
          ],
          
          const Spacer(),
          
          // Stats
          _buildUserStats(),
        ],
      ),
    );
  }

  Widget _buildUserStats() {
    final theme = Theme.of(context);
    final stats = {
      'Total': users.length,
      'Customers': users.where((u) => u.role == UserRole.customer).length,
      'Agents': users.where((u) => u.role == UserRole.agent).length,
      'Technicians': users.where((u) => u.role == UserRole.technician).length,
    };

    return Row(
      children: stats.entries.map((entry) => Container(
        margin: const EdgeInsets.only(left: 16),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              entry.value.toString(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            Text(
              entry.key,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildUsersTable() {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: DataTable2(
        columnSpacing: 12,
        horizontalMargin: 12,
        minWidth: 900,
        columns: const [
          DataColumn2(
            label: Text('User'),
            size: ColumnSize.L,
          ),
          DataColumn2(
            label: Text('Contact'),
            size: ColumnSize.M,
          ),
          DataColumn2(
            label: Text('Role'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Status'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Joined'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Actions'),
            size: ColumnSize.M,
          ),
        ],
        rows: filteredUsers.map((user) => DataRow2(
          cells: [
            DataCell(
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: _getRoleColor(user.role).withValues(alpha: 0.1),
                    backgroundImage: user.avatarUrl != null 
                        ? NetworkImage(user.avatarUrl!)
                        : null,
                    child: user.avatarUrl == null
                        ? Icon(
                            Icons.person,
                            color: _getRoleColor(user.role),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          user.fullName ?? 'No Name',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          user.email,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (user.phoneNumber != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.phone,
                          size: 14,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user.phoneNumber!,
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ] else
                    Text(
                      'No phone',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
                      ),
                    ),
                ],
              ),
            ),
            DataCell(
              Chip(
                label: Text(
                  user.role.name.toUpperCase(),
                  style: theme.textTheme.bodySmall,
                ),
                backgroundColor: _getRoleColor(user.role).withValues(alpha: 0.1),
                side: BorderSide.none,
              ),
            ),
            DataCell(
              Chip(
                label: Text(
                  user.status.name.toUpperCase(),
                  style: theme.textTheme.bodySmall,
                ),
                backgroundColor: _getStatusColor(user.status).withValues(alpha: 0.1),
                side: BorderSide.none,
              ),
            ),
            DataCell(
              Text(
                dateFormatter.format(user.createdAt),
                style: theme.textTheme.bodySmall,
              ),
            ),
            DataCell(
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'view',
                        child: Row(
                          children: [
                            const Icon(Icons.visibility, size: 16),
                            const SizedBox(width: 8),
                            const Text('View Details'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'verify',
                        enabled: user.status != UserStatus.active,
                        child: Row(
                          children: [
                            const Icon(Icons.verified, size: 16),
                            const SizedBox(width: 8),
                            const Text('Verify'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'suspend',
                        enabled: user.status != UserStatus.suspended,
                        child: Row(
                          children: [
                            Icon(Icons.block, size: 16, color: Colors.orange),
                            const SizedBox(width: 8),
                            Text('Suspend', style: TextStyle(color: Colors.orange)),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'reset_password',
                        child: Row(
                          children: [
                            const Icon(Icons.lock_reset, size: 16),
                            const SizedBox(width: 8),
                            const Text('Reset Password'),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (action) => _handleUserAction(user, action),
                  ),
                ],
              ),
            ),
          ],
        )).toList(),
      ),
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Colors.blue;
      case UserRole.agent:
        return Colors.green;
      case UserRole.technician:
        return Colors.orange;
      case UserRole.admin:
        return Colors.purple;
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green;
      case UserStatus.inactive:
        return Colors.grey;
      case UserStatus.suspended:
        return Colors.red;
      case UserStatus.pendingVerification:
        return Colors.orange;
    }
  }

  void _handleUserAction(User user, String action) {
    switch (action) {
      case 'view':
        _showUserDetailsDialog(user);
        break;
      case 'verify':
        _updateUserStatus(user, UserStatus.active);
        break;
      case 'suspend':
        _updateUserStatus(user, UserStatus.suspended);
        break;
      case 'reset_password':
        _showResetPasswordDialog(user);
        break;
    }
  }

  void _showUserDetailsDialog(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('User Details - ${user.fullName ?? 'No Name'}'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Email', user.email),
              _buildDetailRow('Phone', user.phoneNumber ?? 'Not provided'),
              _buildDetailRow('Role', user.role.name.toUpperCase()),
              _buildDetailRow('Status', user.status.name.toUpperCase()),
              _buildDetailRow('Joined', dateFormatter.format(user.createdAt)),
              _buildDetailRow('Last Updated', dateFormatter.format(user.updatedAt)),
              if (user.metadata != null) ...[
                const SizedBox(height: 16),
                const Text('Additional Info:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...user.metadata!.entries.map((entry) => 
                    _buildDetailRow(entry.key, entry.value.toString())),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _updateUserStatus(User user, UserStatus newStatus) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update User Status'),
        content: Text(
          'Are you sure you want to change ${user.fullName}\'s status to ${newStatus.name.toUpperCase()}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await AdminService.instance.updateUserStatus(user.id, newStatus);
        _loadUsers();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('User status updated successfully')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating user status: $e')),
        );
      }
    }
  }

  void _showResetPasswordDialog(User user) {
    final passwordController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Password - ${user.fullName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Generate a new password for ${user.email}'),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(
                labelText: 'New Password',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (passwordController.text.length < 6) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Password must be at least 6 characters'),
                  ),
                );
                return;
              }

              try {
                await AdminService.instance.resetUserPassword(
                  user.id, 
                  passwordController.text
                );
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Password reset successfully'),
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error resetting password: $e')),
                );
              }
            },
            child: const Text('Reset Password'),
          ),
        ],
      ),
    );
  }
}