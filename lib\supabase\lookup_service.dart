import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/tanzania_locations.dart';
import '../models/service_rates.dart';

/// Service class for querying lookup/configuration data from Supabase
/// This replaces all hardcoded data with dynamic database queries
class LookupService {
  static final SupabaseClient _client = Supabase.instance.client;

  // REGION AND LOCATION METHODS

  /// Get all active regions
  static Future<List<Map<String, dynamic>>> getActiveRegions() async {
    try {
      final response = await _client
          .from('active_regions')
          .select()
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get regions: ${e.toString()}');
    }
  }

  /// Get districts for a specific region
  static Future<List<Map<String, dynamic>>> getDistrictsByRegion(String regionId) async {
    try {
      final response = await _client
          .from('active_districts')
          .select()
          .eq('region_id', regionId)
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get districts: ${e.toString()}');
    }
  }

  /// Get all districts with region names
  static Future<List<Map<String, dynamic>>> getAllDistrictsWithRegions() async {
    try {
      final response = await _client
          .from('active_districts')
          .select()
          .order('region_name')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get districts with regions: ${e.toString()}');
    }
  }

  /// Convert database regions to TanzaniaLocation objects (for backward compatibility)
  static Future<List<TanzaniaLocation>> getTanzaniaLocations() async {
    try {
      final regions = await getActiveRegions();
      final allDistricts = await getAllDistrictsWithRegions();
      
      List<TanzaniaLocation> locations = [];
      
      for (var region in regions) {
        // Get districts for this region
        final regionDistricts = allDistricts
            .where((d) => d['region_id'] == region['id'])
            .toList();
        
        // Convert to LocationPoint objects
        List<LocationPoint> points = regionDistricts.map((district) {
          final coords = district['coordinates'] as Map<String, dynamic>?;
          return LocationPoint(
            name: district['name'],
            latitude: coords?['lat']?.toDouble() ?? 0.0,
            longitude: coords?['lng']?.toDouble() ?? 0.0,
          );
        }).toList();
        
        final regionCoords = region['coordinates'] as Map<String, dynamic>?;
        locations.add(TanzaniaLocation(
          region: region['name'],
          latitude: regionCoords?['lat']?.toDouble() ?? 0.0,
          longitude: regionCoords?['lng']?.toDouble() ?? 0.0,
          points: points,
        ));
      }
      
      return locations;
    } catch (e) {
      throw DatabaseException('Failed to get Tanzania locations: ${e.toString()}');
    }
  }

  // VEHICLE TYPE METHODS

  /// Get all active vehicle types
  static Future<List<Map<String, dynamic>>> getActiveVehicleTypes() async {
    try {
      final response = await _client
          .from('active_vehicle_types')
          .select()
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get vehicle types: ${e.toString()}');
    }
  }

  /// Get vehicle type by code
  static Future<Map<String, dynamic>?> getVehicleTypeByCode(String code) async {
    try {
      final response = await _client
          .from('vehicle_types')
          .select()
          .eq('code', code)
          .eq('is_active', true)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get vehicle type: ${e.toString()}');
    }
  }

  // TECHNICIAN CATEGORY METHODS

  /// Get all active technician categories
  static Future<List<Map<String, dynamic>>> getActiveTechnicianCategories() async {
    try {
      final response = await _client
          .from('active_technician_categories')
          .select()
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get technician categories: ${e.toString()}');
    }
  }

  /// Get technician category by code
  static Future<Map<String, dynamic>?> getTechnicianCategoryByCode(String code) async {
    try {
      final response = await _client
          .from('technician_categories')
          .select()
          .eq('code', code)
          .eq('is_active', true)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get technician category: ${e.toString()}');
    }
  }

  // PAYMENT METHOD METHODS

  /// Get all active payment methods
  static Future<List<Map<String, dynamic>>> getActivePaymentMethods() async {
    try {
      final response = await _client
          .from('active_payment_methods')
          .select()
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get payment methods: ${e.toString()}');
    }
  }

  /// Get payment method by code
  static Future<Map<String, dynamic>?> getPaymentMethodByCode(String code) async {
    try {
      final response = await _client
          .from('payment_methods')
          .select()
          .eq('code', code)
          .eq('is_active', true)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get payment method: ${e.toString()}');
    }
  }

  // DELIVERY PRIORITY METHODS

  /// Get all active delivery priorities
  static Future<List<Map<String, dynamic>>> getActiveDeliveryPriorities() async {
    try {
      final response = await _client
          .from('active_delivery_priorities')
          .select()
          .order('priority_level', ascending: false)
          .order('display_order');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get delivery priorities: ${e.toString()}');
    }
  }

  /// Get delivery priority by code
  static Future<Map<String, dynamic>?> getDeliveryPriorityByCode(String code) async {
    try {
      final response = await _client
          .from('delivery_priorities')
          .select()
          .eq('code', code)
          .eq('is_active', true)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get delivery priority: ${e.toString()}');
    }
  }

  // REQUEST STATUS METHODS

  /// Get all active request statuses
  static Future<List<Map<String, dynamic>>> getActiveRequestStatuses() async {
    try {
      final response = await _client
          .from('active_request_statuses')
          .select()
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get request statuses: ${e.toString()}');
    }
  }

  /// Get request status by code
  static Future<Map<String, dynamic>?> getRequestStatusByCode(String code) async {
    try {
      final response = await _client
          .from('request_statuses')
          .select()
          .eq('code', code)
          .eq('is_active', true)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get request status: ${e.toString()}');
    }
  }

  // SERVICE RATE METHODS

  /// Get all active service rates
  static Future<List<Map<String, dynamic>>> getActiveServiceRates() async {
    try {
      final response = await _client
          .from('active_service_rates')
          .select()
          .order('service_type')
          .order('rate_value');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get service rates: ${e.toString()}');
    }
  }

  /// Get hourly rates for technician services
  static Future<List<Map<String, dynamic>>> getHourlyRates() async {
    try {
      final response = await _client
          .from('service_rates')
          .select()
          .like('service_type', 'hourly_rate_%')
          .eq('is_active', true)
          .order('rate_value');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get hourly rates: ${e.toString()}');
    }
  }

  /// Get delivery rates (base cost and per km)
  static Future<Map<String, dynamic>> getDeliveryRates() async {
    try {
      final response = await _client
          .from('service_rates')
          .select()
          .inFilter('service_type', ['delivery_base_cost', 'delivery_per_km'])
          .eq('is_active', true);
      
      double baseCost = 2000.0; // Default fallback
      double costPerKm = 1000.0; // Default fallback
      
      for (var rate in response) {
        if (rate['service_type'] == 'delivery_base_cost') {
          baseCost = (rate['rate_value'] as num).toDouble();
        } else if (rate['service_type'] == 'delivery_per_km') {
          costPerKm = (rate['rate_value'] as num).toDouble();
        }
      }
      
      return {
        'base_cost': baseCost,
        'cost_per_km': costPerKm,
      };
    } catch (e) {
      throw DatabaseException('Failed to get delivery rates: ${e.toString()}');
    }
  }

  /// Convert database rates to HourlyRate objects (for backward compatibility)
  static Future<List<HourlyRate>> getCommonHourlyRates() async {
    try {
      final rates = await getHourlyRates();
      return rates.map((rate) {
        return HourlyRate(
          value: (rate['rate_value'] as num).toDouble(),
          displayText: rate['display_text'] ?? 'TSh ${rate['rate_value']}/hr',
        );
      }).toList();
    } catch (e) {
      throw DatabaseException('Failed to get common hourly rates: ${e.toString()}');
    }
  }

  // SERVICE CATEGORY METHODS

  /// Get all active service categories
  static Future<List<Map<String, dynamic>>> getActiveServiceCategories() async {
    try {
      final response = await _client
          .from('active_service_categories')
          .select()
          .order('display_order')
          .order('name');
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get service categories: ${e.toString()}');
    }
  }

  /// Get service category by code
  static Future<Map<String, dynamic>?> getServiceCategoryByCode(String code) async {
    try {
      final response = await _client
          .from('service_categories')
          .select()
          .eq('code', code)
          .eq('is_active', true)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get service category: ${e.toString()}');
    }
  }

  // UTILITY METHODS

  /// Refresh all lookup data (useful for admin operations)
  static Future<void> refreshLookupData() async {
    // This could be used to invalidate caches or force refresh
    // For now, it's a placeholder for future caching implementations
  }

  /// Check if lookup tables have data (useful for first-time setup)
  static Future<bool> hasLookupData() async {
    try {
      final regions = await _client.from('regions').select('id').limit(1);
      return regions.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}

/// Exception class for database-related errors in lookup operations
class DatabaseException implements Exception {
  final String message;
  DatabaseException(this.message);
  
  @override
  String toString() => 'DatabaseException: $message';
}