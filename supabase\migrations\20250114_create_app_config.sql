-- Create app_config table for dynamic global application settings
CREATE TABLE IF NOT EXISTS app_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    value_type VARCHAR(20) DEFAULT 'string' CHECK (value_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_sensitive BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE, -- Can be accessed without authentication
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add updated_at trigger
CREATE TRIGGER update_app_config_updated_at BEFORE UPDATE ON app_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_app_config_key ON app_config(key);
CREATE INDEX IF NOT EXISTS idx_app_config_category ON app_config(category);
CREATE INDEX IF NOT EXISTS idx_app_config_public ON app_config(is_public);

-- Enable RLS
ALTER TABLE app_config ENABLE ROW LEVEL SECURITY;

-- Allow read access for admin users
CREATE POLICY "Allow admin full access" ON app_config FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin')
);

-- Allow read access to public settings
CREATE POLICY "Allow public read access" ON app_config FOR SELECT USING (is_public = true);

-- Insert default configuration values
INSERT INTO app_config (key, value, value_type, description, category) VALUES
('base_delivery_fee', '5000', 'number', 'Base delivery fee in TSh', 'pricing'),
('delivery_fee_per_km', '1000', 'number', 'Additional fee per kilometer in TSh', 'pricing'),
('technician_hourly_rate', '15000', 'number', 'Default technician hourly rate in TSh', 'pricing'),
('agent_commission_percentage', '15', 'number', 'Commission percentage for agents', 'pricing'),
('app_name', 'M-Link', 'string', 'Application name', 'general'),
('app_version', '1.0.0', 'string', 'Current app version', 'general'),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 'general'),
('enable_delivery_service', 'true', 'boolean', 'Enable delivery service', 'features'),
('enable_buy_for_me_service', 'true', 'boolean', 'Enable buy-for-me service', 'features'),
('enable_technician_service', 'true', 'boolean', 'Enable technician service', 'features'),
('max_delivery_distance_km', '50', 'number', 'Maximum delivery distance in kilometers', 'limits'),
('min_delivery_fee', '3000', 'number', 'Minimum delivery fee in TSh', 'pricing'),
('max_delivery_fee', '100000', 'number', 'Maximum delivery fee in TSh', 'pricing'),
('support_phone', '+255123456789', 'string', 'Support phone number', 'contact'),
('support_email', '<EMAIL>', 'string', 'Support email address', 'contact'),
('privacy_policy_url', 'https://mlink.co.tz/privacy', 'string', 'Privacy policy URL', 'legal'),
('terms_of_service_url', 'https://mlink.co.tz/terms', 'string', 'Terms of service URL', 'legal');