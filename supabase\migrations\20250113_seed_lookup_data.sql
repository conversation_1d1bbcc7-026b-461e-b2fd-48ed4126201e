-- Seed data for lookup/configuration tables
-- This populates the tables with current hardcoded data from the Flutter app

-- 1. Seed regions table with Tanzania regions
INSERT INTO regions (name, coordinates, display_order) VALUES
('Dar es Salaam', '{"lat": -6.7924, "lng": 39.2083}', 1),
('Arusha', '{"lat": -3.3869, "lng": 36.6830}', 2),
('Dodoma', '{"lat": -6.1630, "lng": 35.7516}', 3),
('Mwanza', '{"lat": -2.5164, "lng": 32.9175}', 4),
('<PERSON>beya', '{"lat": -8.9094, "lng": 33.4607}', 5),
('Morog<PERSON>', '{"lat": -6.8235, "lng": 37.6606}', 6),
('<PERSON><PERSON>', '{"lat": -5.0692, "lng": 39.0968}', 7),
('<PERSON><PERSON><PERSON>', '{"lat": -5.0165, "lng": 32.8074}', 8),
('<PERSON><PERSON><PERSON><PERSON><PERSON>', '{"lat": -3.2180, "lng": 37.3732}', 9),
('<PERSON>wani', '{"lat": -7.0895, "lng": 39.2827}', 10),
('Mara', '{"lat": -1.7707, "lng": 33.9249}', 11),
('<PERSON>di', '{"lat": -10.0023, "lng": 39.7178}', 12),
('Shinyanga', '{"lat": -3.6636, "lng": 33.4218}', 13),
('Singida', '{"lat": -4.8164, "lng": 34.7736}', 14),
('Iringa', '{"lat": -7.7695, "lng": 35.6929}', 15),
('Rukwa', '{"lat": -7.3895, "lng": 31.2849}', 16),
('Kigoma', '{"lat": -4.8740, "lng": 29.6269}', 17),
('Ruvuma', '{"lat": -10.9415, "lng": 35.1755}', 18),
('Mtwara', '{"lat": -10.2692, "lng": 40.1836}', 19),
('Kagera', '{"lat": -1.3373, "lng": 31.1914}', 20),
('Katavi', '{"lat": -6.2830, "lng": 31.2349}', 21),
('Njombe', '{"lat": -9.3327, "lng": 34.7624}', 22),
('Simiyu', '{"lat": -2.8084, "lng": 34.0151}', 23),
('Geita', '{"lat": -2.8717, "lng": 32.2175}', 24),
('Songwe', '{"lat": -9.1519, "lng": 33.4607}', 25),
('Pemba North', '{"lat": -4.867, "lng": 39.75}', 26),
('Pemba South', '{"lat": -5.1333, "lng": 39.7167}', 27),
('Unguja North', '{"lat": -5.72, "lng": 39.22}', 28),
('Unguja South', '{"lat": -6.33, "lng": 39.50}', 29),
('Unguja West', '{"lat": -6.165, "lng": 39.202}', 30)
ON CONFLICT (name) DO NOTHING;

-- 2. Seed districts table (Sample districts for major regions)
-- Note: This is a subset - you may need to add more based on your complete data
DO $$
DECLARE
    dar_region_id UUID;
    arusha_region_id UUID;
    dodoma_region_id UUID;
BEGIN
    -- Get region IDs
    SELECT id INTO dar_region_id FROM regions WHERE name = 'Dar es Salaam';
    SELECT id INTO arusha_region_id FROM regions WHERE name = 'Arusha';
    SELECT id INTO dodoma_region_id FROM regions WHERE name = 'Dodoma';
    
    -- Insert districts for Dar es Salaam
    INSERT INTO districts (region_id, name, coordinates, display_order) VALUES
    (dar_region_id, 'Kinondoni', '{"lat": -6.7924, "lng": 39.2083}', 1),
    (dar_region_id, 'Ilala', '{"lat": -6.8160, "lng": 39.2803}', 2),
    (dar_region_id, 'Temeke', '{"lat": -6.8500, "lng": 39.2667}', 3),
    (dar_region_id, 'Ubungo', '{"lat": -6.7800, "lng": 39.2500}', 4),
    (dar_region_id, 'Kigamboni', '{"lat": -6.8833, "lng": 39.2667}', 5);
    
    -- Insert districts for Arusha
    INSERT INTO districts (region_id, name, coordinates, display_order) VALUES
    (arusha_region_id, 'Arusha City', '{"lat": -3.3869, "lng": 36.6830}', 1),
    (arusha_region_id, 'Arumeru', '{"lat": -3.6167, "lng": 36.7000}', 2),
    (arusha_region_id, 'Karatu', '{"lat": -3.3333, "lng": 35.8000}', 3),
    (arusha_region_id, 'Longido', '{"lat": -2.7000, "lng": 36.7167}', 4),
    (arusha_region_id, 'Monduli', '{"lat": -3.3500, "lng": 36.4833}', 5),
    (arusha_region_id, 'Ngorongoro', '{"lat": -3.2000, "lng": 35.5500}', 6);
    
    -- Insert districts for Dodoma
    INSERT INTO districts (region_id, name, coordinates, display_order) VALUES
    (dodoma_region_id, 'Dodoma City', '{"lat": -6.1630, "lng": 35.7516}', 1),
    (dodoma_region_id, 'Bahi', '{"lat": -5.9667, "lng": 35.3167}', 2),
    (dodoma_region_id, 'Chamwino', '{"lat": -6.1833, "lng": 35.7167}', 3),
    (dodoma_region_id, 'Chemba', '{"lat": -6.0167, "lng": 35.0833}', 4),
    (dodoma_region_id, 'Kondoa', '{"lat": -4.9000, "lng": 35.7833}', 5),
    (dodoma_region_id, 'Kongwa', '{"lat": -6.1667, "lng": 36.2667}', 6),
    (dodoma_region_id, 'Mpwapwa', '{"lat": -6.3500, "lng": 36.4833}', 7);
END $$;

-- 3. Seed vehicle_types table
INSERT INTO vehicle_types (code, name, icon, description, display_order) VALUES
('bodaboda', 'Bodaboda', '🏍️', 'Small packages, documents', 1),
('bicycle', 'Bicycle', '🚲', 'Light packages, eco-friendly', 2),
('bajaji', 'Bajaji', '🛺', 'Medium packages, quick delivery', 3),
('car', 'Car', '🚗', 'Medium packages, comfortable', 4),
('pickup', 'Pickup', '🛻', 'Large packages, furniture', 5),
('van', 'Van', '🚐', 'Large packages, bulk items', 6),
('truck', 'Truck', '🚚', 'Heavy items, long distance', 7),
('other', 'Other', '🚛', 'Special requirements', 8)
ON CONFLICT (code) DO NOTHING;

-- 4. Seed technician_categories table
INSERT INTO technician_categories (code, name, icon, description, display_order) VALUES
('electrician', 'Electrician', '⚡', 'Electrical repairs and installations', 1),
('plumber', 'Plumber', '🔧', 'Water, drainage and pipe repairs', 2),
('mechanic', 'Mechanic', '🔩', 'Vehicle and machinery repairs', 3),
('carpenter', 'Carpenter', '🪚', 'Wood work and furniture repairs', 4),
('painter', 'Painter', '🎨', 'Interior and exterior painting', 5),
('cleaner', 'Cleaner', '🧹', 'House and office cleaning services', 6),
('gardener', 'Gardener', '🌱', 'Garden maintenance and landscaping', 7),
('appliance_repair', 'Appliance Repair', '🔨', 'Home appliance repairs', 8),
('computer_repair', 'Computer Repair', '💻', 'Computer and IT equipment repair', 9),
('phone_repair', 'Phone Repair', '📱', 'Mobile phone and device repair', 10),
('hvac', 'HVAC', '🌡️', 'Heating, ventilation and air conditioning', 11),
('other', 'Other', '🛠️', 'Other technical services', 12)
ON CONFLICT (code) DO NOTHING;

-- 5. Seed payment_methods table
INSERT INTO payment_methods (code, name, icon, description, display_order) VALUES
('cash', 'Cash on Delivery', '💵', 'Pay with cash when service is delivered', 1),
('mobile_money', 'Mobile Money', '📱', 'Pay with M-Pesa, Tigo Pesa, Airtel Money', 2),
('card', 'Card Payment', '💳', 'Pay with credit or debit card', 3),
('wallet', 'M-Link Wallet', '🏦', 'Pay from your M-Link account balance', 4)
ON CONFLICT (code) DO NOTHING;

-- 6. Seed delivery_priorities table
INSERT INTO delivery_priorities (code, name, description, priority_level, display_order) VALUES
('normal', 'Normal Delivery', 'Standard delivery within the day', 1, 1),
('urgent', 'Urgent Delivery', 'Priority delivery - faster service', 2, 2),
('scheduled', 'Scheduled Delivery', 'Deliver at specific date and time', 0, 3)
ON CONFLICT (code) DO NOTHING;

-- 7. Seed request_statuses table
INSERT INTO request_statuses (code, name, description, color_hex, icon, is_terminal, display_order) VALUES
('pending', 'Pending', 'Request is waiting for acceptance', '#FF9800', 'pending', false, 1),
('accepted', 'Accepted', 'Request has been accepted by a service provider', '#2196F3', 'check_circle', false, 2),
('in_progress', 'In Progress', 'Service is currently being performed', '#9C27B0', 'work', false, 3),
('completed', 'Completed', 'Service has been successfully completed', '#4CAF50', 'done', true, 4),
('cancelled', 'Cancelled', 'Request has been cancelled', '#F44336', 'cancel', true, 5),
('failed', 'Failed', 'Service could not be completed', '#FF5722', 'error', true, 6),
('payment_pending', 'Payment Pending', 'Waiting for payment confirmation', '#FFC107', 'payment', false, 7)
ON CONFLICT (code) DO NOTHING;

-- 8. Seed service_rates table with current hardcoded rates
INSERT INTO service_rates (service_type, rate_value, display_text, currency) VALUES
('hourly_rate_3000', 3000.00, 'TSh 3,000/hr', 'TZS'),
('hourly_rate_5000', 5000.00, 'TSh 5,000/hr', 'TZS'),
('hourly_rate_7500', 7500.00, 'TSh 7,500/hr', 'TZS'),
('hourly_rate_10000', 10000.00, 'TSh 10,000/hr', 'TZS'),
('hourly_rate_12500', 12500.00, 'TSh 12,500/hr', 'TZS'),
('hourly_rate_15000', 15000.00, 'TSh 15,000/hr', 'TZS'),
('hourly_rate_20000', 20000.00, 'TSh 20,000/hr', 'TZS'),
('hourly_rate_25000', 25000.00, 'TSh 25,000/hr', 'TZS'),
('hourly_rate_30000', 30000.00, 'TSh 30,000/hr', 'TZS'),
('hourly_rate_40000', 40000.00, 'TSh 40,000/hr', 'TZS'),
('hourly_rate_50000', 50000.00, 'TSh 50,000/hr', 'TZS'),
('delivery_base_cost', 2000.00, 'Base delivery cost', 'TZS'),
('delivery_per_km', 1000.00, 'Cost per kilometer', 'TZS')
ON CONFLICT DO NOTHING;

-- 9. Seed service_categories table
INSERT INTO service_categories (code, name, icon, description, display_order) VALUES
('delivery', 'Delivery Service', 'motorcycle', 'Send packages across the city', 1),
('buy_for_me', 'Buy For Me', 'shopping_cart', 'Get groceries and essentials delivered', 2),
('technician_service', 'Technician Service', 'build', 'Professional repair and maintenance', 3)
ON CONFLICT (code) DO NOTHING;

-- Create views for easy querying
CREATE OR REPLACE VIEW active_regions AS 
SELECT * FROM regions WHERE is_active = true ORDER BY display_order, name;

CREATE OR REPLACE VIEW active_districts AS 
SELECT d.*, r.name as region_name 
FROM districts d 
JOIN regions r ON d.region_id = r.id 
WHERE d.is_active = true AND r.is_active = true 
ORDER BY r.display_order, d.display_order, d.name;

CREATE OR REPLACE VIEW active_vehicle_types AS 
SELECT * FROM vehicle_types WHERE is_active = true ORDER BY display_order, name;

CREATE OR REPLACE VIEW active_technician_categories AS 
SELECT * FROM technician_categories WHERE is_active = true ORDER BY display_order, name;

CREATE OR REPLACE VIEW active_payment_methods AS 
SELECT * FROM payment_methods WHERE is_active = true ORDER BY display_order, name;

CREATE OR REPLACE VIEW active_delivery_priorities AS 
SELECT * FROM delivery_priorities WHERE is_active = true ORDER BY display_order, name;

CREATE OR REPLACE VIEW active_request_statuses AS 
SELECT * FROM request_statuses WHERE is_active = true ORDER BY display_order, name;

CREATE OR REPLACE VIEW active_service_rates AS 
SELECT * FROM service_rates WHERE is_active = true ORDER BY service_type, rate_value;

CREATE OR REPLACE VIEW active_service_categories AS 
SELECT * FROM service_categories WHERE is_active = true ORDER BY display_order, name;