import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UserPayload {
  action: 'create_profile' | 'update_profile' | 'toggle_availability' | 'verify_user' | 'get_profile'
  userData?: any
  userId?: string
  userType?: 'customer' | 'agent' | 'technician'
  availability?: boolean
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, userData, userId, userType, availability } = await req.json() as UserPayload

    switch (action) {
      case 'create_profile':
        return await createUserProfile(supabase, userData, userType!)
      case 'update_profile':
        return await updateUserProfile(supabase, userId!, userData, userType!)
      case 'toggle_availability':
        return await toggleAvailability(supabase, userId!, availability!, userType!)
      case 'verify_user':
        return await verifyUser(supabase, userId!, userType!)
      case 'get_profile':
        return await getUserProfile(supabase, userId!, userType!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('User management error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function createUserProfile(supabase: any, userData: any, userType: string) {
  // Create user profile
  const { data: userProfile, error: userError } = await supabase
    .from('users')
    .insert(userData)
    .select()
    .single()

  if (userError) throw userError

  // Create specific profile based on user type
  if (userType === 'agent') {
    const { data: agentProfile, error: agentError } = await supabase
      .from('agents')
      .insert({
        user_id: userProfile.id,
        is_available: true,
        is_verified: false,
        rating: 0,
        total_requests: 0,
        success_rate: 0,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (agentError) throw agentError

    return new Response(
      JSON.stringify({ success: true, user: userProfile, agent: agentProfile }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } else if (userType === 'technician') {
    const { data: technicianProfile, error: technicianError } = await supabase
      .from('technicians')
      .insert({
        user_id: userProfile.id,
        is_available: true,
        is_verified: false,
        rating: 0,
        total_requests: 0,
        success_rate: 0,
        hourly_rate: 0,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (technicianError) throw technicianError

    return new Response(
      JSON.stringify({ success: true, user: userProfile, technician: technicianProfile }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ success: true, user: userProfile }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function updateUserProfile(supabase: any, userId: string, userData: any, userType: string) {
  // Update user profile
  const { data: userProfile, error: userError } = await supabase
    .from('users')
    .update({ ...userData, updated_at: new Date().toISOString() })
    .eq('id', userId)
    .select()
    .single()

  if (userError) throw userError

  // Update specific profile based on user type
  if (userType === 'agent' && userData.agentData) {
    const { data: agentProfile, error: agentError } = await supabase
      .from('agents')
      .update({ ...userData.agentData, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .select()
      .single()

    if (agentError) throw agentError

    return new Response(
      JSON.stringify({ success: true, user: userProfile, agent: agentProfile }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } else if (userType === 'technician' && userData.technicianData) {
    const { data: technicianProfile, error: technicianError } = await supabase
      .from('technicians')
      .update({ ...userData.technicianData, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .select()
      .single()

    if (technicianError) throw technicianError

    return new Response(
      JSON.stringify({ success: true, user: userProfile, technician: technicianProfile }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ success: true, user: userProfile }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function toggleAvailability(supabase: any, userId: string, availability: boolean, userType: string) {
  let tableName = ''
  
  if (userType === 'agent') {
    tableName = 'agents'
  } else if (userType === 'technician') {
    tableName = 'technicians'
  } else {
    throw new Error('Invalid user type for availability toggle')
  }

  const { data: profile, error } = await supabase
    .from(tableName)
    .update({ 
      is_available: availability,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .select()
    .single()

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, profile }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function verifyUser(supabase: any, userId: string, userType: string) {
  let tableName = ''
  
  if (userType === 'agent') {
    tableName = 'agents'
  } else if (userType === 'technician') {
    tableName = 'technicians'
  } else {
    throw new Error('Invalid user type for verification')
  }

  const { data: profile, error } = await supabase
    .from(tableName)
    .update({ 
      is_verified: true,
      verified_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .select()
    .single()

  if (error) throw error

  // Send notification to user
  await supabase.from('notifications').insert({
    user_id: userId,
    title: 'Account Verified',
    message: `Your ${userType} account has been verified. You can now start accepting requests.`,
    type: 'account_verified',
    created_at: new Date().toISOString(),
  })

  return new Response(
    JSON.stringify({ success: true, profile }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getUserProfile(supabase: any, userId: string, userType: string) {
  // Get user profile
  const { data: userProfile, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  if (userError) throw userError

  // Get specific profile based on user type
  if (userType === 'agent') {
    const { data: agentProfile, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (agentError) throw agentError

    return new Response(
      JSON.stringify({ success: true, user: userProfile, agent: agentProfile }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } else if (userType === 'technician') {
    const { data: technicianProfile, error: technicianError } = await supabase
      .from('technicians')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (technicianError) throw technicianError

    return new Response(
      JSON.stringify({ success: true, user: userProfile, technician: technicianProfile }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ success: true, user: userProfile }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}