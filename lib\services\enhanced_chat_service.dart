import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/models/chat.dart';
import 'package:mlink/models/message.dart';
import 'package:mlink/models/user.dart';

/// Enhanced chat service with comprehensive messaging features
class EnhancedChatService {
  static final EnhancedChatService _instance = EnhancedChatService._internal();
  factory EnhancedChatService() => _instance;
  EnhancedChatService._internal();

  static EnhancedChatService get instance => _instance;

  /// Get chat overview with metrics
  Future<ChatOverview> getChatOverview(String userId) async {
    try {
      final response = await BackendApiService.getChatOverview(userId);
      
      if (response['success'] == true && response['overview'] != null) {
        return ChatOverview.fromJson(response['overview']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get chat overview');
      }
    } catch (e) {
      throw Exception('Failed to get chat overview: $e');
    }
  }

  /// Get chat analytics for insights
  Future<ChatAnalytics> getChatAnalytics(String userId, {String? timeframe}) async {
    try {
      final response = await BackendApiService.getChatAnalytics(userId, timeframe ?? 'week');
      
      if (response['success'] == true && response['analytics'] != null) {
        return ChatAnalytics.fromJson(response['analytics']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get chat analytics');
      }
    } catch (e) {
      throw Exception('Failed to get chat analytics: $e');
    }
  }

  /// Get recent conversations with context
  Future<List<ConversationSummary>> getRecentConversations(String userId, {int? limit}) async {
    try {
      final response = await BackendApiService.getRecentConversations(userId, limit ?? 10);
      
      if (response['success'] == true && response['conversations'] != null) {
        final conversationsData = response['conversations'] as List;
        return conversationsData.map((item) => ConversationSummary.fromJson(item)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to get recent conversations');
      }
    } catch (e) {
      throw Exception('Failed to get recent conversations: $e');
    }
  }

  /// Search messages across all chats
  Future<List<MessageSearchResult>> searchMessages(String userId, String query) async {
    try {
      final response = await BackendApiService.searchMessages(userId, query);
      
      if (response['success'] == true && response['results'] != null) {
        final resultsData = response['results'] as List;
        return resultsData.map((item) => MessageSearchResult.fromJson(item)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to search messages');
      }
    } catch (e) {
      throw Exception('Failed to search messages: $e');
    }
  }

  /// Get message statistics
  Future<MessageStats> getMessageStats(String userId, {String? chatId}) async {
    try {
      final response = await BackendApiService.getMessageStats(userId, chatId);
      
      if (response['success'] == true && response['stats'] != null) {
        return MessageStats.fromJson(response['stats']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get message stats');
      }
    } catch (e) {
      throw Exception('Failed to get message stats: $e');
    }
  }

  /// Get suggested quick replies based on context
  Future<List<String>> getSuggestedReplies(String chatId, String? lastMessage) async {
    try {
      // For now, provide static suggestions. Could be enhanced with AI in the future
      if (lastMessage == null || lastMessage.isEmpty) {
        return [
          'Hello! How can I help you?',
          'Hi there!',
          'Good to hear from you!',
        ];
      }

      final lowerMessage = lastMessage.toLowerCase();
      
      if (lowerMessage.contains('thanks') || lowerMessage.contains('thank you')) {
        return [
          'You\'re welcome!',
          'Happy to help!',
          'Anytime!',
        ];
      } else if (lowerMessage.contains('how much') || lowerMessage.contains('cost') || lowerMessage.contains('price')) {
        return [
          'Let me check the pricing for you',
          'I\'ll send you a quote shortly',
          'The cost depends on the details',
        ];
      } else if (lowerMessage.contains('when') || lowerMessage.contains('time')) {
        return [
          'I can be there soon',
          'What time works for you?',
          'I\'m available now',
        ];
      } else if (lowerMessage.contains('location') || lowerMessage.contains('where')) {
        return [
          'What\'s the address?',
          'I can work in most areas',
          'Send me your location',
        ];
      } else {
        return [
          'Got it!',
          'Understood',
          'I\'ll take care of that',
        ];
      }
    } catch (e) {
      return ['Thanks for your message!'];
    }
  }

  /// Generate conversation insights
  Future<List<ConversationInsight>> getConversationInsights(String userId) async {
    try {
      final analytics = await getChatAnalytics(userId);
      final insights = <ConversationInsight>[];

      // Response time insight
      if (analytics.averageResponseTime > 0) {
        String responseCategory;
        if (analytics.averageResponseTime <= 5) {
          responseCategory = 'excellent';
        } else if (analytics.averageResponseTime <= 15) {
          responseCategory = 'good';
        } else if (analytics.averageResponseTime <= 60) {
          responseCategory = 'average';
        } else {
          responseCategory = 'needs_improvement';
        }
        
        insights.add(ConversationInsight(
          type: 'response_time',
          title: 'Response Time',
          message: _getResponseTimeMessage(responseCategory, analytics.averageResponseTime),
          priority: responseCategory == 'needs_improvement' ? InsightPriority.high : InsightPriority.medium,
          actionable: responseCategory == 'needs_improvement' 
              ? 'Try to respond to messages within 15 minutes for better customer satisfaction'
              : null,
        ));
      }

      // Activity insight
      if (analytics.totalMessages > 0) {
        final avgMessagesPerDay = analytics.totalMessages / 7.0; // Assuming week timeframe
        insights.add(ConversationInsight(
          type: 'activity',
          title: 'Message Activity',
          message: 'You\'ve exchanged ${analytics.totalMessages} messages this week (${avgMessagesPerDay.toInt()} per day)',
          priority: InsightPriority.low,
        ));
      }

      // Customer satisfaction insight
      if (analytics.responseRate > 0) {
        String satisfactionLevel;
        if (analytics.responseRate >= 90) {
          satisfactionLevel = 'excellent';
        } else if (analytics.responseRate >= 75) {
          satisfactionLevel = 'good';
        } else {
          satisfactionLevel = 'needs_improvement';
        }

        insights.add(ConversationInsight(
          type: 'satisfaction',
          title: 'Response Rate',
          message: '${analytics.responseRate.toInt()}% of your messages get responses - ${satisfactionLevel} engagement!',
          priority: satisfactionLevel == 'needs_improvement' ? InsightPriority.high : InsightPriority.low,
        ));
      }

      return insights;
    } catch (e) {
      return [];
    }
  }

  String _getResponseTimeMessage(String category, double responseTime) {
    switch (category) {
      case 'excellent':
        return 'Your average response time is ${responseTime.toInt()} minutes - excellent!';
      case 'good':
        return 'Your average response time is ${responseTime.toInt()} minutes - keep it up!';
      case 'average':
        return 'Your average response time is ${responseTime.toInt()} minutes - room for improvement';
      case 'needs_improvement':
        return 'Your average response time is ${responseTime.toInt()} minutes - try to respond faster';
      default:
        return 'Response time: ${responseTime.toInt()} minutes';
    }
  }
}

/// Chat overview model
class ChatOverview {
  final int totalChats;
  final int activeChats;
  final int unreadMessages;
  final double averageResponseTime;
  final int messagesThisWeek;
  final List<Chat> recentChats;
  final Map<String, int> chatsByType;
  final Map<String, int> messagesByDay;

  ChatOverview({
    required this.totalChats,
    required this.activeChats,
    required this.unreadMessages,
    required this.averageResponseTime,
    required this.messagesThisWeek,
    required this.recentChats,
    required this.chatsByType,
    required this.messagesByDay,
  });

  factory ChatOverview.fromJson(Map<String, dynamic> json) {
    return ChatOverview(
      totalChats: json['total_chats'] ?? 0,
      activeChats: json['active_chats'] ?? 0,
      unreadMessages: json['unread_messages'] ?? 0,
      averageResponseTime: (json['average_response_time'] ?? 0.0).toDouble(),
      messagesThisWeek: json['messages_this_week'] ?? 0,
      recentChats: (json['recent_chats'] as List? ?? [])
          .map((item) => Chat.fromJson(item)).toList(),
      chatsByType: Map<String, int>.from(json['chats_by_type'] ?? {}),
      messagesByDay: Map<String, int>.from(json['messages_by_day'] ?? {}),
    );
  }
}

/// Chat analytics model
class ChatAnalytics {
  final String timeframe;
  final int totalMessages;
  final int totalChats;
  final double averageResponseTime;
  final double responseRate;
  final Map<String, int> messagesByType;
  final Map<String, int> messagesByStatus;
  final List<String> topContacts;
  final Map<String, double> activityHours;

  ChatAnalytics({
    required this.timeframe,
    required this.totalMessages,
    required this.totalChats,
    required this.averageResponseTime,
    required this.responseRate,
    required this.messagesByType,
    required this.messagesByStatus,
    required this.topContacts,
    required this.activityHours,
  });

  factory ChatAnalytics.fromJson(Map<String, dynamic> json) {
    return ChatAnalytics(
      timeframe: json['timeframe'] ?? '',
      totalMessages: json['total_messages'] ?? 0,
      totalChats: json['total_chats'] ?? 0,
      averageResponseTime: (json['average_response_time'] ?? 0.0).toDouble(),
      responseRate: (json['response_rate'] ?? 0.0).toDouble(),
      messagesByType: Map<String, int>.from(json['messages_by_type'] ?? {}),
      messagesByStatus: Map<String, int>.from(json['messages_by_status'] ?? {}),
      topContacts: List<String>.from(json['top_contacts'] ?? []),
      activityHours: Map<String, double>.from(json['activity_hours'] ?? {}),
    );
  }
}

/// Conversation summary model
class ConversationSummary {
  final Chat chat;
  final int totalMessages;
  final DateTime lastActivity;
  final String? lastMessagePreview;
  final bool hasUnread;
  final String? contextType;
  final Map<String, dynamic>? metadata;

  ConversationSummary({
    required this.chat,
    required this.totalMessages,
    required this.lastActivity,
    this.lastMessagePreview,
    required this.hasUnread,
    this.contextType,
    this.metadata,
  });

  factory ConversationSummary.fromJson(Map<String, dynamic> json) {
    return ConversationSummary(
      chat: Chat.fromJson(json['chat']),
      totalMessages: json['total_messages'] ?? 0,
      lastActivity: DateTime.parse(json['last_activity']),
      lastMessagePreview: json['last_message_preview'],
      hasUnread: json['has_unread'] ?? false,
      contextType: json['context_type'],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Message search result model
class MessageSearchResult {
  final Message message;
  final Chat chat;
  final String highlightedContent;
  final double relevanceScore;

  MessageSearchResult({
    required this.message,
    required this.chat,
    required this.highlightedContent,
    required this.relevanceScore,
  });

  factory MessageSearchResult.fromJson(Map<String, dynamic> json) {
    return MessageSearchResult(
      message: Message.fromJson(json['message']),
      chat: Chat.fromJson(json['chat']),
      highlightedContent: json['highlighted_content'] ?? '',
      relevanceScore: (json['relevance_score'] ?? 0.0).toDouble(),
    );
  }
}

/// Message statistics model
class MessageStats {
  final String period;
  final int totalSent;
  final int totalReceived;
  final int totalRead;
  final double readRate;
  final Map<String, int> messagesByHour;
  final Map<MessageType, int> messagesByType;
  final double averageLength;

  MessageStats({
    required this.period,
    required this.totalSent,
    required this.totalReceived,
    required this.totalRead,
    required this.readRate,
    required this.messagesByHour,
    required this.messagesByType,
    required this.averageLength,
  });

  factory MessageStats.fromJson(Map<String, dynamic> json) {
    final messagesByTypeMap = <MessageType, int>{};
    if (json['messages_by_type'] != null) {
      (json['messages_by_type'] as Map<String, dynamic>).forEach((key, value) {
        try {
          final messageType = MessageType.values.firstWhere((e) => e.name == key);
          messagesByTypeMap[messageType] = value as int;
        } catch (e) {
          // Skip unknown message types
        }
      });
    }

    return MessageStats(
      period: json['period'] ?? '',
      totalSent: json['total_sent'] ?? 0,
      totalReceived: json['total_received'] ?? 0,
      totalRead: json['total_read'] ?? 0,
      readRate: (json['read_rate'] ?? 0.0).toDouble(),
      messagesByHour: Map<String, int>.from(json['messages_by_hour'] ?? {}),
      messagesByType: messagesByTypeMap,
      averageLength: (json['average_length'] ?? 0.0).toDouble(),
    );
  }
}

/// Conversation insight model
class ConversationInsight {
  final String type;
  final String title;
  final String message;
  final InsightPriority priority;
  final String? actionable;

  ConversationInsight({
    required this.type,
    required this.title,
    required this.message,
    required this.priority,
    this.actionable,
  });
}

/// Insight priority enum (if not already defined elsewhere)
enum InsightPriority {
  low,
  medium,
  high,
  critical,
}