import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mlink/models/request.dart';

class MapsService {
  static final MapsService _instance = MapsService._internal();
  factory MapsService() => _instance;
  MapsService._internal();

  static MapsService get instance => _instance;

  GoogleMapController? _controller;
  StreamSubscription<Position>? _positionStream;
  final StreamController<Position> _locationController = StreamController<Position>.broadcast();

  Stream<Position> get locationStream => _locationController.stream;

  /// Initialize the maps service
  Future<void> initialize() async {
    await _checkPermissions();
  }

  /// Set the Google Maps controller
  void setController(GoogleMapController controller) {
    _controller = controller;
  }

  /// Check and request location permissions
  Future<bool> _checkPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        if (kDebugMode) {
          print('Location permissions are denied');
        }
        return false;
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      if (kDebugMode) {
        print('Location permissions are permanently denied');
      }
      return false;
    }
    
    return true;
  }

  /// Get current position
  Future<Position?> getCurrentPosition() async {
    try {
      final hasPermission = await _checkPermissions();
      if (!hasPermission) return null;

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current position: $e');
      }
      return null;
    }
  }

  /// Get current location as LatLng
  Future<LatLng?> getCurrentLatLng() async {
    final position = await getCurrentPosition();
    if (position != null) {
      return LatLng(position.latitude, position.longitude);
    }
    return null;
  }

  /// Get current location as LocationPoint
  Future<LocationPoint?> getCurrentLocationPoint() async {
    final position = await getCurrentPosition();
    if (position != null) {
      final address = await getAddressFromCoordinates(
        position.latitude, 
        position.longitude,
      );
      return LocationPoint(
        latitude: position.latitude,
        longitude: position.longitude,
        address: address,
      );
    }
    return null;
  }

  /// Start location tracking
  Future<void> startLocationTracking() async {
    final hasPermission = await _checkPermissions();
    if (!hasPermission) return;

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update every 10 meters
    );

    _positionStream = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen(
      (Position position) {
        _locationController.add(position);
      },
      onError: (error) {
        if (kDebugMode) {
          print('Location tracking error: $error');
        }
      },
    );
  }

  /// Stop location tracking
  void stopLocationTracking() {
    _positionStream?.cancel();
    _positionStream = null;
  }

  /// Move camera to specific location
  Future<void> moveCamera(LatLng location, {double zoom = 15.0}) async {
    if (_controller != null) {
      await _controller!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: location,
            zoom: zoom,
          ),
        ),
      );
    }
  }

  /// Move camera to current location
  Future<void> moveToCurrent({double zoom = 15.0}) async {
    final position = await getCurrentLatLng();
    if (position != null) {
      await moveCamera(position, zoom: zoom);
    }
  }

  /// Get address from coordinates (reverse geocoding)
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return '${placemark.street ?? ''}, ${placemark.locality ?? ''}, ${placemark.country ?? ''}'.trim().replaceAll(RegExp(r'^,\s*|,\s*$'), '');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting address from coordinates: $e');
      }
    }
    return null;
  }

  /// Get coordinates from address (geocoding)
  Future<LatLng?> getCoordinatesFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return LatLng(location.latitude, location.longitude);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting coordinates from address: $e');
      }
    }
    return null;
  }

  /// Calculate distance between two points in kilometers
  double calculateDistance(LatLng start, LatLng end) {
    return Geolocator.distanceBetween(
      start.latitude,
      start.longitude,
      end.latitude,
      end.longitude,
    ) / 1000; // Convert meters to kilometers
  }

  /// Calculate estimated time based on distance and average speed
  Duration calculateEstimatedTime(LatLng start, LatLng end, {double averageSpeedKmh = 30}) {
    final distance = calculateDistance(start, end);
    final timeInHours = distance / averageSpeedKmh;
    final timeInMinutes = (timeInHours * 60).round();
    return Duration(minutes: timeInMinutes);
  }

  /// Get delivery route points (simplified - you might want to use Directions API for real routing)
  Future<List<LatLng>> getRoutePoints(LatLng start, LatLng end) async {
    // This is a simple straight line route
    // In production, you would use Google Directions API or similar service
    return [start, end];
  }

  /// Create marker for location
  Marker createLocationMarker({
    required String markerId,
    required LatLng position,
    required String title,
    String? snippet,
    BitmapDescriptor? icon,
    VoidCallback? onTap,
  }) {
    return Marker(
      markerId: MarkerId(markerId),
      position: position,
      infoWindow: InfoWindow(
        title: title,
        snippet: snippet,
      ),
      icon: icon ?? BitmapDescriptor.defaultMarker,
      onTap: onTap,
    );
  }

  /// Create pickup marker
  Marker createPickupMarker(LocationPoint location, {VoidCallback? onTap}) {
    return createLocationMarker(
      markerId: 'pickup',
      position: LatLng(location.latitude, location.longitude),
      title: 'Pickup Location',
      snippet: location.address ?? 'Pickup point',
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      onTap: onTap,
    );
  }

  /// Create dropoff marker
  Marker createDropoffMarker(LocationPoint location, {VoidCallback? onTap}) {
    return createLocationMarker(
      markerId: 'dropoff',
      position: LatLng(location.latitude, location.longitude),
      title: 'Dropoff Location',
      snippet: location.address ?? 'Dropoff point',
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      onTap: onTap,
    );
  }

  /// Create agent/technician marker
  Marker createAgentMarker({
    required String agentId,
    required LatLng position,
    required String name,
    VoidCallback? onTap,
  }) {
    return createLocationMarker(
      markerId: agentId,
      position: position,
      title: name,
      snippet: 'Agent',
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
      onTap: onTap,
    );
  }

  /// Create polyline between two points
  Polyline createRoutePolyline({
    required String polylineId,
    required List<LatLng> points,
    Color color = const Color(0xFF2196F3),
    double width = 5,
  }) {
    return Polyline(
      polylineId: PolylineId(polylineId),
      points: points,
      color: color,
      width: width.round(),
      patterns: [],
    );
  }

  /// Get bounds for multiple locations
  LatLngBounds getBoundsFromLocations(List<LatLng> locations) {
    if (locations.isEmpty) {
      throw ArgumentError('Locations list cannot be empty');
    }

    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (final location in locations) {
      minLat = min(minLat, location.latitude);
      maxLat = max(maxLat, location.latitude);
      minLng = min(minLng, location.longitude);
      maxLng = max(maxLng, location.longitude);
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  /// Fit camera to show all locations
  Future<void> fitToLocations(List<LatLng> locations, {double padding = 100}) async {
    if (_controller == null || locations.isEmpty) return;

    final bounds = getBoundsFromLocations(locations);
    await _controller!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, padding),
    );
  }

  /// Clean up resources
  void dispose() {
    stopLocationTracking();
    _locationController.close();
    _controller = null;
  }
}