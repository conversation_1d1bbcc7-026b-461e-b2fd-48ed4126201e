# Supabase Authentication Setup Instructions

## 1. Environment Variables Setup

### Update .env file
Replace the placeholder values in `.env` with your actual Supabase credentials:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here

# Google Sign-In Configuration (optional)
GOOGLE_CLIENT_ID_ANDROID=your-android-client-id
GOOGLE_CLIENT_ID_IOS=your-ios-client-id
```

### Where to find your Supabase credentials:
1. Go to [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Go to Settings → API
4. Copy the "Project URL" and "anon public" key

## 2. Database Setup

### Create Users Table
Run this SQL in your Supabase SQL editor:

```sql
-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT auth.uid(),
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT CHECK (role IN ('customer', 'agent', 'technician', 'admin')) DEFAULT 'customer',
    status TEXT CHECK (status IN ('active', 'inactive', 'suspended')) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read their own data" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own data" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (id, email, full_name, role)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'), 'customer');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

## 3. Google Sign-In Setup (Optional)

### Supabase Configuration
1. Go to Authentication → Providers in your Supabase dashboard
2. Enable Google provider
3. Add your Google OAuth credentials:
   - Client ID
   - Client Secret

### Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google Sign-In API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://your-project-id.supabase.co/auth/v1/callback`
   - `io.supabase.flutter://auth-callback`

### Android Setup
1. Add SHA-1 fingerprint to Google Cloud Console
2. Download `google-services.json` and place in `android/app/`
3. Update `android/build.gradle` and `android/app/build.gradle` if needed

### iOS Setup
1. Add iOS bundle ID to Google Cloud Console
2. Download `GoogleService-Info.plist` and add to iOS project
3. Update URL schemes in `ios/Runner/Info.plist` (already done)

## 4. Testing Authentication

### Email/Password Authentication
1. Test user registration with different roles
2. Test login with valid credentials
3. Test login with invalid credentials
4. Test logout functionality

### Google Sign-In
1. Test Google Sign-In flow
2. Verify user profile creation
3. Test deep link redirect

## 5. Security Considerations

### Row Level Security (RLS)
- All tables should have RLS enabled
- Users can only access their own data
- Admin users might need special policies

### Environment Variables
- Never commit `.env` file to version control
- Use different environments for development/production
- Keep credentials secure

## 6. Troubleshooting

### Common Issues

1. **Authentication flickering**: 
   - Ensure proper stream management in AuthService
   - Check if auth state listeners are properly implemented

2. **Google Sign-In not working**:
   - Verify Google OAuth credentials in Supabase
   - Check redirect URIs configuration
   - Ensure proper platform-specific setup

3. **Deep links not working**:
   - Verify URL schemes in platform configurations
   - Check if proper intent filters are added (Android)
   - Ensure bundle identifiers match (iOS)

4. **Database errors**:
   - Check if RLS policies are properly set
   - Verify table permissions
   - Ensure triggers are working correctly

### Debug Steps
1. Check Flutter logs for authentication errors
2. Monitor Supabase logs for backend issues
3. Test API calls directly using Supabase client
4. Verify network connectivity

## 7. Next Steps

After setting up authentication:
1. Test all authentication flows thoroughly
2. Implement proper error handling
3. Add password reset functionality
4. Set up user profile management
5. Implement role-based access control
6. Add security monitoring and logging

## 8. Important Notes

- The `.env` file contains sensitive information and should not be committed to version control
- Make sure to update the Supabase URL and keys with your actual project credentials
- Test authentication on both Android and iOS devices
- Consider implementing additional security measures like email verification
- Monitor authentication metrics in Supabase dashboard

---

For any issues or questions, refer to the [Supabase Documentation](https://supabase.com/docs) or [Flutter Supabase Package Documentation](https://pub.dev/packages/supabase_flutter).