import 'package:flutter/material.dart';
import 'package:mlink/models/admin/post.dart';
import 'package:mlink/services/admin/admin_service.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:intl/intl.dart';
import 'package:data_table_2/data_table_2.dart';

class PostManager extends StatefulWidget {
  const PostManager({super.key});

  @override
  State<PostManager> createState() => _PostManagerState();
}

class _PostManagerState extends State<PostManager> {
  List<Post> posts = [];
  bool isLoading = true;
  PostRole? filterRole;
  PostStatus? filterStatus;
  final dateFormatter = DateFormat('MMM dd, yyyy');

  @override
  void initState() {
    super.initState();
    _loadPosts();
  }

  Future<void> _loadPosts() async {
    try {
      final data = await AdminService.instance.getAllPosts();
      setState(() {
        posts = data;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading posts: $e');
      setState(() => isLoading = false);
    }
  }

  List<Post> get filteredPosts {
    return posts.where((post) {
      if (filterRole != null && post.targetRole != filterRole) {
        return false;
      }
      if (filterStatus != null && post.status != filterStatus) {
        return false;
      }
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Post Manager',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          _buildFilterChips(),
          const SizedBox(width: 16),
          ElevatedButton.icon(
            onPressed: () => _showCreatePostDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Create Post'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Filters
                _buildFilters(),
                
                // Posts Table
                Expanded(
                  child: _buildPostsTable(),
                ),
              ],
            ),
    );
  }

  Widget _buildFilterChips() {
    return Row(
      children: [
        if (filterRole != null || filterStatus != null)
          IconButton(
            onPressed: () => setState(() {
              filterRole = null;
              filterStatus = null;
            }),
            icon: const Icon(Icons.clear),
            tooltip: 'Clear filters',
          ),
      ],
    );
  }

  Widget _buildFilters() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Role Filter
          DropdownButton<PostRole?>(
            value: filterRole,
            hint: const Text('Filter by Role'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Roles')),
              ...PostRole.values.map((role) => DropdownMenuItem(
                value: role,
                child: Text(role.name.toUpperCase()),
              )),
            ],
            onChanged: (value) => setState(() => filterRole = value),
          ),
          
          const SizedBox(width: 16),
          
          // Status Filter
          DropdownButton<PostStatus?>(
            value: filterStatus,
            hint: const Text('Filter by Status'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Statuses')),
              ...PostStatus.values.map((status) => DropdownMenuItem(
                value: status,
                child: Text(status.name.toUpperCase()),
              )),
            ],
            onChanged: (value) => setState(() => filterStatus = value),
          ),
          
          const Spacer(),
          
          Text(
            '${filteredPosts.length} posts',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsTable() {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: DataTable2(
        columnSpacing: 12,
        horizontalMargin: 12,
        minWidth: 800,
        columns: const [
          DataColumn2(
            label: Text('Title'),
            size: ColumnSize.L,
          ),
          DataColumn2(
            label: Text('Role'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Status'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Date Range'),
            size: ColumnSize.M,
          ),
          DataColumn2(
            label: Text('Created'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Actions'),
            size: ColumnSize.S,
          ),
        ],
        rows: filteredPosts.map((post) => DataRow2(
          cells: [
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    post.title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (post.body.isNotEmpty)
                    Text(
                      post.body,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            DataCell(
              Chip(
                label: Text(
                  post.targetRole.name.toUpperCase(),
                  style: theme.textTheme.bodySmall,
                ),
                backgroundColor: _getRoleColor(post.targetRole).withValues(alpha: 0.1),
                side: BorderSide.none,
              ),
            ),
            DataCell(
              Chip(
                label: Text(
                  post.status.name.toUpperCase(),
                  style: theme.textTheme.bodySmall,
                ),
                backgroundColor: _getStatusColor(post.status).withValues(alpha: 0.1),
                side: BorderSide.none,
              ),
            ),
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (post.startDate != null)
                    Text(
                      'From: ${dateFormatter.format(post.startDate!)}',
                      style: theme.textTheme.bodySmall,
                    ),
                  if (post.endDate != null)
                    Text(
                      'To: ${dateFormatter.format(post.endDate!)}',
                      style: theme.textTheme.bodySmall,
                    ),
                  if (post.startDate == null && post.endDate == null)
                    Text(
                      'Always active',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                ],
              ),
            ),
            DataCell(
              Text(
                dateFormatter.format(post.createdAt),
                style: theme.textTheme.bodySmall,
              ),
            ),
            DataCell(
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _showEditPostDialog(post),
                    icon: const Icon(Icons.edit, size: 18),
                    color: theme.colorScheme.primary,
                  ),
                  IconButton(
                    onPressed: () => _deletePost(post),
                    icon: const Icon(Icons.delete, size: 18),
                    color: Colors.red,
                  ),
                ],
              ),
            ),
          ],
        )).toList(),
      ),
    );
  }

  Color _getRoleColor(PostRole role) {
    switch (role) {
      case PostRole.all:
        return Colors.blue;
      case PostRole.customer:
        return Colors.green;
      case PostRole.agent:
        return Colors.orange;
      case PostRole.technician:
        return Colors.purple;
    }
  }

  Color _getStatusColor(PostStatus status) {
    switch (status) {
      case PostStatus.draft:
        return Colors.grey;
      case PostStatus.published:
        return Colors.green;
      case PostStatus.archived:
        return Colors.orange;
    }
  }

  void _showCreatePostDialog() {
    _showPostDialog(null);
  }

  void _showEditPostDialog(Post post) {
    _showPostDialog(post);
  }

  void _showPostDialog(Post? post) {
    final isEdit = post != null;
    final titleController = TextEditingController(text: post?.title ?? '');
    final bodyController = TextEditingController(text: post?.body ?? '');
    final imageController = TextEditingController(text: post?.imageUrl ?? '');
    PostRole selectedRole = post?.targetRole ?? PostRole.all;
    PostStatus selectedStatus = post?.status ?? PostStatus.draft;
    DateTime? startDate = post?.startDate;
    DateTime? endDate = post?.endDate;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(isEdit ? 'Edit Post' : 'Create New Post'),
          content: SingleChildScrollView(
            child: SizedBox(
              width: 500,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title *',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: bodyController,
                    decoration: const InputDecoration(
                      labelText: 'Body *',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 4,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: imageController,
                    decoration: const InputDecoration(
                      labelText: 'Image URL (optional)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<PostRole>(
                          value: selectedRole,
                          decoration: const InputDecoration(
                            labelText: 'Target Role',
                            border: OutlineInputBorder(),
                          ),
                          items: PostRole.values.map((role) => DropdownMenuItem(
                            value: role,
                            child: Text(role.name.toUpperCase()),
                          )).toList(),
                          onChanged: (value) => setDialogState(() => selectedRole = value!),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<PostStatus>(
                          value: selectedStatus,
                          decoration: const InputDecoration(
                            labelText: 'Status',
                            border: OutlineInputBorder(),
                          ),
                          items: PostStatus.values.map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.name.toUpperCase()),
                          )).toList(),
                          onChanged: (value) => setDialogState(() => selectedStatus = value!),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('Start Date (optional)'),
                            const SizedBox(height: 8),
                            OutlinedButton.icon(
                              onPressed: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: startDate ?? DateTime.now(),
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime.now().add(const Duration(days: 365)),
                                );
                                if (date != null) {
                                  setDialogState(() => startDate = date);
                                }
                              },
                              icon: const Icon(Icons.calendar_today),
                              label: Text(startDate != null 
                                  ? dateFormatter.format(startDate!) 
                                  : 'Select Date'),
                            ),
                            if (startDate != null)
                              TextButton(
                                onPressed: () => setDialogState(() => startDate = null),
                                child: const Text('Clear'),
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('End Date (optional)'),
                            const SizedBox(height: 8),
                            OutlinedButton.icon(
                              onPressed: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: endDate ?? DateTime.now().add(const Duration(days: 7)),
                                  firstDate: startDate ?? DateTime.now(),
                                  lastDate: DateTime.now().add(const Duration(days: 365)),
                                );
                                if (date != null) {
                                  setDialogState(() => endDate = date);
                                }
                              },
                              icon: const Icon(Icons.calendar_today),
                              label: Text(endDate != null 
                                  ? dateFormatter.format(endDate!) 
                                  : 'Select Date'),
                            ),
                            if (endDate != null)
                              TextButton(
                                onPressed: () => setDialogState(() => endDate = null),
                                child: const Text('Clear'),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => _savePost(
                post,
                titleController.text,
                bodyController.text,
                imageController.text.isEmpty ? null : imageController.text,
                selectedRole,
                selectedStatus,
                startDate,
                endDate,
              ),
              child: Text(isEdit ? 'Update' : 'Create'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _savePost(
    Post? existingPost,
    String title,
    String body,
    String? imageUrl,
    PostRole role,
    PostStatus status,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    if (title.isEmpty || body.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Title and body are required')),
      );
      return;
    }

    try {
      final now = DateTime.now();
      final currentUser = AuthService.instance.currentUser!;
      
      if (existingPost != null) {
        // Update existing post
        final updatedPost = existingPost.copyWith(
          title: title,
          body: body,
          imageUrl: imageUrl,
          targetRole: role,
          status: status,
          startDate: startDate,
          endDate: endDate,
          updatedAt: now,
        );
        await AdminService.instance.updatePost(updatedPost);
      } else {
        // Create new post
        final newPost = Post(
          id: '', // Will be generated
          title: title,
          body: body,
          imageUrl: imageUrl,
          targetRole: role,
          status: status,
          startDate: startDate,
          endDate: endDate,
          createdAt: now,
          updatedAt: now,
          createdBy: currentUser.id,
        );
        await AdminService.instance.createPost(newPost);
      }

      Navigator.of(context).pop();
      _loadPosts();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Post ${existingPost != null ? 'updated' : 'created'} successfully'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> _deletePost(Post post) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Post'),
        content: Text('Are you sure you want to delete "${post.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await AdminService.instance.deletePost(post.id);
        _loadPosts();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Post deleted successfully')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting post: $e')),
        );
      }
    }
  }
}