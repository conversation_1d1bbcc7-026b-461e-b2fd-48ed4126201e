import 'package:flutter/material.dart';

class AppLogo extends StatelessWidget {
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? margin;
  final BoxFit fit;

  const AppLogo({
    super.key,
    this.width,
    this.height,
    this.margin,
    this.fit = BoxFit.contain,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Image.asset(
        'assets/images/logo_1.png',
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          // Fallback to text logo if image fails to load
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF8B5CF6), // Purple
                  Color(0xFF3B82F6), // Blue
                ],
              ),
              borderRadius: BorderRadius.circular((width ?? 60) / 2),
            ),
            child: Center(
              child: Text(
                'M-Link',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: (width ?? 60) * 0.2,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Predefined logo sizes for consistency
class AppLogoSizes {
  static const double small = 40;
  static const double medium = 60;
  static const double large = 80;
  static const double extraLarge = 120;
}