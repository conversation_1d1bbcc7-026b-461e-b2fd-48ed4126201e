
enum TechnicianCategory {
  electrician,
  plumber,
  mechanic,
  carpenter,
  painter,
  cleaner,
  gardener,
  applianceRepair,
  computerRepair,
  phoneRepair,
  hvac,
  other
}

enum TechnicianStatus { active, inactive, suspended, pendingVerification }

class Skill {
  final String id;
  final String name;
  final TechnicianCategory category;
  final String? description;
  final double hourlyRate;
  final double? fixedRate;
  final DateTime createdAt;

  Skill({
    required this.id,
    required this.name,
    required this.category,
    this.description,
    required this.hourlyRate,
    this.fixedRate,
    required this.createdAt,
  });

  factory Skill.fromJson(Map<String, dynamic> json) {
    return Skill(
      id: json['id'],
      name: json['name'],
      category: TechnicianCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => TechnicianCategory.other,
      ),
      description: json['description'],
      hourlyRate: (json['hourly_rate'] as num).toDouble(),
      fixedRate: (json['fixed_rate'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category.name,
      'description': description,
      'hourly_rate': hourlyRate,
      'fixed_rate': fixedRate,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Portfolio {
  final String id;
  final String title;
  final String? description;
  final List<String> imageUrls;
  final TechnicianCategory category;
  final DateTime createdAt;

  Portfolio({
    required this.id,
    required this.title,
    this.description,
    this.imageUrls = const [],
    required this.category,
    required this.createdAt,
  });

  factory Portfolio.fromJson(Map<String, dynamic> json) {
    return Portfolio(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      imageUrls: (json['image_urls'] as List<dynamic>?)
          ?.map((url) => url.toString())
          .toList() ?? [],
      category: TechnicianCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => TechnicianCategory.other,
      ),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_urls': imageUrls,
      'category': category.name,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Technician {
  final String id;
  final String userId;
  final String? nidaNumber;
  final bool isNidaVerified;
  final TechnicianStatus status;
  final List<Skill> skills;
  final List<Portfolio> portfolio;
  final double rating;
  final int totalJobs;
  final double earnings;
  final double availableBalance;
  final bool isAvailable;
  final DateTime? lastActiveAt;
  final Map<String, dynamic>? location;
  final String? bio;
  final int experienceYears;
  final List<String> certifications;
  final DateTime createdAt;
  final DateTime updatedAt;

  Technician({
    required this.id,
    required this.userId,
    this.nidaNumber,
    this.isNidaVerified = false,
    this.status = TechnicianStatus.pendingVerification,
    this.skills = const [],
    this.portfolio = const [],
    this.rating = 0.0,
    this.totalJobs = 0,
    this.earnings = 0.0,
    this.availableBalance = 0.0,
    this.isAvailable = false,
    this.lastActiveAt,
    this.location,
    this.bio,
    this.experienceYears = 0,
    this.certifications = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory Technician.fromJson(Map<String, dynamic> json) {
    return Technician(
      id: json['id'],
      userId: json['user_id'],
      nidaNumber: json['nida_number'],
      isNidaVerified: json['is_nida_verified'] ?? false,
      status: TechnicianStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TechnicianStatus.pendingVerification,
      ),
      skills: (json['skills'] as List<dynamic>?)
          ?.map((s) => Skill.fromJson(s))
          .toList() ?? [],
      portfolio: (json['portfolio'] as List<dynamic>?)
          ?.map((p) => Portfolio.fromJson(p))
          .toList() ?? [],
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalJobs: json['total_jobs'] ?? 0,
      earnings: (json['earnings'] as num?)?.toDouble() ?? 0.0,
      availableBalance: (json['available_balance'] as num?)?.toDouble() ?? 0.0,
      isAvailable: json['is_available'] ?? false,
      lastActiveAt: json['last_active_at'] != null
          ? DateTime.parse(json['last_active_at'])
          : null,
      location: json['location'] as Map<String, dynamic>?,
      bio: json['bio'],
      experienceYears: json['experience_years'] ?? 0,
      certifications: (json['certifications'] as List<dynamic>?)
          ?.map((c) => c.toString())
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'nida_number': nidaNumber,
      'is_nida_verified': isNidaVerified,
      'status': status.name,
      'skills': skills.map((s) => s.toJson()).toList(),
      'portfolio': portfolio.map((p) => p.toJson()).toList(),
      'rating': rating,
      'total_jobs': totalJobs,
      'earnings': earnings,
      'available_balance': availableBalance,
      'is_available': isAvailable,
      'last_active_at': lastActiveAt?.toIso8601String(),
      'location': location,
      'bio': bio,
      'experience_years': experienceYears,
      'certifications': certifications,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Technician copyWith({
    String? id,
    String? userId,
    String? nidaNumber,
    bool? isNidaVerified,
    TechnicianStatus? status,
    List<Skill>? skills,
    List<Portfolio>? portfolio,
    double? rating,
    int? totalJobs,
    double? earnings,
    double? availableBalance,
    bool? isAvailable,
    DateTime? lastActiveAt,
    Map<String, dynamic>? location,
    String? bio,
    int? experienceYears,
    List<String>? certifications,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Technician(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      nidaNumber: nidaNumber ?? this.nidaNumber,
      isNidaVerified: isNidaVerified ?? this.isNidaVerified,
      status: status ?? this.status,
      skills: skills ?? this.skills,
      portfolio: portfolio ?? this.portfolio,
      rating: rating ?? this.rating,
      totalJobs: totalJobs ?? this.totalJobs,
      earnings: earnings ?? this.earnings,
      availableBalance: availableBalance ?? this.availableBalance,
      isAvailable: isAvailable ?? this.isAvailable,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      location: location ?? this.location,
      bio: bio ?? this.bio,
      experienceYears: experienceYears ?? this.experienceYears,
      certifications: certifications ?? this.certifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Technician && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Technician{id: $id, userId: $userId, status: $status, rating: $rating, isAvailable: $isAvailable}';
  }
}