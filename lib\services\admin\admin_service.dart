import 'package:mlink/models/admin/post.dart';
import 'package:mlink/models/admin/app_config.dart';
import 'package:mlink/models/admin/admin_analytics.dart';
import 'package:mlink/models/user.dart' as app_user;
import 'package:mlink/models/request.dart';
import 'package:mlink/supabase/supabase_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AdminService {
  static AdminService? _instance;
  static AdminService get instance => _instance ??= AdminService._();
  AdminService._();

  // Posts Management
  Future<List<Post>> getAllPosts() async {
    final response = await SupabaseConfig.client
        .from('posts')
        .select()
        .order('created_at', ascending: false);
    
    return (response as List)
        .map((json) => Post.fromJson(json))
        .toList();
  }

  Future<Post> createPost(Post post) async {
    final response = await SupabaseConfig.client
        .from('posts')
        .insert(post.toJson())
        .select()
        .single();
    
    return Post.fromJson(response);
  }

  Future<Post> updatePost(Post post) async {
    final response = await SupabaseConfig.client
        .from('posts')
        .update(post.toJson())
        .eq('id', post.id)
        .select()
        .single();
    
    return Post.fromJson(response);
  }

  Future<void> deletePost(String postId) async {
    await SupabaseConfig.client
        .from('posts')
        .delete()
        .eq('id', postId);
  }

  // Users Management
  Future<List<app_user.User>> getAllUsers({app_user.UserRole? role, app_user.UserStatus? status}) async {
    var query = SupabaseConfig.client
        .from('users')
        .select();

    if (role != null) {
      query = query.eq('role', role.name);
    }
    if (status != null) {
      query = query.eq('status', status.name);
    }

    final response = await query.order('created_at', ascending: false);
    return (response as List)
        .map((json) => app_user.User.fromJson(json))
        .toList();
  }

  Future<void> updateUserStatus(String userId, app_user.UserStatus status) async {
    await SupabaseConfig.client
        .from('users')
        .update({
          'status': status.name,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', userId);
  }

  Future<void> resetUserPassword(String userId, String newPassword) async {
    // This would typically involve calling a Supabase Edge Function
    // For now, we'll implement a placeholder
    await SupabaseConfig.client.rpc('reset_user_password', params: {
      'user_id': userId,
      'new_password': newPassword,
    });
  }

  // Requests Management
  Future<Map<String, List<BaseRequest>>> getRequestsByType() async {
    final response = await SupabaseConfig.client
        .from('requests')
        .select('''
          *,
          delivery_requests(*),
          buy_for_me_requests(*),
          technician_requests(*)
        ''')
        .order('created_at', ascending: false);

    final requests = (response as List)
        .map((json) => BaseRequest.fromJson(json))
        .toList();

    return {
      'delivery': requests.where((r) => r.type == RequestType.delivery).toList(),
      'buy_for_me': requests.where((r) => r.type == RequestType.buyForMe).toList(),
      'technician': requests.where((r) => r.type == RequestType.technicianService).toList(),
    };
  }

  Future<void> updateRequestStatus(String requestId, RequestStatus status) async {
    await SupabaseConfig.client
        .from('requests')
        .update({
          'status': status.name,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', requestId);
  }

  Future<void> reassignRequest(String requestId, String newAssigneeId) async {
    // Implementation depends on request type
    // This is a simplified version
    await SupabaseConfig.client.rpc('reassign_request', params: {
      'request_id': requestId,
      'new_assignee_id': newAssigneeId,
    });
  }

  // App Configuration Management
  Future<List<AppConfig>> getAllConfigs() async {
    final response = await SupabaseConfig.client
        .from('app_config')
        .select()
        .order('category', ascending: true)
        .order('key', ascending: true);
    
    return (response as List)
        .map((json) => AppConfig.fromJson(json))
        .toList();
  }

  Future<AppConfig> updateConfig(AppConfig config) async {
    final response = await SupabaseConfig.client
        .from('app_config')
        .update({
          'value': config.value,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', config.id)
        .select()
        .single();
    
    return AppConfig.fromJson(response);
  }

  Future<Map<String, List<AppConfig>>> getConfigsByCategory() async {
    final configs = await getAllConfigs();
    final Map<String, List<AppConfig>> configsByCategory = {};
    
    for (final config in configs) {
      if (!configsByCategory.containsKey(config.category)) {
        configsByCategory[config.category] = [];
      }
      configsByCategory[config.category]!.add(config);
    }
    
    return configsByCategory;
  }

  // Analytics
  Future<AdminAnalytics> getAnalytics() async {
    final response = await SupabaseConfig.client.rpc('get_admin_analytics');
    return AdminAnalytics.fromJson(response);
  }

  Future<List<DailyStats>> getDailyStats(int days) async {
    final response = await SupabaseConfig.client.rpc('get_daily_stats', params: {
      'days_back': days,
    });
    
    return (response as List)
        .map((json) => DailyStats.fromJson(json))
        .toList();
  }

  Future<List<ServiceAreaStats>> getTopServiceAreas(int limit) async {
    final response = await SupabaseConfig.client.rpc('get_top_service_areas', params: {
      'area_limit': limit,
    });
    
    return (response as List)
        .map((json) => ServiceAreaStats.fromJson(json))
        .toList();
  }

  // Subscription Management
  Future<Map<String, dynamic>> getSubscriptionStats() async {
    final response = await SupabaseConfig.client.rpc('get_subscription_stats');
    return response;
  }

  Future<void> updateCommissionRate(double newRate) async {
    await updateConfig(AppConfig(
      id: '', // Will be fetched by key
      key: 'agent_commission_percentage',
      value: newRate.toString(),
      valueType: ConfigValueType.number,
      category: 'pricing',
      isSensitive: false,
      isPublic: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ));
  }
}