import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mlink/theme.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/screens/welcome_screen.dart';
import 'package:mlink/screens/customer/customer_home_screen.dart';
import 'package:mlink/screens/agent/agent_home_screen.dart';
import 'package:mlink/screens/technician/technician_home_screen.dart';
import 'package:mlink/screens/admin/admin_dashboard_screen.dart';
import 'package:mlink/models/user.dart' as app_user;
import 'package:mlink/supabase/supabase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");
    
    // Initialize Supabase
    await SupabaseConfig.initialize();
    
    print('M-Link: App initialized successfully with backend-first architecture');
  } catch (e) {
    print('M-Link: Initialization error: $e');
    // Continue with app startup even if initialization fails
  }
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'M-Link',
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: ThemeMode.system,
      home: const AuthWrapper(),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isLoading = true;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    if (_isInitialized) return;
    
    try {
      print('M-Link: Initializing auth service...');
      
      // Add a timeout to prevent hanging
      await AuthService.instance.initialize().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          print('M-Link: Auth initialization timed out');
          throw Exception('Auth initialization timed out');
        },
      );
      
      print('M-Link: Auth service initialized successfully');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isInitialized = true;
        });
      }
    } catch (e) {
      print('M-Link: Auth initialization error: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isInitialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print('M-Link: AuthWrapper build - isLoading: $_isLoading, isInitialized: $_isInitialized');
    
    if (_isLoading) {
      print('M-Link: Showing loading screen...');
      return Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'M-Link',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                'Connecting Tanzania',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.7),
                    ),
              ),
              const SizedBox(height: 32),
              CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
        ),
      );
    }

    print('M-Link: Building StreamBuilder...');
    return StreamBuilder<bool>(
      stream: AuthService.instance.authStateStream,
      initialData: AuthService.instance.isLoggedIn,
      builder: (context, snapshot) {
        final isLoggedIn = snapshot.data ?? false;
        print('M-Link: StreamBuilder - isLoggedIn: $isLoggedIn');
        
        if (!isLoggedIn) {
          print('M-Link: User not logged in, showing WelcomeScreen');
          return const WelcomeScreen();
        }

        // Navigate based on user role
        final user = AuthService.instance.currentUser;
        switch (user?.role) {
          case app_user.UserRole.customer:
            return const CustomerHomeScreen();
          case app_user.UserRole.agent:
            return const AgentHomeScreen();
          case app_user.UserRole.technician:
            return const TechnicianHomeScreen();
          case app_user.UserRole.admin:
            return const AdminDashboardScreen();
          default:
            return const WelcomeScreen();
        }
      },
    );
  }
}
