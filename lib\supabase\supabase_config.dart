import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class SupabaseConfig {
  static String get supabaseUrl => dotenv.env['https://hhlazpbeoqoknaqwxptx.supabase.co'] ?? '';
  static String get anonKey => dotenv.env['eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhobGF6cGJlb3Fva25hcXd4cHR4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDUwMzcsImV4cCI6MjA2ODQyMTAzN30.AlQcAxnGtkYuCWV8hYf18x6hWD5sulMi8IyRqbeEdFE'] ?? '';
  
  static SupabaseClient get client => Supabase.instance.client;

  /// Initialize Supabase client
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: anonKey,
    );
  }

  /// Get the currently authenticated user
  static User? get currentUser => client.auth.currentUser;

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  /// Stream of authentication state changes
  static Stream<AuthState> get authStateStream => client.auth.onAuthStateChange;
  
  // Authentication helpers
  /// Sign in with email and password
  static Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      return await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Network error: Please check your connection');
    }
  }
  
  /// Sign up with email and password
  static Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    Map<String, dynamic>? userData,
  }) async {
    try {
      return await client.auth.signUp(
        email: email,
        password: password,
        data: userData,
      );
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Network error: Please check your connection');
    }
  }
  
  static Future<void> signInWithGoogle() async {
    try {
      await client.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'io.supabase.flutter://auth-callback',
      );
    } catch (e) {
      throw AuthException('Failed to sign in with Google: ${e.toString()}');
    }
  }
  
  /// Sign out
  static Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Network error: Please check your connection');
    }
  }

  /// Reset password
  static Future<void> resetPassword(String email) async {
    try {
      await client.auth.resetPasswordForEmail(email);
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Network error: Please check your connection');
    }
  }
  
  // Database helpers
  static Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      final response = await client
          .from('users')
          .select('id, email, full_name, phone_number, role, status, profile_image_url, created_at, updated_at, metadata, online_status, last_seen, avatar_url')
          .eq('id', userId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get user profile: ${e.toString()}');
    }
  }
  
  /// Create or update user profile after authentication
  static Future<void> createUserProfile({
    required String userId,
    required String name,
    required String email,
    required String role,
    String? phone,
    String? avatar,
  }) async {
    try {
      await client.from('users').upsert({
        'id': userId,
        'full_name': name,
        'email': email,
        'role': role,
        'phone_number': phone,
        'profile_image_url': avatar,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Failed to create user profile: ${e.toString()}');
    }
  }
  
  static Future<void> updateUserProfile(String userId, Map<String, dynamic> userData) async {
    try {
      await client
          .from('users')
          .update(userData)
          .eq('id', userId);
    } catch (e) {
      throw DatabaseException('Failed to update user profile: ${e.toString()}');
    }
  }
  
  // Agent helpers
  static Future<List<Map<String, dynamic>>> getAvailableAgents() async {
    try {
      final response = await client
          .from('agents')
          .select('''
            id, user_id, vehicle_type, is_available, is_verified, rating, total_deliveries,
            current_location_lat, current_location_lng,
            users!user_id(id, full_name, phone_number, profile_image_url)
          ''')
          .eq('is_available', true)
          .eq('is_verified', true)
          .order('rating', ascending: false)
          .limit(50); // Limit results for performance
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get available agents: ${e.toString()}');
    }
  }
  
  static Future<Map<String, dynamic>?> getAgentProfile(String userId) async {
    try {
      final response = await client
          .from('agents')
          .select('*, users!user_id(full_name, phone_number, email)')
          .eq('user_id', userId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get agent profile: ${e.toString()}');
    }
  }
  
  // Technician helpers
  static Future<List<Map<String, dynamic>>> getAvailableTechnicians(String? category) async {
    try {
      var query = client
          .from('technicians')
          .select('''
            id, user_id, category, is_available, is_verified, rating, total_jobs,
            experience_years, hourly_rate, location,
            users!user_id(id, full_name, phone_number, profile_image_url)
          ''')
          .eq('is_available', true)
          .eq('is_verified', true);
      
      if (category != null) {
        query = query.eq('category', category);
      }

      final response = await query.order('rating', ascending: false)
          .limit(50); // Limit results for performance
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get available technicians: ${e.toString()}');
    }
  }
  
  static Future<Map<String, dynamic>?> getTechnicianProfile(String userId) async {
    try {
      final response = await client
          .from('technicians')
          .select('*, users!user_id(full_name, phone_number, email)')
          .eq('user_id', userId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get technician profile: ${e.toString()}');
    }
  }
  
  // Request helpers
  static Future<List<Map<String, dynamic>>> getUserRequests(String userId) async {
    try {
      final response = await client
          .from('requests')
          .select('''
            id, type, status, description, created_at, updated_at, scheduled_at,
            pickup_location, dropoff_location, payment, notes,
            users!customer_id(id, full_name, phone_number)
          ''')
          .eq('customer_id', userId)
          .order('created_at', ascending: false)
          .limit(100); // Limit for pagination
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get user requests: ${e.toString()}');
    }
  }
  
  static Future<List<Map<String, dynamic>>> getAvailableRequests(String requestType) async {
    try {
      final response = await client
          .from('requests')
          .select('''
            id, type, status, description, created_at, updated_at, scheduled_at,
            pickup_location, dropoff_location, payment, customer_phone,
            users!customer_id(id, full_name, phone_number, profile_image_url)
          ''')
          .eq('type', requestType)
          .eq('status', 'pending')
          .order('created_at', ascending: false)
          .limit(50); // Limit for performance
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get available requests: ${e.toString()}');
    }
  }
  
  static Future<Map<String, dynamic>?> getRequestDetails(String requestId) async {
    try {
      final response = await client
          .from('requests')
          .select('*, users!customer_id(full_name, phone_number)')
          .eq('id', requestId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get request details: ${e.toString()}');
    }
  }
  
  static Future<void> updateRequestStatus(String requestId, String status, String updatedBy) async {
    try {
      await client
          .from('requests')
          .update({'status': status, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', requestId);
      
      // Log the status change
      await client.from('request_updates').insert({
        'request_id': requestId,
        'new_status': status,
        'updated_by': updatedBy,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw DatabaseException('Failed to update request status: ${e.toString()}');
    }
  }
  
  // Notification helpers
  static Future<List<Map<String, dynamic>>> getUserNotifications(String userId) async {
    try {
      final response = await client
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get user notifications: ${e.toString()}');
    }
  }
  
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await client
          .from('notifications')
          .update({'is_read': true})
          .eq('id', notificationId);
    } catch (e) {
      throw DatabaseException('Failed to mark notification as read: ${e.toString()}');
    }
  }
  
  static Future<void> createNotification(Map<String, dynamic> notificationData) async {
    try {
      await client.from('notifications').insert(notificationData);
    } catch (e) {
      throw DatabaseException('Failed to create notification: ${e.toString()}');
    }
  }
  
  // Review helpers
  static Future<List<Map<String, dynamic>>> getUserReviews(String userId) async {
    try {
      final response = await client
          .from('reviews')
          .select('*, reviewer:users!reviewer_id(full_name), reviewee:users!reviewee_id(full_name)')
          .eq('reviewee_id', userId)
          .order('created_at', ascending: false);
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get user reviews: ${e.toString()}');
    }
  }
  
  static Future<void> createReview(Map<String, dynamic> reviewData) async {
    try {
      await client.from('reviews').insert(reviewData);
    } catch (e) {
      throw DatabaseException('Failed to create review: ${e.toString()}');
    }
  }
  
  // Real-time helpers
  static RealtimeChannel subscribeToRequests(String requestType, Function(Map<String, dynamic>) callback) {
    return client.channel('requests:$requestType')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'requests',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'type',
            value: requestType,
          ),
          callback: (payload) {
            callback(payload.newRecord);
          },
        )
        .subscribe();
  }
  
  static RealtimeChannel subscribeToUserNotifications(String userId, Function(Map<String, dynamic>) callback) {
    return client.channel('notifications:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'notifications',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) {
            callback(payload.newRecord);
          },
        )
        .subscribe();
  }

  // Chat helpers
  static Future<List<Map<String, dynamic>>> getUserChats(String userId) async {
    try {
      final response = await client
          .from('chats')
          .select('*, user1:users!user1_id(id, full_name, phone_number), user2:users!user2_id(id, full_name, phone_number)')
          .or('user1_id.eq.$userId,user2_id.eq.$userId')
          .order('last_message_at', ascending: false);
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get user chats: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>?> getChatById(String chatId) async {
    try {
      final response = await client
          .from('chats')
          .select('*, user1:users!user1_id(id, full_name, phone_number), user2:users!user2_id(id, full_name, phone_number)')
          .eq('id', chatId)
          .maybeSingle();
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get chat: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>?> getOrCreateChat(String user1Id, String user2Id, {String? requestId}) async {
    try {
      // Try to find existing chat between these users
      final existingChat = await client
          .from('chats')
          .select()
          .or('and(user1_id.eq.$user1Id,user2_id.eq.$user2Id),and(user1_id.eq.$user2Id,user2_id.eq.$user1Id)')
          .maybeSingle();

      if (existingChat != null) {
        return existingChat;
      }

      // Create new chat if none exists
      final newChat = await client
          .from('chats')
          .insert({
            'user1_id': user1Id,
            'user2_id': user2Id,
            if (requestId != null) 'request_id': requestId,
          })
          .select()
          .single();

      return newChat;
    } catch (e) {
      throw DatabaseException('Failed to get or create chat: ${e.toString()}');
    }
  }

  static Future<List<Map<String, dynamic>>> getChatMessages(String chatId, {int limit = 50, int offset = 0}) async {
    try {
      final response = await client
          .from('messages')
          .select('*, sender:users!sender_id(id, full_name)')
          .eq('chat_id', chatId)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);
      return response;
    } catch (e) {
      throw DatabaseException('Failed to get chat messages: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>> sendMessage(String chatId, String senderId, String content, {String messageType = 'text', String? fileUrl, String? fileName, int? fileSize}) async {
    try {
      final message = await client
          .from('messages')
          .insert({
            'chat_id': chatId,
            'sender_id': senderId,
            'content': content,
            'message_type': messageType,
            if (fileUrl != null) 'file_url': fileUrl,
            if (fileName != null) 'file_name': fileName,
            if (fileSize != null) 'file_size': fileSize,
          })
          .select()
          .single();

      // Update chat's last message
      await client
          .from('chats')
          .update({
            'last_message': content,
            'last_message_at': DateTime.now().toIso8601String(),
          })
          .eq('id', chatId);

      return message;
    } catch (e) {
      throw DatabaseException('Failed to send message: ${e.toString()}');
    }
  }

  static Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      await client
          .from('messages')
          .update({'is_read': true, 'read_at': DateTime.now().toIso8601String()})
          .eq('chat_id', chatId)
          .neq('sender_id', userId)
          .eq('is_read', false);
    } catch (e) {
      throw DatabaseException('Failed to mark messages as read: ${e.toString()}');
    }
  }

  static Future<int> getUnreadMessageCount(String userId) async {
    try {
      final response = await client
          .from('messages')
          .select('id, chats!inner(user1_id, user2_id)')
          .neq('sender_id', userId)
          .eq('is_read', false)
          .or('chats.user1_id.eq.$userId,chats.user2_id.eq.$userId');
      return response.length;
    } catch (e) {
      throw DatabaseException('Failed to get unread message count: ${e.toString()}');
    }
  }

  // Real-time chat subscriptions
  static RealtimeChannel subscribeToChatMessages(String chatId, Function(Map<String, dynamic>) callback) {
    return client.channel('chat_messages:$chatId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'messages',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'chat_id',
            value: chatId,
          ),
          callback: (payload) {
            callback(payload.newRecord);
          },
        )
        .subscribe();
  }

  static RealtimeChannel subscribeToUserChats(String userId, Function(Map<String, dynamic>) callback) {
    return client.channel('user_chats:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'chats',
          callback: (payload) {
            final chat = payload.newRecord;
            if (chat['user1_id'] == userId || chat['user2_id'] == userId) {
              callback(chat);
            }
          },
        )
        .subscribe();
  }

  /// Handle common authentication exceptions
  static Exception _handleAuthException(AuthException e) {
    switch (e.message) {
      case 'Invalid login credentials':
        return Exception('Invalid email or password');
      case 'Email not confirmed':
        return Exception('Please confirm your email address');
      case 'User not found':
        return Exception('No account found with this email');
      case 'Password should be at least 6 characters':
        return Exception('Password must be at least 6 characters long');
      default:
        return Exception(e.message);
    }
  }

  /// Generic database query helper
  static SupabaseQueryBuilder from(String table) {
    return client.from(table);
  }

  /// Real-time subscription helper
  static RealtimeChannel channel(String channelName) {
    return client.channel(channelName);
  }
}

class AuthException implements Exception {
  final String message;
  AuthException(this.message);
  
  @override
  String toString() => 'AuthException: $message';
}

class DatabaseException implements Exception {
  final String message;
  DatabaseException(this.message);
  
  @override
  String toString() => 'DatabaseException: $message';
}

class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}