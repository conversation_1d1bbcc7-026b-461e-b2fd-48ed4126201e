import 'package:flutter/material.dart';
import 'package:mlink/models/tanzania_locations.dart' as tz;
import 'package:mlink/models/request.dart';
import 'package:mlink/services/maps_service.dart';
import 'package:mlink/services/location_service.dart';

class LocationSelector extends StatefulWidget {
  final String label;
  final String? selectedRegion;
  final String? selectedDistrict;
  final LocationPoint? customLocation;
  final Function(String?) onRegionChanged;
  final Function(String?) onDistrictChanged;
  final Function(LocationPoint?) onCustomLocationChanged;
  final bool allowCustomLocation;
  final String? hint;

  const LocationSelector({
    super.key,
    required this.label,
    this.selectedRegion,
    this.selectedDistrict,
    this.customLocation,
    required this.onRegionChanged,
    required this.onDistrictChanged,
    required this.onCustomLocationChanged,
    this.allowCustomLocation = true,
    this.hint,
  });

  @override
  State<LocationSelector> createState() => _LocationSelectorState();
}

class _LocationSelectorState extends State<LocationSelector> {
  final _customLocationController = TextEditingController();
  bool _showCustomLocation = false;
  final MapsService _mapsService = MapsService.instance;

  @override
  void initState() {
    super.initState();
    if (widget.customLocation != null) {
      _customLocationController.text = widget.customLocation!.address ?? '';
      _showCustomLocation = true;
    }
  }

  @override
  void dispose() {
    _customLocationController.dispose();
    super.dispose();
  }

  Future<LocationPoint?> _getLocationPoint() async {
    if (_showCustomLocation && widget.customLocation != null) {
      return widget.customLocation;
    }
    
    if (widget.selectedRegion != null && widget.selectedDistrict != null) {
      final location = await LocationService.getLocationByName(
        widget.selectedRegion!, 
        widget.selectedDistrict!,
      );
      if (location != null) {
        return LocationPoint(
          latitude: location.latitude,
          longitude: location.longitude,
          address: '${location.name}, ${widget.selectedRegion}',
        );
      }
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        // Region Dropdown
        FutureBuilder<List<String>>(
          future: LocationService.getRegionNames(),
          builder: (context, snapshot) {
            return Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButtonFormField<String>(
                value: widget.selectedRegion,
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Icons.location_city_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  labelText: 'Select Region',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                isExpanded: true,
                menuMaxHeight: 300,
                items: snapshot.hasData 
                    ? snapshot.data!.map((region) {
                        return DropdownMenuItem(
                          value: region,
                          child: Text(
                            region,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList()
                    : [DropdownMenuItem(value: null, child: Text('Loading...'))],
                onChanged: snapshot.hasData ? (value) {
                  widget.onRegionChanged(value);
                  widget.onDistrictChanged(null); // Reset district when region changes
                  setState(() {
                    _showCustomLocation = false;
                  });
                } : null,
              ),
            );
          },
        ),
        const SizedBox(height: 16),
        
        // District Dropdown
        FutureBuilder<List<String>>(
          future: widget.selectedRegion != null 
              ? LocationService.getDistrictNames(widget.selectedRegion!)
              : Future.value([]),
          builder: (context, snapshot) {
            return Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButtonFormField<String>(
                value: widget.selectedDistrict,
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Icons.location_on_outlined,
                    color: widget.selectedRegion != null 
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline,
                  ),
                  labelText: 'Select District/Area',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                isExpanded: true,
                menuMaxHeight: 300,
                items: widget.selectedRegion != null && snapshot.hasData
                    ? snapshot.data!.map((district) {
                        return DropdownMenuItem(
                          value: district,
                          child: Text(
                            district,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList()
                    : widget.selectedRegion != null
                        ? [DropdownMenuItem(value: null, child: Text('Loading districts...'))]
                        : [],
                onChanged: widget.selectedRegion != null && snapshot.hasData
                    ? (value) {
                        widget.onDistrictChanged(value);
                        setState(() {
                          _showCustomLocation = false;
                        });
                      }
                    : null,
              ),
            );
          },
        ),
        
        if (widget.allowCustomLocation) ...[
          const SizedBox(height: 12),
          
          // Toggle for custom location
          Row(
            children: [
              Text(
                'Location not listed?',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: () {
                  setState(() {
                    _showCustomLocation = !_showCustomLocation;
                    if (!_showCustomLocation) {
                      _customLocationController.clear();
                      widget.onCustomLocationChanged(null);
                    }
                  });
                },
                child: Text(_showCustomLocation ? 'Use dropdown' : 'Enter manually'),
              ),
            ],
          ),
          
          // Custom location input
          if (_showCustomLocation) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _customLocationController,
                    decoration: InputDecoration(
                      prefixIcon: Icon(
                        Icons.edit_location_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      labelText: 'Enter location',
                      hintText: widget.hint ?? 'Type location name...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          Icons.search,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        onPressed: () => _searchLocation(),
                        tooltip: 'Search with Google Maps',
                      ),
                    ),
                    onChanged: (value) {
                      // For now, create a basic location point
                      // TODO: Implement Google Maps Autocomplete
                      if (value.isNotEmpty) {
                        final locationPoint = LocationPoint(
                          latitude: 0.0, // Will be updated with actual coordinates
                          longitude: 0.0,
                          address: value,
                        );
                        widget.onCustomLocationChanged(locationPoint);
                      } else {
                        widget.onCustomLocationChanged(null);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _getCurrentLocation(),
                  icon: const Icon(Icons.my_location),
                  tooltip: 'Use current location',
                ),
              ],
            ),
          ],
        ],
        
        // Show selected location info
        if (_getLocationPoint() != null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Theme.of(context).colorScheme.tertiary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Selected location',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onTertiaryContainer,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _searchLocation() async {
    final query = _customLocationController.text.trim();
    if (query.isEmpty) return;
    
    // TODO: Implement Google Maps Autocomplete search
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Google Maps search will be implemented soon'),
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      final currentLocation = await _mapsService.getCurrentLocationPoint();
      if (currentLocation != null) {
        setState(() {
          _customLocationController.text = currentLocation.address ?? 'Current Location';
          _showCustomLocation = true;
        });
        widget.onCustomLocationChanged(currentLocation);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Unable to get current location. Please check permissions.'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
          ),
        );
      }
    }
  }
}

class SelectedLocation {
  final String? region;
  final String? district;
  final LocationPoint? customLocation;
  
  SelectedLocation({
    this.region,
    this.district,
    this.customLocation,
  });
  
  LocationPoint? get locationPoint {
    if (customLocation != null) {
      return customLocation;
    }
    
    if (region != null && district != null) {
      final location = tz.TanzaniaLocations.getLocationByName(region!, district!);
      if (location != null) {
        return LocationPoint(
          latitude: location.latitude,
          longitude: location.longitude,
          address: '${location.name}, $region',
        );
      }
    }
    
    return null;
  }
  
  String get displayText {
    if (customLocation != null) {
      return customLocation!.address ?? 'Custom Location';
    }
    
    if (region != null && district != null) {
      return '$district, $region';
    }
    
    if (region != null) {
      return region!;
    }
    
    return 'No location selected';
  }
  
  bool get isValid {
    return locationPoint != null;
  }
}