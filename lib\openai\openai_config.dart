import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:mlink/supabase/supabase_config.dart';

class OpenAIConfig {
  // Using environment variables for configuration
  static const String apiKey = String.fromEnvironment('OPENAI_PROXY_API_KEY');
  static const String endpoint = String.fromEnvironment('OPENAI_PROXY_ENDPOINT');
  
  // If environment variables are not set, use Supabase edge functions
  static String get _apiKey {
    if (apiKey.isNotEmpty) {
      return apiKey;
    }
    // Use the user's access token for Supabase edge function authentication
    final accessToken = SupabaseConfig.client.auth.currentSession?.accessToken;
    if (accessToken != null) {
      return accessToken;
    }
    throw OpenAIException('No API key configured. Please set OPENAI_PROXY_API_KEY in your environment or ensure user is authenticated.');
  }
  
  static String get _endpoint => endpoint.isNotEmpty ? endpoint : '${SupabaseConfig.supabaseUrl}/functions/v1/openai-chat';
  
  static Future<Map<String, dynamic>> generateChatCompletion({
    required List<Map<String, dynamic>> messages,
    String model = 'gpt-4o',
    double temperature = 0.7,
    int maxTokens = 2000,
    Map<String, dynamic>? responseFormat,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        final requestBody = {
          'model': model,
          'messages': messages,
          'temperature': temperature,
          'max_tokens': maxTokens,
          if (responseFormat != null) 'response_format': responseFormat,
        };

        final response = await http.post(
          Uri.parse(_endpoint),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_apiKey',
          },
          body: utf8.encode(json.encode(requestBody)),
        ).timeout(const Duration(seconds: 30));

        if (response.statusCode == 200) {
          final decodedResponse = utf8.decode(response.bodyBytes);
          final jsonResponse = json.decode(decodedResponse);
          
          // Validate response structure
          if (jsonResponse is Map<String, dynamic>) {
            return jsonResponse;
          } else {
            throw OpenAIException('Invalid response format from OpenAI API');
          }
        } else if (response.statusCode == 429) {
          // Rate limit exceeded - wait and retry
          retryCount++;
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount * 2));
            continue;
          }
          throw OpenAIException('Rate limit exceeded. Please try again later.');
        } else if (response.statusCode >= 500) {
          // Server error - retry
          retryCount++;
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount));
            continue;
          }
          throw OpenAIException('Server error. Please try again later.');
        } else {
          throw OpenAIException(
            'OpenAI API request failed with status ${response.statusCode}: ${response.body}',
          );
        }
      } catch (e) {
        if (e is OpenAIException) {
          rethrow;
        }
        
        // Handle network errors
        if (e.toString().contains('TimeoutException') || 
            e.toString().contains('SocketException') ||
            e.toString().contains('HandshakeException')) {
          retryCount++;
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount));
            continue;
          }
          throw OpenAIException('Network error. Please check your internet connection and try again.');
        }
        
        throw OpenAIException('Failed to generate chat completion: ${e.toString()}');
      }
    }
    
    throw OpenAIException('Maximum retries exceeded. Please try again later.');
  }

  static Future<Map<String, dynamic>> generateChatCompletionWithImage({
    required String prompt,
    required String imageBase64,
    String model = 'gpt-4o',
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final messages = [
      {
        'role': 'user',
        'content': [
          {
            'type': 'text',
            'text': prompt,
          },
          {
            'type': 'image_url',
            'image_url': {'url': 'data:image/jpeg;base64,$imageBase64'},
          },
        ],
      }
    ];

    return await generateChatCompletion(
      messages: messages,
      model: model,
      temperature: temperature,
      maxTokens: maxTokens,
    );
  }

  static Future<Map<String, dynamic>> generateStructuredResponse({
    required List<Map<String, dynamic>> messages,
    required Map<String, dynamic> jsonSchema,
    String model = 'gpt-4o',
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    // Add JSON instruction to system message
    final systemMessage = {
      'role': 'system',
      'content': 'You are a helpful assistant. Always respond with valid JSON that follows the provided schema.',
    };

    final updatedMessages = [systemMessage, ...messages];

    return await generateChatCompletion(
      messages: updatedMessages,
      model: model,
      temperature: temperature,
      maxTokens: maxTokens,
      responseFormat: {
        'type': 'json_object',
        'schema': jsonSchema,
      },
    );
  }

  static String extractMessageContent(Map<String, dynamic> response) {
    try {
      final choices = response['choices'] as List<dynamic>?;
      if (choices != null && choices.isNotEmpty) {
        final choice = choices.first as Map<String, dynamic>?;
        if (choice != null) {
          final message = choice['message'] as Map<String, dynamic>?;
          if (message != null) {
            return message['content'] as String? ?? '';
          }
        }
      }
      return '';
    } catch (e) {
      throw OpenAIException('Failed to extract message content: ${e.toString()}');
    }
  }

  static bool isRateLimited(Map<String, dynamic> response) {
    final error = response['error'] as Map<String, dynamic>?;
    if (error != null) {
      final type = error['type'] as String?;
      return type == 'rate_limit_exceeded';
    }
    return false;
  }

  static String getErrorMessage(Map<String, dynamic> response) {
    final error = response['error'] as Map<String, dynamic>?;
    if (error != null) {
      return error['message'] as String? ?? 'Unknown error occurred';
    }
    return 'Unknown error occurred';
  }

  static String getUserFriendlyError(dynamic error) {
    if (error is OpenAIException) {
      final message = error.message.toLowerCase();
      
      if (message.contains('rate limit')) {
        return 'AI services are temporarily busy. Please try again in a few moments.';
      } else if (message.contains('network') || message.contains('connection')) {
        return 'Please check your internet connection and try again.';
      } else if (message.contains('timeout')) {
        return 'Request timed out. Please try again.';
      } else if (message.contains('server error') || message.contains('500')) {
        return 'AI services are temporarily unavailable. Please try again later.';
      } else if (message.contains('unauthorized') || message.contains('401')) {
        return 'Authentication failed. Please log in again.';
      } else if (message.contains('invalid') || message.contains('400')) {
        return 'Your request could not be processed. Please try again.';
      }
    }
    
    return 'AI assistant is temporarily unavailable. Please try again later.';
  }
}

class OpenAIException implements Exception {
  final String message;
  OpenAIException(this.message);
  
  @override
  String toString() => 'OpenAIException: $message';
}