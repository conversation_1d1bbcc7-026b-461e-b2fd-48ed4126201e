import 'package:flutter/material.dart';

class GoogleIcon extends StatelessWidget {
  final double size;
  
  const GoogleIcon({super.key, this.size = 20});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: CustomPaint(
        painter: Google<PERSON>ogoPainter(),
        size: Size(size, size),
      ),
    );
  }
}

class GoogleLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    
    // Create path for the "G" shape
    final path = Path();
    
    // Google Blue
    paint.color = const Color(0xFF4285F4);
    path.moveTo(size.width * 0.5, size.height * 0.1);
    path.lineTo(size.width * 0.9, size.height * 0.1);
    path.lineTo(size.width * 0.9, size.height * 0.4);
    path.lineTo(size.width * 0.7, size.height * 0.4);
    path.lineTo(size.width * 0.7, size.height * 0.3);
    path.lineTo(size.width * 0.5, size.height * 0.3);
    path.close();
    canvas.drawPath(path, paint);
    
    // Google Red
    paint.color = const Color(0xFFEA4335);
    path.reset();
    path.moveTo(size.width * 0.1, size.height * 0.5);
    path.lineTo(size.width * 0.5, size.height * 0.1);
    path.lineTo(size.width * 0.5, size.height * 0.3);
    path.lineTo(size.width * 0.3, size.height * 0.5);
    path.close();
    canvas.drawPath(path, paint);
    
    // Google Yellow
    paint.color = const Color(0xFFFBBC04);
    path.reset();
    path.moveTo(size.width * 0.1, size.height * 0.5);
    path.lineTo(size.width * 0.3, size.height * 0.5);
    path.lineTo(size.width * 0.5, size.height * 0.7);
    path.lineTo(size.width * 0.5, size.height * 0.9);
    path.close();
    canvas.drawPath(path, paint);
    
    // Google Green
    paint.color = const Color(0xFF34A853);
    path.reset();
    path.moveTo(size.width * 0.5, size.height * 0.7);
    path.lineTo(size.width * 0.7, size.height * 0.5);
    path.lineTo(size.width * 0.9, size.height * 0.5);
    path.lineTo(size.width * 0.9, size.height * 0.9);
    path.lineTo(size.width * 0.5, size.height * 0.9);
    path.close();
    canvas.drawPath(path, paint);
    
    // Center circle (white)
    paint.color = Colors.white;
    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.5),
      size.width * 0.15,
      paint,
    );
    
    // G letter
    paint.color = const Color(0xFF4285F4);
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'G',
        style: TextStyle(
          color: Color(0xFF4285F4),
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        size.width * 0.5 - textPainter.width * 0.5,
        size.height * 0.5 - textPainter.height * 0.5,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}