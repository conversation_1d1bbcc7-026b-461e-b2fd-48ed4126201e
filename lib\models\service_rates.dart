class HourlyRate {
  final double value;
  final String displayText;
  final bool isCustom;

  const HourlyRate({
    required this.value,
    required this.displayText,
    this.isCustom = false,
  });

  @override
  String toString() => displayText;
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HourlyRate && other.value == value && other.isCustom == isCustom;
  }
  
  @override
  int get hashCode => value.hashCode ^ isCustom.hashCode;
}

class ServiceRates {
  static const List<HourlyRate> commonHourlyRates = [
    HourlyRate(value: 3000, displayText: 'TSh 3,000/hr'),
    HourlyRate(value: 5000, displayText: 'TSh 5,000/hr'),
    HourlyRate(value: 7500, displayText: 'TSh 7,500/hr'),
    HourlyRate(value: 10000, displayText: 'TSh 10,000/hr'),
    HourlyRate(value: 12500, displayText: 'TSh 12,500/hr'),
    HourlyRate(value: 15000, displayText: 'TSh 15,000/hr'),
    HourlyRate(value: 20000, displayText: 'TSh 20,000/hr'),
    HourlyRate(value: 25000, displayText: 'TSh 25,000/hr'),
    HourlyRate(value: 30000, displayText: 'TSh 30,000/hr'),
    HourlyRate(value: 40000, displayText: 'TSh 40,000/hr'),
    HourlyRate(value: 50000, displayText: 'TSh 50,000/hr'),
    HourlyRate(value: -1, displayText: 'Custom Rate...', isCustom: true),
  ];

  static HourlyRate? getRateByValue(double value) {
    try {
      return commonHourlyRates.firstWhere((rate) => rate.value == value);
    } catch (e) {
      return null;
    }
  }

  static String formatCurrency(double amount) {
    return 'TSh ${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}';
  }

  static HourlyRate createCustomRate(double value) {
    return HourlyRate(
      value: value,
      displayText: '${formatCurrency(value)}/hr',
      isCustom: true,
    );
  }
}

class DeliveryCostCalculator {
  static const double costPerKm = 1000.0; // TSh 1,000 per km
  static const double baseCost = 2000.0; // TSh 2,000 base cost
  
  static double calculateDeliveryCost(double distanceKm) {
    return baseCost + (distanceKm * costPerKm);
  }
  
  static String formatDeliveryCost(double distanceKm) {
    final cost = calculateDeliveryCost(distanceKm);
    return ServiceRates.formatCurrency(cost);
  }
  
  static String getDeliveryCostBreakdown(double distanceKm) {
    final cost = calculateDeliveryCost(distanceKm);
    final distanceCost = distanceKm * costPerKm;
    
    return 'Base cost: ${ServiceRates.formatCurrency(baseCost)}\n'
           'Distance (${distanceKm.toStringAsFixed(1)} km): ${ServiceRates.formatCurrency(distanceCost)}\n'
           'Total: ${ServiceRates.formatCurrency(cost)}';
  }
}