import 'package:flutter/material.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/services/admin/admin_service.dart';
import 'package:intl/intl.dart';
import 'package:data_table_2/data_table_2.dart';

class RequestManager extends StatefulWidget {
  const RequestManager({super.key});

  @override
  State<RequestManager> createState() => _RequestManagerState();
}

class _RequestManagerState extends State<RequestManager>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, List<Request>> requestsByType = {};
  bool isLoading = true;
  String searchQuery = '';
  final searchController = TextEditingController();
  final dateFormatter = DateFormat('MMM dd, yyyy HH:mm');
  final currencyFormatter = NumberFormat.currency(locale: 'sw_TZ', symbol: 'TSh ');

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRequests() async {
    try {
      final data = await AdminService.instance.getRequestsByType();
      setState(() {
        requestsByType = data;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading requests: $e');
      setState(() => isLoading = false);
    }
  }

  List<Request> get currentRequests {
    String currentType;
    switch (_tabController.index) {
      case 0:
        currentType = 'delivery';
        break;
      case 1:
        currentType = 'buy_for_me';
        break;
      case 2:
        currentType = 'technician';
        break;
      default:
        currentType = 'delivery';
    }

    final requests = requestsByType[currentType] ?? [];
    
    if (searchQuery.isEmpty) return requests;
    
    final query = searchQuery.toLowerCase();
    return requests.where((request) {
      final descriptionMatch = request.description.toLowerCase().contains(query);
      final idMatch = request.id.toLowerCase().contains(query);
      final statusMatch = request.status.name.toLowerCase().contains(query);
      return descriptionMatch || idMatch || statusMatch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Request Manager',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          SizedBox(
            width: 300,
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: 'Search requests...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          searchController.clear();
                          setState(() => searchQuery = '');
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
              onChanged: (value) => setState(() => searchQuery = value),
            ),
          ),
          const SizedBox(width: 16),
        ],
        bottom: TabBar(
          controller: _tabController,
          onTap: (_) => setState(() {}), // Refresh when tab changes
          tabs: [
            Tab(
              icon: const Icon(Icons.delivery_dining),
              text: 'Delivery (${requestsByType['delivery']?.length ?? 0})',
            ),
            Tab(
              icon: const Icon(Icons.shopping_cart),
              text: 'Buy4Me (${requestsByType['buy_for_me']?.length ?? 0})',
            ),
            Tab(
              icon: const Icon(Icons.build),
              text: 'Technician (${requestsByType['technician']?.length ?? 0})',
            ),
          ],
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildRequestsTable('delivery'),
                _buildRequestsTable('buy_for_me'),
                _buildRequestsTable('technician'),
              ],
            ),
    );
  }

  Widget _buildRequestsTable(String type) {
    final theme = Theme.of(context);
    final requests = currentRequests;

    if (requests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getTypeIcon(type),
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No ${_getTypeName(type)} requests found',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            if (searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  searchController.clear();
                  setState(() => searchQuery = '');
                },
                child: const Text('Clear search'),
              ),
            ],
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: DataTable2(
        columnSpacing: 12,
        horizontalMargin: 12,
        minWidth: 1000,
        columns: [
          const DataColumn2(
            label: Text('Request ID'),
            size: ColumnSize.S,
          ),
          const DataColumn2(
            label: Text('Description'),
            size: ColumnSize.L,
          ),
          const DataColumn2(
            label: Text('Status'),
            size: ColumnSize.S,
          ),
          const DataColumn2(
            label: Text('Customer'),
            size: ColumnSize.M,
          ),
          const DataColumn2(
            label: Text('Location'),
            size: ColumnSize.M,
          ),
          const DataColumn2(
            label: Text('Created'),
            size: ColumnSize.S,
          ),
          const DataColumn2(
            label: Text('Actions'),
            size: ColumnSize.S,
          ),
        ],
        rows: requests.map((request) => DataRow2(
          cells: [
            DataCell(
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  'REQ${request.id.substring(0, 8).toUpperCase()}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ),
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    request.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (request.notes?.isNotEmpty == true) ...[
                    const SizedBox(height: 2),
                    Text(
                      request.notes ?? '',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            DataCell(
              Chip(
                label: Text(
                  request.status.name.toUpperCase(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getStatusColor(request.status),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                backgroundColor: _getStatusColor(request.status).withValues(alpha: 0.1),
                side: BorderSide.none,
              ),
            ),
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Customer', // Would be actual customer name
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (request.customerPhone?.isNotEmpty == true)
                    Text(
                      request.customerPhone!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                ],
              ),
            ),
            DataCell(
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _getLocationText(request.pickupLocation.toJson()),
                    style: theme.textTheme.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (request.dropoffLocation != null) ...[
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Icon(
                          Icons.arrow_downward,
                          size: 12,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            _getLocationText(request.dropoffLocation?.toJson()),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            DataCell(
              Text(
                dateFormatter.format(request.createdAt),
                style: theme.textTheme.bodySmall,
              ),
            ),
            DataCell(
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        const Icon(Icons.visibility, size: 16),
                        const SizedBox(width: 8),
                        const Text('View Details'),
                      ],
                    ),
                  ),
                  if (request.status == RequestStatus.pending)
                    PopupMenuItem(
                      value: 'accept',
                      child: Row(
                        children: [
                          Icon(Icons.check, size: 16, color: Colors.green),
                          const SizedBox(width: 8),
                          Text('Accept', style: TextStyle(color: Colors.green)),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'reassign',
                    child: Row(
                      children: [
                        const Icon(Icons.swap_horiz, size: 16),
                        const SizedBox(width: 8),
                        const Text('Reassign'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'cancel',
                    child: Row(
                      children: [
                        Icon(Icons.cancel, size: 16, color: Colors.red),
                        const SizedBox(width: 8),
                        Text('Cancel', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                onSelected: (action) => _handleRequestAction(request, action),
              ),
            ),
          ],
        )).toList(),
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'delivery':
        return Icons.delivery_dining;
      case 'buy_for_me':
        return Icons.shopping_cart;
      case 'technician':
        return Icons.build;
      default:
        return Icons.help;
    }
  }

  String _getTypeName(String type) {
    switch (type) {
      case 'delivery':
        return 'delivery';
      case 'buy_for_me':
        return 'buy-for-me';
      case 'technician':
        return 'technician';
      default:
        return type;
    }
  }

  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.accepted:
        return Colors.blue;
      case RequestStatus.inProgress:
        return Colors.indigo;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.failed:
        return Colors.red;
      case RequestStatus.paymentPending:
        return Colors.purple;
    }
  }

  String _getLocationText(Map<String, dynamic>? location) {
    if (location == null) return 'Location not specified';
    if (location.containsKey('address')) {
      return location['address'].toString();
    } else if (location.containsKey('name')) {
      return location['name'].toString();
    } else if (location.containsKey('lat') && location.containsKey('lng')) {
      return '${location['lat']}, ${location['lng']}';
    }
    return 'Location not specified';
  }

  void _handleRequestAction(Request request, String action) {
    switch (action) {
      case 'view':
        _showRequestDetailsDialog(request);
        break;
      case 'accept':
        _updateRequestStatus(request, RequestStatus.accepted);
        break;
      case 'reassign':
        _showReassignDialog(request);
        break;
      case 'cancel':
        _updateRequestStatus(request, RequestStatus.cancelled);
        break;
    }
  }

  void _showRequestDetailsDialog(Request request) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Request Details - REQ${request.id.substring(0, 8).toUpperCase()}'),
        content: SizedBox(
          width: 500,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Type', request.type.name.toUpperCase()),
                _buildDetailRow('Status', request.status.name.toUpperCase()),
                _buildDetailRow('Description', request.description),
                if (request.notes?.isNotEmpty == true)
                  _buildDetailRow('Notes', request.notes!),
                _buildDetailRow('Pickup Location', _getLocationText(request.pickupLocation.toJson())),
                if (request.dropoffLocation != null)
                  _buildDetailRow('Dropoff Location', _getLocationText(request.dropoffLocation!.toJson())),
                if (request.customerPhone?.isNotEmpty == true)
                  _buildDetailRow('Customer Phone', request.customerPhone!),
                _buildDetailRow('Created', dateFormatter.format(request.createdAt)),
                _buildDetailRow('Updated', dateFormatter.format(request.updatedAt)),
                if (request.scheduledAt != null)
                  _buildDetailRow('Scheduled', dateFormatter.format(request.scheduledAt!)),
                
                // Payment details
                const SizedBox(height: 16),
                const Text('Payment Information:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildDetailRow('Amount', '\$${request.payment.amount}'),
                _buildDetailRow('Method', request.payment.method.name),
                _buildDetailRow('Status', request.payment.isPaid ? 'Paid' : 'Unpaid'),
                
                // Images
                if (request.imageUrls.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text('Attached Images:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: request.imageUrls.map((url) => 
                        Chip(
                          label: Text('Image'),
                          avatar: Icon(Icons.image, size: 16),
                        )
                    ).toList(),
                  ),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _updateRequestStatus(Request request, RequestStatus newStatus) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Request Status'),
        content: Text(
          'Are you sure you want to change request REQ${request.id.substring(0, 8).toUpperCase()} status to ${newStatus.name.toUpperCase()}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: newStatus == RequestStatus.cancelled ? Colors.red : null,
              foregroundColor: newStatus == RequestStatus.cancelled ? Colors.white : null,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await AdminService.instance.updateRequestStatus(request.id, newStatus);
        _loadRequests();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Request status updated successfully')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating request status: $e')),
        );
      }
    }
  }

  void _showReassignDialog(Request request) {
    final assigneeIdController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reassign Request - REQ${request.id.substring(0, 8).toUpperCase()}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Enter the new assignee ID for this ${request.type.name} request:'),
            const SizedBox(height: 16),
            TextField(
              controller: assigneeIdController,
              decoration: const InputDecoration(
                labelText: 'Assignee ID',
                border: OutlineInputBorder(),
                hintText: 'Enter agent or technician ID',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (assigneeIdController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter an assignee ID')),
                );
                return;
              }

              try {
                await AdminService.instance.reassignRequest(
                  request.id,
                  assigneeIdController.text,
                );
                Navigator.of(context).pop();
                _loadRequests();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Request reassigned successfully')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error reassigning request: $e')),
                );
              }
            },
            child: const Text('Reassign'),
          ),
        ],
      ),
    );
  }
}