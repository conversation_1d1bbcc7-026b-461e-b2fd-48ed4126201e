class DailyStats {
  final DateTime date;
  final int totalRequests;
  final int deliveryRequests;
  final int buyForMeRequests;
  final int technicianRequests;
  final int completedRequests;
  final double totalRevenue;

  DailyStats({
    required this.date,
    required this.totalRequests,
    required this.deliveryRequests,
    required this.buyForMeRequests,
    required this.technicianRequests,
    required this.completedRequests,
    required this.totalRevenue,
  });

  factory DailyStats.fromJson(Map<String, dynamic> json) {
    return DailyStats(
      date: DateTime.parse(json['date']),
      totalRequests: json['total_requests'] ?? 0,
      deliveryRequests: json['delivery_requests'] ?? 0,
      buyForMeRequests: json['buy_for_me_requests'] ?? 0,
      technicianRequests: json['technician_requests'] ?? 0,
      completedRequests: json['completed_requests'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
    );
  }
}

class ServiceAreaStats {
  final String region;
  final String district;
  final int requestCount;
  final int activeAgents;
  final int activeTechnicians;
  final double averageRating;

  ServiceAreaStats({
    required this.region,
    required this.district,
    required this.requestCount,
    required this.activeAgents,
    required this.activeTechnicians,
    required this.averageRating,
  });

  factory ServiceAreaStats.fromJson(Map<String, dynamic> json) {
    return ServiceAreaStats(
      region: json['region'] ?? '',
      district: json['district'] ?? '',
      requestCount: json['request_count'] ?? 0,
      activeAgents: json['active_agents'] ?? 0,
      activeTechnicians: json['active_technicians'] ?? 0,
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
    );
  }
}

class AgentStats {
  final String agentId;
  final String fullName;
  final int totalDeliveries;
  final double totalEarnings;
  final double rating;
  final bool isActive;
  final DateTime lastActive;

  AgentStats({
    required this.agentId,
    required this.fullName,
    required this.totalDeliveries,
    required this.totalEarnings,
    required this.rating,
    required this.isActive,
    required this.lastActive,
  });

  factory AgentStats.fromJson(Map<String, dynamic> json) {
    return AgentStats(
      agentId: json['agent_id'],
      fullName: json['full_name'] ?? '',
      totalDeliveries: json['total_deliveries'] ?? 0,
      totalEarnings: (json['total_earnings'] ?? 0.0).toDouble(),
      rating: (json['rating'] ?? 0.0).toDouble(),
      isActive: json['is_active'] ?? false,
      lastActive: DateTime.parse(json['last_active']),
    );
  }
}

class AdminAnalytics {
  final int totalUsers;
  final int totalAgents;
  final int totalTechnicians;
  final int activeRequests;
  final int completedRequestsToday;
  final double totalRevenueToday;
  final List<DailyStats> dailyStats;
  final List<ServiceAreaStats> topServiceAreas;
  final List<AgentStats> topAgents;

  AdminAnalytics({
    required this.totalUsers,
    required this.totalAgents,
    required this.totalTechnicians,
    required this.activeRequests,
    required this.completedRequestsToday,
    required this.totalRevenueToday,
    required this.dailyStats,
    required this.topServiceAreas,
    required this.topAgents,
  });

  factory AdminAnalytics.fromJson(Map<String, dynamic> json) {
    return AdminAnalytics(
      totalUsers: json['total_users'] ?? 0,
      totalAgents: json['total_agents'] ?? 0,
      totalTechnicians: json['total_technicians'] ?? 0,
      activeRequests: json['active_requests'] ?? 0,
      completedRequestsToday: json['completed_requests_today'] ?? 0,
      totalRevenueToday: (json['total_revenue_today'] ?? 0.0).toDouble(),
      dailyStats: (json['daily_stats'] as List<dynamic>?)
          ?.map((item) => DailyStats.fromJson(item))
          .toList() ?? [],
      topServiceAreas: (json['top_service_areas'] as List<dynamic>?)
          ?.map((item) => ServiceAreaStats.fromJson(item))
          .toList() ?? [],
      topAgents: (json['top_agents'] as List<dynamic>?)
          ?.map((item) => AgentStats.fromJson(item))
          .toList() ?? [],
    );
  }
}