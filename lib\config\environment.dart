import 'package:flutter_dotenv/flutter_dotenv.dart';

class Environment {
  // Backend Configuration (Backend-First Architecture)
  static String get backendApiUrl => dotenv.env['BACKEND_API_URL'] ?? 'https://hhlazpbeoqoknaqwxptx.supabase.co/functions/v1';
  
  // Supabase Configuration (Auth State Management Only)
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? 'https://hhlazpbeoqoknaqwxptx.supabase.co';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhobGF6cGJlb3Fva25hcXd4cHR4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDUwMzcsImV4cCI6MjA2ODQyMTAzN30.AlQcAxnGtkYuCWV8hYf18x6hWD5sulMi8IyRqbeEdFE';
  
  // Legacy API Configuration (Deprecated - Use Backend API)
  @Deprecated('Use backendApiUrl instead')
  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? '';
  
  // Google Sign-In Configuration
  static String get googleClientIdAndroid => dotenv.env['GOOGLE_CLIENT_ID_ANDROID'] ?? '';
  static String get googleClientIdIos => dotenv.env['GOOGLE_CLIENT_ID_IOS'] ?? '';
  
  // Google AI Configuration
  static String get googleAIApiKey => dotenv.env['GOOGLE_AI_API_KEY'] ?? '';
  
  // Architecture Configuration
  static bool get useBackendFirst => dotenv.env['USE_BACKEND_FIRST']?.toLowerCase() == 'true' || true;
  static bool get enableRealTimeUpdates => dotenv.env['ENABLE_REALTIME']?.toLowerCase() == 'true' || false;
  static bool get enableOfflineMode => dotenv.env['ENABLE_OFFLINE']?.toLowerCase() == 'true' || false;
  
  // Development Configuration
  static bool get enableApiLogging => dotenv.env['ENABLE_API_LOGGING']?.toLowerCase() == 'true' || false;
  static bool get enablePerformanceMonitoring => dotenv.env['ENABLE_PERFORMANCE_MONITORING']?.toLowerCase() == 'true' || false;
  
  // Helper method to get any environment variable
  static String getEnv(String key, {String defaultValue = ''}) {
    return dotenv.env[key] ?? defaultValue;
  }
  
  // Helper method to check if environment variable exists
  static bool hasEnv(String key) {
    return dotenv.env.containsKey(key);
  }
  
  // Helper method to get required environment variable (throws error if not found)
  static String getRequiredEnv(String key) {
    final value = dotenv.env[key];
    if (value == null || value.isEmpty) {
      throw Exception('Required environment variable $key is not set');
    }
    return value;
  }
}