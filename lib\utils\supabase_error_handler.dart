import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseErrorHandler {
  static String getErrorMessage(dynamic error) {
    if (error is PostgrestException) {
      // Handle specific PostgrestException cases
      if (error.message.contains('Could not find a relationship')) {
        return 'Database relationship error. Please contact support.';
      }
      if (error.message.contains('foreign key constraint')) {
        return 'Data integrity error. Please try again.';
      }
      if (error.message.contains('permission denied')) {
        return 'You don\'t have permission to access this data.';
      }
      return 'Database error: ${error.message}';
    }
    
    if (error is AuthException) {
      return 'Authentication error: ${error.message}';
    }
    
    return error.toString();
  }

  static void showErrorSnackBar(BuildContext context, dynamic error) {
    final message = getErrorMessage(error);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  static void logError(String context, dynamic error, [StackTrace? stackTrace]) {
    print('ERROR in $context: ${getErrorMessage(error)}');
    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }

  static bool isRelationshipError(dynamic error) {
    return error is PostgrestException && 
           error.message.contains('Could not find a relationship');
  }

  static bool isPermissionError(dynamic error) {
    return error is PostgrestException && 
           error.message.contains('permission denied');
  }
}