-- M-<PERSON> App Row Level Security Policies
-- This file creates security policies for all tables to control data access

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE technicians ENABLE ROW LEVEL SECURITY;
ALTER TABLE technician_skills ENABLE ROW LEVEL SECURITY;
ALTER TABLE technician_portfolio ENABLE ROW LEVEL SECURITY;
ALTER TABLE requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE request_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_balances ENABLE ROW LEVEL SECURITY;

-- Users table policies
-- IMPORTANT: Set `WITH CHECK (true)` for users table as per Supabase instructions
CREATE POLICY "Users can be created during signup" ON users
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (true);

-- Allow authenticated users to view basic user info for service providers
CREATE POLICY "Authenticated users can view service provider profiles" ON users
    FOR SELECT
    USING (
        auth.role() = 'authenticated' AND 
        role IN ('agent', 'technician')
    );

-- Admins can manage all users
CREATE POLICY "Admins can manage all users" ON users
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Agents table policies
CREATE POLICY "Agents can manage their own profile" ON agents
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.id = agents.user_id
        )
    )
    WITH CHECK (true);

-- Allow authenticated users to view available agents
CREATE POLICY "Authenticated users can view available agents" ON agents
    FOR SELECT
    USING (auth.role() = 'authenticated' AND is_available = true AND is_verified = true);

-- Admins can manage all agents
CREATE POLICY "Admins can manage all agents" ON agents
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Technicians table policies
CREATE POLICY "Technicians can manage their own profile" ON technicians
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.id = technicians.user_id
        )
    )
    WITH CHECK (true);

-- Allow authenticated users to view available technicians
CREATE POLICY "Authenticated users can view available technicians" ON technicians
    FOR SELECT
    USING (auth.role() = 'authenticated' AND is_available = true AND status = 'active');

-- Admins can manage all technicians
CREATE POLICY "Admins can manage all technicians" ON technicians
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Technician Skills table policies
CREATE POLICY "Technicians can manage their own skills" ON technician_skills
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM technicians 
            WHERE technicians.id = technician_skills.technician_id 
            AND technicians.user_id = auth.uid()
        )
    )
    WITH CHECK (true);

CREATE POLICY "Authenticated users can view technician skills" ON technician_skills
    FOR SELECT
    USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage all technician skills" ON technician_skills
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Technician Portfolio table policies
CREATE POLICY "Technicians can manage their own portfolio" ON technician_portfolio
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM technicians 
            WHERE technicians.id = technician_portfolio.technician_id 
            AND technicians.user_id = auth.uid()
        )
    )
    WITH CHECK (true);

CREATE POLICY "Authenticated users can view technician portfolios" ON technician_portfolio
    FOR SELECT
    USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage all technician portfolios" ON technician_portfolio
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Requests table policies (unified table)
CREATE POLICY "Users can create requests" ON requests
    FOR INSERT
    USING (auth.uid() = customer_id)
    WITH CHECK (true);

CREATE POLICY "Customers can manage their own requests" ON requests
    FOR ALL
    USING (auth.uid() = customer_id)
    WITH CHECK (true);

CREATE POLICY "Agents can manage assigned requests" ON requests
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM agents 
            WHERE agents.user_id = auth.uid() 
            AND agents.id = requests.agent_id
        )
    )
    WITH CHECK (true);

CREATE POLICY "Technicians can manage assigned requests" ON requests
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM technicians 
            WHERE technicians.user_id = auth.uid() 
            AND technicians.id = requests.technician_id
        )
    )
    WITH CHECK (true);

CREATE POLICY "Service providers can view available requests" ON requests
    FOR SELECT
    USING (
        auth.role() = 'authenticated' AND 
        status = 'pending' AND
        (
            (type IN ('delivery', 'buyForMe') AND EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'agent')) OR
            (type = 'technicianService' AND EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'technician'))
        )
    );

CREATE POLICY "Admins can manage all requests" ON requests
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Request Updates table policies
CREATE POLICY "Authenticated users can view request updates for their requests" ON request_updates
    FOR SELECT
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM requests 
            WHERE requests.id = request_updates.request_id 
            AND (
                requests.customer_id = auth.uid() OR
                EXISTS (SELECT 1 FROM agents WHERE agents.user_id = auth.uid() AND agents.id = requests.agent_id) OR
                EXISTS (SELECT 1 FROM technicians WHERE technicians.user_id = auth.uid() AND technicians.id = requests.technician_id)
            )
        )
    );

CREATE POLICY "Service providers can create request updates" ON request_updates
    FOR INSERT
    USING (
        EXISTS (
            SELECT 1 FROM requests 
            WHERE requests.id = request_updates.request_id 
            AND (
                EXISTS (SELECT 1 FROM agents WHERE agents.user_id = auth.uid() AND agents.id = requests.agent_id) OR
                EXISTS (SELECT 1 FROM technicians WHERE technicians.user_id = auth.uid() AND technicians.id = requests.technician_id)
            )
        )
    )
    WITH CHECK (true);

CREATE POLICY "Admins can manage all request updates" ON request_updates
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Chats table policies
CREATE POLICY "Users can view chats they're part of" ON chats
    FOR SELECT
    USING (auth.uid() = user1_id OR auth.uid() = user2_id);

CREATE POLICY "Users can create chats with other users" ON chats
    FOR INSERT
    USING (auth.uid() = user1_id OR auth.uid() = user2_id)
    WITH CHECK (true);

CREATE POLICY "Users can update chats they're part of" ON chats
    FOR UPDATE
    USING (auth.uid() = user1_id OR auth.uid() = user2_id)
    WITH CHECK (true);

CREATE POLICY "Admins can manage all chats" ON chats
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Messages table policies
CREATE POLICY "Users can view messages in their chats" ON messages
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM chats 
            WHERE chats.id = messages.chat_id 
            AND (chats.user1_id = auth.uid() OR chats.user2_id = auth.uid())
        )
    );

CREATE POLICY "Users can send messages in their chats" ON messages
    FOR INSERT
    USING (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM chats 
            WHERE chats.id = messages.chat_id 
            AND (chats.user1_id = auth.uid() OR chats.user2_id = auth.uid())
        )
    )
    WITH CHECK (true);

CREATE POLICY "Users can update messages they sent" ON messages
    FOR UPDATE
    USING (auth.uid() = sender_id)
    WITH CHECK (true);

CREATE POLICY "Admins can manage all messages" ON messages
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Notifications table policies
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications for users" ON notifications
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (true);

CREATE POLICY "Admins can manage all notifications" ON notifications
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Reviews table policies
CREATE POLICY "Users can view reviews for requests they're involved in" ON reviews
    FOR SELECT
    USING (
        auth.role() = 'authenticated' AND
        (
            auth.uid() = reviewer_id OR 
            auth.uid() = reviewee_id OR
            EXISTS (
                SELECT 1 FROM requests 
                WHERE requests.id = reviews.request_id 
                AND requests.customer_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create reviews for completed requests" ON reviews
    FOR INSERT
    USING (
        auth.uid() = reviewer_id AND
        EXISTS (
            SELECT 1 FROM requests 
            WHERE requests.id = reviews.request_id 
            AND requests.status = 'completed'
            AND (
                requests.customer_id = auth.uid() OR
                EXISTS (SELECT 1 FROM agents WHERE agents.user_id = auth.uid() AND agents.id = requests.agent_id) OR
                EXISTS (SELECT 1 FROM technicians WHERE technicians.user_id = auth.uid() AND technicians.id = requests.technician_id)
            )
        )
    )
    WITH CHECK (true);

CREATE POLICY "Admins can manage all reviews" ON reviews
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Payments table policies
CREATE POLICY "Users can view payments they're involved in" ON payments
    FOR SELECT
    USING (
        auth.uid() = payer_id OR 
        auth.uid() = payee_id OR
        EXISTS (
            SELECT 1 FROM requests 
            WHERE requests.id = payments.request_id 
            AND requests.customer_id = auth.uid()
        )
    );

CREATE POLICY "System can create payments" ON payments
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "System can update payment status" ON payments
    FOR UPDATE
    WITH CHECK (true);

CREATE POLICY "Admins can manage all payments" ON payments
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- Wallet Transactions table policies
CREATE POLICY "Users can view their own wallet transactions" ON wallet_transactions
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "System can create wallet transactions" ON wallet_transactions
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Admins can manage all wallet transactions" ON wallet_transactions
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);

-- User Balances table policies
CREATE POLICY "Users can view their own balance" ON user_balances
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "System can create and update user balances" ON user_balances
    FOR INSERT
    WITH CHECK (true);

CREATE POLICY "System can update user balances" ON user_balances
    FOR UPDATE
    WITH CHECK (true);

CREATE POLICY "Admins can manage all user balances" ON user_balances
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() AND users.role = 'admin'
        )
    )
    WITH CHECK (true);