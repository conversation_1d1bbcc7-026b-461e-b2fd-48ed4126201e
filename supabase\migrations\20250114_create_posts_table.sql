-- Create posts table for admin announcements
CREATE TABLE IF NOT EXISTS posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    image_url TEXT,
    target_role VARCHAR(20) DEFAULT 'all' CHECK (target_role IN ('all', 'customer', 'agent', 'technician')),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Add updated_at trigger
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_target_role ON posts(target_role);
CREATE INDEX IF NOT EXISTS idx_posts_created_by ON posts(created_by);
CREATE INDEX IF NOT EXISTS idx_posts_start_date ON posts(start_date);
CREATE INDEX IF NOT EXISTS idx_posts_end_date ON posts(end_date);

-- Enable RLS
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

-- Allow admin full access
CREATE POLICY "Allow admin full access" ON posts FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin')
);

-- Allow users to read published posts targeted to their role or 'all'
CREATE POLICY "Allow users to read published posts" ON posts FOR SELECT USING (
    status = 'published' AND
    (target_role = 'all' OR EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() AND users.role = posts.target_role
    )) AND
    (start_date IS NULL OR start_date <= NOW()) AND
    (end_date IS NULL OR end_date >= NOW())
);