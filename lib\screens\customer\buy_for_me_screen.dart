import 'package:flutter/material.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/widgets/app_logo.dart';
import 'package:mlink/screens/maps/location_picker_screen.dart';
import 'package:mlink/services/maps_service.dart';

class BuyForMeScreen extends StatefulWidget {
  const BuyForMeScreen({super.key});

  @override
  State<BuyForMeScreen> createState() => _BuyForMeScreenState();
}

class _BuyForMeScreenState extends State<BuyForMeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _storeLocationController = TextEditingController();
  final _deliveryAddressController = TextEditingController();
  final _maxBudgetController = TextEditingController();
  final _preferredBrandsController = TextEditingController();
  final _notesController = TextEditingController();
  
  final MapsService _mapsService = MapsService.instance;
  LocationPoint? _storeLocation;
  LocationPoint? _deliveryLocation;

  PaymentMethod _selectedPayment = PaymentMethod.cash;
  bool _allowSubstitutes = true;
  bool _needAdvance = false;
  bool _isScheduled = false;
  DateTime? _scheduledDate;
  TimeOfDay? _scheduledTime;

  List<String> _items = [''];

  @override
  void dispose() {
    _descriptionController.dispose();
    _storeLocationController.dispose();
    _deliveryAddressController.dispose();
    _maxBudgetController.dispose();
    _preferredBrandsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Buy For Me'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: AppLogo(
              width: AppLogoSizes.small,
              height: AppLogoSizes.small,
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.shopping_cart,
                      color: Theme.of(context).colorScheme.secondary,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Buy For Me Service',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Get groceries and essentials delivered to your door',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Store Location
              GestureDetector(
                onTap: () => _selectLocation(isStore: true),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.store_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Store/Market Location',
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _storeLocation?.address ?? 'Tap to select store location',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: _storeLocation?.address != null 
                                    ? Theme.of(context).colorScheme.onSurface
                                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                              ),
                            ),
                            if (_storeLocation?.address == null)
                              Text(
                                'Where should we shop for you?',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                                ),
                              ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.map_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Delivery Address
              GestureDetector(
                onTap: () => _selectLocation(isStore: false),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Delivery Address',
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _deliveryLocation?.address ?? 'Tap to select delivery location',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: _deliveryLocation?.address != null 
                                    ? Theme.of(context).colorScheme.onSurface
                                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.my_location),
                            onPressed: () => _getCurrentLocation(),
                            tooltip: 'Use current location',
                          ),
                          Icon(
                            Icons.map_outlined,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Shopping List
              Text(
                'Shopping List',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              ...List.generate(_items.length, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: _items[index],
                          decoration: InputDecoration(
                            labelText: 'Item ${index + 1}',
                            prefixIcon: Icon(
                              Icons.shopping_basket_outlined,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onChanged: (value) {
                            _items[index] = value;
                          },
                          validator: (value) {
                            if (index == 0 && (value == null || value.trim().isEmpty)) {
                              return 'Please enter at least one item';
                            }
                            return null;
                          },
                        ),
                      ),
                      if (_items.length > 1)
                        IconButton(
                          icon: const Icon(Icons.remove_circle_outline),
                          onPressed: () {
                            setState(() => _items.removeAt(index));
                          },
                        ),
                    ],
                  ),
                );
              }),
              
              OutlinedButton.icon(
                onPressed: () {
                  setState(() => _items.add(''));
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Item'),
              ),
              const SizedBox(height: 24),

              // Budget
              TextFormField(
                controller: _maxBudgetController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Maximum Budget (TSh)',
                  prefixIcon: Icon(
                    Icons.account_balance_wallet_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'Optional: Set a spending limit',
                ),
              ),
              const SizedBox(height: 16),

              // Preferred Brands
              TextFormField(
                controller: _preferredBrandsController,
                decoration: InputDecoration(
                  labelText: 'Preferred Brands (Optional)',
                  prefixIcon: Icon(
                    Icons.verified_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'e.g., Coca-Cola, Azam, Colgate',
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 24),

              // Options
              Text(
                'Shopping Options',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              CheckboxListTile(
                title: const Text('Allow substitutes'),
                subtitle: const Text('Let agent choose alternatives if items unavailable'),
                value: _allowSubstitutes,
                onChanged: (value) {
                  setState(() => _allowSubstitutes = value ?? true);
                },
              ),
              
              CheckboxListTile(
                title: const Text('Need advance payment'),
                subtitle: const Text('Request money upfront for shopping'),
                value: _needAdvance,
                onChanged: (value) {
                  setState(() => _needAdvance = value ?? false);
                },
              ),
              
              CheckboxListTile(
                title: const Text('Schedule for later'),
                value: _isScheduled,
                onChanged: (value) {
                  setState(() => _isScheduled = value ?? false);
                },
              ),

              if (_isScheduled) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _selectDate(context),
                        icon: const Icon(Icons.calendar_today),
                        label: Text(
                          _scheduledDate != null
                              ? '${_scheduledDate!.day}/${_scheduledDate!.month}/${_scheduledDate!.year}'
                              : 'Select Date',
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _selectTime(context),
                        icon: const Icon(Icons.access_time),
                        label: Text(
                          _scheduledTime != null
                              ? _scheduledTime!.format(context)
                              : 'Select Time',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 24),

              // Special Instructions
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: 'Special Instructions (Optional)',
                  prefixIcon: Icon(
                    Icons.note_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'Any specific requirements or notes',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              // Payment Method
              Text(
                'Payment Method',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: PaymentMethod.values.map((method) {
                    return RadioListTile<PaymentMethod>(
                      title: Text(_getPaymentDisplayName(method)),
                      value: method,
                      groupValue: _selectedPayment,
                      onChanged: (value) {
                        setState(() => _selectedPayment = value!);
                      },
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 24),

              // Info Banner
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'How it works',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '1. Agent will contact you to confirm items and budget\n'
                      '2. Agent shops for your items\n'
                      '3. Agent delivers to your specified address\n'
                      '4. You pay for items + delivery fee',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                height: 56,
                child: ElevatedButton(
                  onPressed: _submitRequest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                    foregroundColor: Theme.of(context).colorScheme.onSecondary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Submit Request',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 7)),
    );
    if (picked != null) {
      setState(() => _scheduledDate = picked);
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() => _scheduledTime = picked);
    }
  }

  Future<void> _selectLocation({required bool isStore}) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LocationPickerScreen(
          title: isStore ? 'Select Store Location' : 'Select Delivery Location',
          initialLocation: isStore ? _storeLocation : _deliveryLocation,
        ),
      ),
    );
    
    if (result != null && result is LocationPoint) {
      setState(() {
        if (isStore) {
          _storeLocation = result;
          _storeLocationController.text = result.address ?? '';
        } else {
          _deliveryLocation = result;
          _deliveryAddressController.text = result.address ?? '';
        }
      });
    }
  }
  
  Future<void> _getCurrentLocation() async {
    final currentLocation = await _mapsService.getCurrentLocationPoint();
    if (currentLocation != null) {
      setState(() {
        _deliveryLocation = currentLocation;
        _deliveryAddressController.text = currentLocation.address ?? '';
      });
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to get current location. Please check permissions.'),
          ),
        );
      }
    }
  }

  void _submitRequest() {
    if (!_formKey.currentState!.validate()) return;

    final nonEmptyItems = _items.where((item) => item.trim().isNotEmpty).toList();
    if (nonEmptyItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least one item to your shopping list'),
        ),
      );
      return;
    }

    if (_storeLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a store location'),
        ),
      );
      return;
    }

    if (_deliveryLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a delivery location'),
        ),
      );
      return;
    }

    if (_isScheduled && (_scheduledDate == null || _scheduledTime == null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select date and time for scheduled shopping'),
        ),
      );
      return;
    }

    // TODO: Submit buy for me request
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Buy for me request submitted successfully!'),
      ),
    );
    Navigator.of(context).pop();
  }

  String _getPaymentDisplayName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return '💵 Cash on Delivery';
      case PaymentMethod.mobileMoney:
        return '📱 Mobile Money';
      case PaymentMethod.card:
        return '💳 Card';
      case PaymentMethod.wallet:
        return '🏦 M-Link Wallet';
    }
  }
}