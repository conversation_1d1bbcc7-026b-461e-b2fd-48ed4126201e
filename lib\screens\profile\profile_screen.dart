import 'package:flutter/material.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/screens/auth/login_screen.dart';
import 'package:mlink/models/user.dart' as app_user;
import 'package:mlink/widgets/profile_components.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AuthService.instance.authStateStream,
      initialData: AuthService.instance.isLoggedIn,
      builder: (context, snapshot) {
        final isLoggedIn = snapshot.data ?? false;
        final user = AuthService.instance.currentUser;
        
        if (!isLoggedIn || user == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Profile'),
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.account_circle_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Please log in to view your profile',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        }

        switch (user.role) {
          case app_user.UserRole.customer:
            return const CustomerProfileScreen();
          case app_user.UserRole.agent:
            return const AgentProfileScreen();
          case app_user.UserRole.technician:
            return const TechnicianProfileScreen();
          case app_user.UserRole.admin:
            return const AdminProfileScreen();
          default:
            return const CustomerProfileScreen();
        }
      },
    );
  }
}

class CustomerProfileScreen extends StatefulWidget {
  const CustomerProfileScreen({super.key});

  @override
  State<CustomerProfileScreen> createState() => _CustomerProfileScreenState();
}

class _CustomerProfileScreenState extends State<CustomerProfileScreen> {
  Map<String, dynamic> _userStats = {};
  Map<String, dynamic> _activitySummary = {};
  Map<String, dynamic> _userSettings = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProfileData();
  }

  Future<void> _loadProfileData() async {
    try {
      setState(() => _isLoading = true);
      
      final user = AuthService.instance.currentUser;
      if (user != null) {
        // Load all profile data in parallel
        final results = await Future.wait([
          _loadUserStats(user.id),
          _loadActivitySummary(user.id),
          _loadUserSettings(user.id),
        ]);
        
        setState(() => _isLoading = false);
      }
    } catch (e) {
      print('Error loading profile data: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadUserStats(String userId) async {
    try {
      final response = await BackendApiService.getUserAnalytics(userId);
      if (response['success'] == true && response['analytics'] != null) {
        _userStats = response['analytics'];
      }
    } catch (e) {
      print('Error loading user stats: $e');
    }
  }

  Future<void> _loadActivitySummary(String userId) async {
    try {
      final response = await BackendApiService.getUserActivitySummary(userId, 'month');
      if (response['success'] == true && response['summary'] != null) {
        _activitySummary = response['summary'];
      }
    } catch (e) {
      print('Error loading activity summary: $e');
    }
  }

  Future<void> _loadUserSettings(String userId) async {
    try {
      final response = await BackendApiService.getUserSettings(userId);
      if (response['success'] == true && response['settings'] != null) {
        _userSettings = response['settings'];
      }
    } catch (e) {
      print('Error loading user settings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;
    
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Profile')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // TODO: Navigate to edit profile screen
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Enhanced Profile Header Card
            _buildEnhancedProfileHeader(user),
            const SizedBox(height: 24),
            
            // Activity Summary Section
            if (_activitySummary.isNotEmpty) ...[
              ProfileSection(
                title: 'Activity Summary',
                icon: Icons.trending_up,
                children: [
                  ProfileListItem(
                    icon: Icons.assignment,
                    title: 'Total Requests',
                    subtitle: '${_activitySummary['total_requests'] ?? 0} requests made',
                  ),
                  ProfileListItem(
                    icon: Icons.check_circle,
                    title: 'Completed Requests',
                    subtitle: '${_activitySummary['completed_requests'] ?? 0} successful completions',
                  ),
                  ProfileListItem(
                    icon: Icons.star,
                    title: 'Average Rating Given',
                    subtitle: '${(_activitySummary['avg_rating_given'] ?? 0.0).toStringAsFixed(1)} stars',
                  ),
                  ProfileListItem(
                    icon: Icons.account_balance_wallet,
                    title: 'Total Spent',
                    subtitle: 'UGX ${_activitySummary['total_spent'] ?? 0}',
                  ),
                ],
              ),
              const SizedBox(height: 24),
            ],
            
            // Account Information Section
            ProfileSection(
              title: 'Account Information',
              icon: Icons.person_outline,
              children: [
                ProfileListItem(
                  icon: Icons.email_outlined,
                  title: 'Email',
                  subtitle: user?.email ?? 'Not provided',
                ),
                ProfileListItem(
                  icon: Icons.phone_outlined,
                  title: 'Phone Number',
                  subtitle: user?.phoneNumber ?? 'Not provided',
                  onTap: () {
                    // TODO: Edit phone number
                  },
                ),
                ProfileListItem(
                  icon: Icons.calendar_today_outlined,
                  title: 'Member Since',
                  subtitle: user?.createdAt != null 
                      ? '${user!.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}'
                      : 'Unknown',
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Settings Section
            ProfileSection(
              title: 'Settings',
              icon: Icons.settings_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Manage your notifications',
                  onTap: () {
                    // TODO: Navigate to notifications settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.security_outlined,
                  title: 'Privacy & Security',
                  subtitle: 'Manage your privacy settings',
                  onTap: () {
                    // TODO: Navigate to privacy settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.language_outlined,
                  title: 'Language',
                  subtitle: _userSettings['language'] ?? 'English',
                  onTap: () {
                    // TODO: Navigate to language settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.dark_mode_outlined,
                  title: 'Theme',
                  subtitle: _userSettings['theme'] ?? 'System',
                  onTap: () {
                    // TODO: Navigate to theme settings
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Support Section
            ProfileSection(
              title: 'Support',
              icon: Icons.support_agent_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.help_outline,
                  title: 'Help Center',
                  subtitle: 'Get help and support',
                  onTap: () {
                    // TODO: Navigate to help center
                  },
                ),
                ProfileListItem(
                  icon: Icons.feedback_outlined,
                  title: 'Send Feedback',
                  subtitle: 'Help us improve the app',
                  onTap: () {
                    // TODO: Navigate to feedback form
                  },
                ),
                ProfileListItem(
                  icon: Icons.info_outline,
                  title: 'About',
                  subtitle: 'Learn more about M-Link',
                  onTap: () {
                    // TODO: Navigate to about screen
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await _showLogoutDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.logout, color: Theme.of(context).colorScheme.onError),
                    const SizedBox(width: 8),
                    Text(
                      'Logout',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onError,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await AuthService.instance.logout();
      // Navigation will be handled automatically by the main AuthWrapper
      // due to the reactive StreamBuilder listening to auth state changes
    }
  }

  Widget _buildEnhancedProfileHeader(app_user.User? user) {
    return ProfileHeaderCard(
      user: user,
      stats: _userStats.isNotEmpty ? _userStats : null,
    );
  }
}

class AgentProfileScreen extends StatefulWidget {
  const AgentProfileScreen({super.key});

  @override
  State<AgentProfileScreen> createState() => _AgentProfileScreenState();
}

class _AgentProfileScreenState extends State<AgentProfileScreen> {
  Map<String, dynamic> _agentProfile = {};
  Map<String, dynamic> _performanceMetrics = {};
  bool _isLoading = true;
  bool _isAvailable = true;

  @override
  void initState() {
    super.initState();
    _loadAgentProfile();
  }

  Future<void> _loadAgentProfile() async {
    try {
      setState(() => _isLoading = true);
      
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final results = await Future.wait([
          _loadAgentData(user.id),
          _loadPerformanceMetrics(user.id),
        ]);
        
        setState(() => _isLoading = false);
      }
    } catch (e) {
      print('Error loading agent profile: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadAgentData(String userId) async {
    try {
      final response = await BackendApiService.getAgentProfile(userId);
      if (response['success'] == true && response['profile'] != null) {
        setState(() {
          _agentProfile = response['profile'];
          _isAvailable = _agentProfile['is_available'] ?? true;
        });
      }
    } catch (e) {
      print('Error loading agent data: $e');
    }
  }

  Future<void> _loadPerformanceMetrics(String userId) async {
    try {
      final response = await BackendApiService.getAgentPerformanceMetrics(userId);
      if (response['success'] == true && response['metrics'] != null) {
        setState(() {
          _performanceMetrics = response['metrics'];
        });
      }
    } catch (e) {
      print('Error loading performance metrics: $e');
    }
  }

  Future<void> _toggleAvailability(bool value) async {
    try {
      final user = AuthService.instance.currentUser;
      if (user != null) {
        await BackendApiService.updateAgentAvailability(user.id, value);
        setState(() {
          _isAvailable = value;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Availability updated to ${value ? "Available" : "Unavailable"}'),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update availability: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;
    
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Agent Profile')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Agent Profile'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // TODO: Navigate to edit profile screen
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Enhanced Profile Header for Agent
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.secondary,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.white,
                    child: Text(
                      user?.fullName?.substring(0, 1).toUpperCase() ?? 'A',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    user?.fullName ?? 'Agent',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.verified, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Verified Agent',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.star, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          '${(_performanceMetrics['average_rating'] ?? 4.8).toStringAsFixed(1)} Rating • ${_performanceMetrics['completed_jobs'] ?? 147} Jobs',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Agent Status Section
            ProfileSection(
              title: 'Agent Status',
              icon: Icons.work_outline,
              children: [
                ProfileListItem(
                  icon: Icons.toggle_on,
                  title: 'Availability Status',
                  subtitle: _isAvailable ? 'Currently Available' : 'Currently Unavailable',
                  trailing: Switch(
                    value: _isAvailable,
                    onChanged: _toggleAvailability,
                  ),
                ),
                ProfileListItem(
                  icon: Icons.location_on_outlined,
                  title: 'Service Area',
                  subtitle: 'Kampala, Uganda',
                  onTap: () {
                    // TODO: Edit service area
                  },
                ),
                ProfileListItem(
                  icon: Icons.motorcycle,
                  title: 'Vehicle Details',
                  subtitle: 'Motorcycle • UBA 123K',
                  onTap: () {
                    // TODO: Edit vehicle details
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Performance Section
            ProfileSection(
              title: 'Performance',
              icon: Icons.trending_up,
              children: [
                ProfileListItem(
                  icon: Icons.star_outline,
                  title: 'Average Rating',
                  subtitle: '4.8 out of 5 stars',
                ),
                ProfileListItem(
                  icon: Icons.check_circle_outline,
                  title: 'Completed Jobs',
                  subtitle: '147 successful deliveries',
                ),
                ProfileListItem(
                  icon: Icons.speed,
                  title: 'Response Time',
                  subtitle: 'Average 8 minutes',
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Account Settings
            ProfileSection(
              title: 'Account Settings',
              icon: Icons.settings_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.account_balance_wallet_outlined,
                  title: 'Payment Settings',
                  subtitle: 'Manage payment methods',
                  onTap: () {
                    // TODO: Navigate to payment settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Job alerts and updates',
                  onTap: () {
                    // TODO: Navigate to notification settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.security_outlined,
                  title: 'Privacy & Security',
                  subtitle: 'Account security settings',
                  onTap: () {
                    // TODO: Navigate to security settings
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Support Section
            ProfileSection(
              title: 'Support',
              icon: Icons.support_agent_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.help_outline,
                  title: 'Agent Help Center',
                  subtitle: 'Get help with deliveries',
                  onTap: () {
                    // TODO: Navigate to agent help
                  },
                ),
                ProfileListItem(
                  icon: Icons.policy_outlined,
                  title: 'Agent Policies',
                  subtitle: 'Terms and guidelines',
                  onTap: () {
                    // TODO: Navigate to policies
                  },
                ),
                ProfileListItem(
                  icon: Icons.feedback_outlined,
                  title: 'Feedback',
                  subtitle: 'Help improve the platform',
                  onTap: () {
                    // TODO: Navigate to feedback
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await _showLogoutDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.logout, color: Theme.of(context).colorScheme.onError),
                    const SizedBox(width: 8),
                    Text(
                      'Logout',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onError,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await AuthService.instance.logout();
      // Navigation will be handled automatically by the main AuthWrapper
      // due to the reactive StreamBuilder listening to auth state changes
    }
  }
}

class TechnicianProfileScreen extends StatelessWidget {
  const TechnicianProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Technician Profile'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // TODO: Navigate to edit profile screen
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Enhanced Profile Header for Technician
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.orange,
                    Colors.deepOrange,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.white,
                    child: Icon(
                      Icons.build,
                      size: 40,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    user?.fullName ?? 'Technician',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.verified, color: Colors.white, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Certified Technician',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.star, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          '4.9 Rating • 89 Jobs',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Technician Status Section
            ProfileSection(
              title: 'Technician Status',
              icon: Icons.engineering,
              children: [
                ProfileListItem(
                  icon: Icons.toggle_on,
                  title: 'Availability Status',
                  subtitle: 'Currently Available',
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {
                      // TODO: Toggle availability
                    },
                  ),
                ),
                ProfileListItem(
                  icon: Icons.location_on_outlined,
                  title: 'Service Area',
                  subtitle: 'Kampala Central, Uganda',
                  onTap: () {
                    // TODO: Edit service area
                  },
                ),
                ProfileListItem(
                  icon: Icons.schedule,
                  title: 'Working Hours',
                  subtitle: '8:00 AM - 6:00 PM',
                  onTap: () {
                    // TODO: Edit working hours
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Skills & Certifications
            ProfileSection(
              title: 'Skills & Certifications',
              icon: Icons.workspace_premium,
              children: [
                ProfileListItem(
                  icon: Icons.electrical_services,
                  title: 'Electrical Work',
                  subtitle: 'Licensed Electrician',
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Certified',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                ProfileListItem(
                  icon: Icons.plumbing,
                  title: 'Plumbing',
                  subtitle: 'Professional Plumber',
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Certified',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                ProfileListItem(
                  icon: Icons.ac_unit,
                  title: 'AC Repair',
                  subtitle: 'HVAC Specialist',
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Certified',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                ProfileListItem(
                  icon: Icons.add_circle_outline,
                  title: 'Add Skill',
                  subtitle: 'Expand your expertise',
                  onTap: () {
                    // TODO: Add new skill
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Performance Section
            ProfileSection(
              title: 'Performance',
              icon: Icons.trending_up,
              children: [
                ProfileListItem(
                  icon: Icons.star_outline,
                  title: 'Average Rating',
                  subtitle: '4.9 out of 5 stars',
                ),
                ProfileListItem(
                  icon: Icons.check_circle_outline,
                  title: 'Completed Jobs',
                  subtitle: '89 successful repairs',
                ),
                ProfileListItem(
                  icon: Icons.speed,
                  title: 'Response Time',
                  subtitle: 'Average 15 minutes',
                ),
                ProfileListItem(
                  icon: Icons.handyman,
                  title: 'Success Rate',
                  subtitle: '98% first-time fix',
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Account Settings
            ProfileSection(
              title: 'Account Settings',
              icon: Icons.settings_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.account_balance_wallet_outlined,
                  title: 'Payment Settings',
                  subtitle: 'Manage payment methods',
                  onTap: () {
                    // TODO: Navigate to payment settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Job alerts and updates',
                  onTap: () {
                    // TODO: Navigate to notification settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.security_outlined,
                  title: 'Privacy & Security',
                  subtitle: 'Account security settings',
                  onTap: () {
                    // TODO: Navigate to security settings
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Support Section
            ProfileSection(
              title: 'Support',
              icon: Icons.support_agent_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.help_outline,
                  title: 'Technician Help Center',
                  subtitle: 'Get help with technical issues',
                  onTap: () {
                    // TODO: Navigate to technician help
                  },
                ),
                ProfileListItem(
                  icon: Icons.policy_outlined,
                  title: 'Technician Policies',
                  subtitle: 'Terms and safety guidelines',
                  onTap: () {
                    // TODO: Navigate to policies
                  },
                ),
                ProfileListItem(
                  icon: Icons.feedback_outlined,
                  title: 'Feedback',
                  subtitle: 'Help improve the platform',
                  onTap: () {
                    // TODO: Navigate to feedback
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await _showLogoutDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.logout, color: Theme.of(context).colorScheme.onError),
                    const SizedBox(width: 8),
                    Text(
                      'Logout',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onError,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await AuthService.instance.logout();
      // Navigation will be handled automatically by the main AuthWrapper
      // due to the reactive StreamBuilder listening to auth state changes
    }
  }
}

class AdminProfileScreen extends StatelessWidget {
  const AdminProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Profile'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.admin_panel_settings, size: 64),
            SizedBox(height: 16),
            Text('Admin Profile'),
            SizedBox(height: 8),
            Text('Administrative features coming soon'),
          ],
        ),
      ),
    );
  }
}