import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/models/user.dart';

/// Enhanced profile service with dynamic data integration
class EnhancedProfileService {
  static final EnhancedProfileService _instance = EnhancedProfileService._internal();
  factory EnhancedProfileService() => _instance;
  EnhancedProfileService._internal();

  static EnhancedProfileService get instance => _instance;

  /// Get enhanced profile with dynamic metrics
  Future<EnhancedProfile> getEnhancedProfile(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getEnhancedProfile(userId, userRole.name);
      
      if (response['success'] == true && response['profile'] != null) {
        return EnhancedProfile.fromJson(response['profile']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get enhanced profile');
      }
    } catch (e) {
      throw Exception('Failed to get enhanced profile: $e');
    }
  }

  /// Get user activity summary
  Future<ActivitySummary> getActivitySummary(String userId, String period) async {
    try {
      final response = await BackendApiService.getUserActivitySummary(userId, period);
      
      if (response['success'] == true && response['activity'] != null) {
        return ActivitySummary.fromJson(response['activity']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get activity summary');
      }
    } catch (e) {
      throw Exception('Failed to get activity summary: $e');
    }
  }

  /// Get performance metrics
  Future<PerformanceMetrics> getPerformanceMetrics(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getUserPerformanceMetrics(userId, userRole.name);
      
      if (response['success'] == true && response['metrics'] != null) {
        return PerformanceMetrics.fromJson(response['metrics']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get performance metrics');
      }
    } catch (e) {
      throw Exception('Failed to get performance metrics: $e');
    }
  }

  /// Update profile availability status
  Future<bool> updateAvailability(
    String userId, 
    UserRole userRole, 
    AvailabilityStatus status,
    {String? serviceArea, String? workingHours, Map<String, dynamic>? additionalData}
  ) async {
    try {
      final availabilityData = {
        'status': status.name,
        if (serviceArea != null) 'service_area': serviceArea,
        if (workingHours != null) 'working_hours': workingHours,
        ...?additionalData,
      };

      final response = await BackendApiService.updateProfileAvailability(
        userId, 
        userRole.name, 
        availabilityData
      );
      
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update availability: $e');
    }
  }

  /// Get user settings and preferences
  Future<UserSettings> getUserSettings(String userId) async {
    try {
      final response = await BackendApiService.getUserSettings(userId);
      
      if (response['success'] == true && response['settings'] != null) {
        return UserSettings.fromJson(response['settings']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get user settings');
      }
    } catch (e) {
      throw Exception('Failed to get user settings: $e');
    }
  }

  /// Update user settings
  Future<bool> updateUserSettings(String userId, UserSettings settings) async {
    try {
      final response = await BackendApiService.updateUserSettings(userId, settings.toJson());
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update user settings: $e');
    }
  }

  /// Get profile completion status
  ProfileCompletionStatus getProfileCompletionStatus(User user, EnhancedProfile? profile) {
    int completed = 0;
    int total = 10; // Total fields to check
    
    final completionItems = <CompletionItem>[];

    // Basic profile information
    if (user.fullName?.isNotEmpty == true) {
      completed++;
      completionItems.add(CompletionItem('Full Name', true));
    } else {
      completionItems.add(CompletionItem('Full Name', false));
    }

    if (user.email.isNotEmpty) {
      completed++;
      completionItems.add(CompletionItem('Email Address', true));
    } else {
      completionItems.add(CompletionItem('Email Address', false));
    }

    if (user.phoneNumber?.isNotEmpty == true) {
      completed++;
      completionItems.add(CompletionItem('Phone Number', true));
    } else {
      completionItems.add(CompletionItem('Phone Number', false));
    }

    if (user.profileImageUrl?.isNotEmpty == true || user.avatarUrl?.isNotEmpty == true) {
      completed++;
      completionItems.add(CompletionItem('Profile Picture', true));
    } else {
      completionItems.add(CompletionItem('Profile Picture', false));
    }

    // Role-specific fields
    if (profile != null) {
      if (profile.serviceArea?.isNotEmpty == true) {
        completed++;
        completionItems.add(CompletionItem('Service Area', true));
      } else {
        completionItems.add(CompletionItem('Service Area', false));
      }

      if (profile.workingHours?.isNotEmpty == true) {
        completed++;
        completionItems.add(CompletionItem('Working Hours', true));
      } else {
        completionItems.add(CompletionItem('Working Hours', false));
      }

      if (profile.skills.isNotEmpty) {
        completed++;
        completionItems.add(CompletionItem('Skills & Certifications', true));
      } else {
        completionItems.add(CompletionItem('Skills & Certifications', false));
      }

      if (profile.vehicleDetails?.isNotEmpty == true) {
        completed++;
        completionItems.add(CompletionItem('Vehicle Details', true));
      } else {
        completionItems.add(CompletionItem('Vehicle Details', false));
      }

      if (profile.bio?.isNotEmpty == true) {
        completed++;
        completionItems.add(CompletionItem('Bio/Description', true));
      } else {
        completionItems.add(CompletionItem('Bio/Description', false));
      }

      if (profile.experience > 0) {
        completed++;
        completionItems.add(CompletionItem('Experience Level', true));
      } else {
        completionItems.add(CompletionItem('Experience Level', false));
      }
    } else {
      // If no profile data, mark remaining as incomplete
      for (int i = 0; i < 6; i++) {
        completionItems.add(CompletionItem('Additional Information', false));
      }
    }

    final percentage = (completed / total * 100).round();
    
    return ProfileCompletionStatus(
      percentage: percentage,
      completedItems: completed,
      totalItems: total,
      items: completionItems,
    );
  }
}

/// Enhanced profile model
class EnhancedProfile {
  final String userId;
  final UserRole role;
  final AvailabilityStatus availabilityStatus;
  final String? serviceArea;
  final String? workingHours;
  final String? bio;
  final List<String> skills;
  final Map<String, dynamic> certifications;
  final String? vehicleDetails;
  final int experience; // in months
  final double rating;
  final int totalReviews;
  final bool isVerified;
  final DateTime? verificationDate;
  final DateTime lastActiveAt;
  final Map<String, dynamic> preferences;
  final Map<String, dynamic> metadata;

  EnhancedProfile({
    required this.userId,
    required this.role,
    required this.availabilityStatus,
    this.serviceArea,
    this.workingHours,
    this.bio,
    required this.skills,
    required this.certifications,
    this.vehicleDetails,
    required this.experience,
    required this.rating,
    required this.totalReviews,
    required this.isVerified,
    this.verificationDate,
    required this.lastActiveAt,
    required this.preferences,
    required this.metadata,
  });

  factory EnhancedProfile.fromJson(Map<String, dynamic> json) {
    return EnhancedProfile(
      userId: json['user_id'],
      role: UserRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => UserRole.customer,
      ),
      availabilityStatus: AvailabilityStatus.values.firstWhere(
        (e) => e.name == json['availability_status'],
        orElse: () => AvailabilityStatus.offline,
      ),
      serviceArea: json['service_area'],
      workingHours: json['working_hours'],
      bio: json['bio'],
      skills: List<String>.from(json['skills'] ?? []),
      certifications: Map<String, dynamic>.from(json['certifications'] ?? {}),
      vehicleDetails: json['vehicle_details'],
      experience: json['experience'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      isVerified: json['is_verified'] ?? false,
      verificationDate: json['verification_date'] != null 
          ? DateTime.parse(json['verification_date']) 
          : null,
      lastActiveAt: DateTime.parse(json['last_active_at']),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'role': role.name,
      'availability_status': availabilityStatus.name,
      'service_area': serviceArea,
      'working_hours': workingHours,
      'bio': bio,
      'skills': skills,
      'certifications': certifications,
      'vehicle_details': vehicleDetails,
      'experience': experience,
      'rating': rating,
      'total_reviews': totalReviews,
      'is_verified': isVerified,
      'verification_date': verificationDate?.toIso8601String(),
      'last_active_at': lastActiveAt.toIso8601String(),
      'preferences': preferences,
      'metadata': metadata,
    };
  }
}

/// Activity summary model
class ActivitySummary {
  final String period;
  final int totalActions;
  final int requestsCreated;
  final int requestsCompleted;
  final int messagesExchanged;
  final double averageResponseTime; // in minutes
  final Map<String, int> dailyActivity;
  final List<RecentAction> recentActions;

  ActivitySummary({
    required this.period,
    required this.totalActions,
    required this.requestsCreated,
    required this.requestsCompleted,
    required this.messagesExchanged,
    required this.averageResponseTime,
    required this.dailyActivity,
    required this.recentActions,
  });

  factory ActivitySummary.fromJson(Map<String, dynamic> json) {
    return ActivitySummary(
      period: json['period'] ?? '',
      totalActions: json['total_actions'] ?? 0,
      requestsCreated: json['requests_created'] ?? 0,
      requestsCompleted: json['requests_completed'] ?? 0,
      messagesExchanged: json['messages_exchanged'] ?? 0,
      averageResponseTime: (json['average_response_time'] ?? 0.0).toDouble(),
      dailyActivity: Map<String, int>.from(json['daily_activity'] ?? {}),
      recentActions: (json['recent_actions'] as List? ?? [])
          .map((item) => RecentAction.fromJson(item)).toList(),
    );
  }
}

/// Recent action model
class RecentAction {
  final String id;
  final String type;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  RecentAction({
    required this.id,
    required this.type,
    required this.description,
    required this.timestamp,
    this.metadata,
  });

  factory RecentAction.fromJson(Map<String, dynamic> json) {
    return RecentAction(
      id: json['id'],
      type: json['type'] ?? '',
      description: json['description'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Performance metrics model
class PerformanceMetrics {
  final double overallScore;
  final double responseTimeScore;
  final double qualityScore;
  final double reliabilityScore;
  final double communicationScore;
  final Map<String, double> trendData;
  final List<PerformanceInsight> insights;

  PerformanceMetrics({
    required this.overallScore,
    required this.responseTimeScore,
    required this.qualityScore,
    required this.reliabilityScore,
    required this.communicationScore,
    required this.trendData,
    required this.insights,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) {
    return PerformanceMetrics(
      overallScore: (json['overall_score'] ?? 0.0).toDouble(),
      responseTimeScore: (json['response_time_score'] ?? 0.0).toDouble(),
      qualityScore: (json['quality_score'] ?? 0.0).toDouble(),
      reliabilityScore: (json['reliability_score'] ?? 0.0).toDouble(),
      communicationScore: (json['communication_score'] ?? 0.0).toDouble(),
      trendData: Map<String, double>.from(json['trend_data'] ?? {}),
      insights: (json['insights'] as List? ?? [])
          .map((item) => PerformanceInsight.fromJson(item)).toList(),
    );
  }
}

/// Performance insight model
class PerformanceInsight {
  final String type;
  final String title;
  final String description;
  final String? actionable;
  final InsightPriority priority;

  PerformanceInsight({
    required this.type,
    required this.title,
    required this.description,
    this.actionable,
    required this.priority,
  });

  factory PerformanceInsight.fromJson(Map<String, dynamic> json) {
    return PerformanceInsight(
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      actionable: json['actionable'],
      priority: InsightPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => InsightPriority.low,
      ),
    );
  }
}

/// User settings model
class UserSettings {
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final String language;
  final String timezone;
  final String currency;
  final Map<String, bool> privacySettings;
  final Map<String, dynamic> preferences;

  UserSettings({
    required this.notificationsEnabled,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.pushNotifications,
    required this.language,
    required this.timezone,
    required this.currency,
    required this.privacySettings,
    required this.preferences,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      notificationsEnabled: json['notifications_enabled'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      smsNotifications: json['sms_notifications'] ?? true,
      pushNotifications: json['push_notifications'] ?? true,
      language: json['language'] ?? 'en',
      timezone: json['timezone'] ?? 'UTC',
      currency: json['currency'] ?? 'UGX',
      privacySettings: Map<String, bool>.from(json['privacy_settings'] ?? {}),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notifications_enabled': notificationsEnabled,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'push_notifications': pushNotifications,
      'language': language,
      'timezone': timezone,
      'currency': currency,
      'privacy_settings': privacySettings,
      'preferences': preferences,
    };
  }
}

/// Profile completion status
class ProfileCompletionStatus {
  final int percentage;
  final int completedItems;
  final int totalItems;
  final List<CompletionItem> items;

  ProfileCompletionStatus({
    required this.percentage,
    required this.completedItems,
    required this.totalItems,
    required this.items,
  });
}

/// Individual completion item
class CompletionItem {
  final String name;
  final bool isCompleted;

  CompletionItem(this.name, this.isCompleted);
}

/// Availability status enum
enum AvailabilityStatus {
  available,
  busy,
  offline,
  away,
}

/// Insight priority enum
enum InsightPriority {
  low,
  medium,
  high,
  critical,
}