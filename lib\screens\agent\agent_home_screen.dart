import 'package:flutter/material.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/services/request_service.dart';
import 'package:mlink/widgets/app_logo.dart';
import 'package:mlink/screens/chat/chat_screen.dart';
import 'package:mlink/screens/portfolio/portfolio_screen.dart';
import 'package:mlink/screens/profile/profile_screen.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/widgets/job_cards.dart';

class AgentHomeScreen extends StatefulWidget {
  const AgentHomeScreen({super.key});

  @override
  State<AgentHomeScreen> createState() => _AgentHomeScreenState();
}

class _AgentHomeScreenState extends State<AgentHomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const AgentDashboard(),
    const AgentJobsScreen(),
    const ChatScreen(),
    const PortfolioScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined),
            activeIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.work_outline),
            activeIcon: Icon(Icons.work),
            label: 'Jobs',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble_outline),
            activeIcon: Icon(Icons.chat_bubble),
            label: 'Chat',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.photo_library_outlined),
            activeIcon: Icon(Icons.photo_library),
            label: 'Portfolio',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}

class AgentDashboard extends StatefulWidget {
  const AgentDashboard({super.key});

  @override
  State<AgentDashboard> createState() => _AgentDashboardState();
}

class _AgentDashboardState extends State<AgentDashboard> {
  bool _isOnline = false;

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      user?.fullName?.substring(0, 1).toUpperCase() ?? 'A',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Agent Dashboard',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Hello, ${user?.fullName?.split(' ').first ?? 'Agent'}!',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.notifications_outlined,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    onPressed: () {
                      // TODO: Implement notifications
                    },
                  ),
                  const SizedBox(width: 8),
                  AppLogo(
                    width: AppLogoSizes.small,
                    height: AppLogoSizes.small,
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Online Status Toggle
              Card(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: (_isOnline ? Colors.green : Colors.grey).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          _isOnline ? Icons.power : Icons.power_off,
                          color: _isOnline ? Colors.green : Colors.grey,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _isOnline ? 'You are Online' : 'You are Offline',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _isOnline ? 'Ready to receive delivery requests' : 'Turn on to start receiving jobs',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Switch(
                        value: _isOnline,
                        onChanged: (value) {
                          setState(() => _isOnline = value);
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Stats Cards
              Text(
                'Today\'s Stats',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'Deliveries',
                      value: '0',
                      icon: Icons.delivery_dining,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: StatsCard(
                      title: 'Earnings',
                      value: 'TSh 0',
                      icon: Icons.account_balance_wallet,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'Rating',
                      value: '0.0',
                      icon: Icons.star,
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: StatsCard(
                      title: 'Hours',
                      value: '0.0',
                      icon: Icons.access_time,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Available Jobs
              Text(
                'Available Jobs',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _isOnline ? 'No jobs available' : 'Go online to see available jobs',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isOnline ? 'New delivery requests will appear here' : 'Turn on your status to start receiving job requests',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AgentJobsScreen extends StatefulWidget {
  const AgentJobsScreen({super.key});

  @override
  State<AgentJobsScreen> createState() => _AgentJobsScreenState();
}

class _AgentJobsScreenState extends State<AgentJobsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';
  String _sortBy = 'created_at';
  bool _sortAscending = false;
  List<BaseRequest> _availableJobs = [];
  List<BaseRequest> _myActiveJobs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadJobs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadJobs() async {
    setState(() => _isLoading = true);
    try {
      // Load available jobs (delivery and buy-for-me)
      final deliveryJobs = await RequestService.instance.getAvailableRequests(RequestType.delivery);
      final buyForMeJobs = await RequestService.instance.getAvailableRequests(RequestType.buyForMe);
      
      _availableJobs = [...deliveryJobs, ...buyForMeJobs];
      
      // Load agent's active jobs
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final userRequests = await RequestService.instance.getUserRequests(user.id);
        _myActiveJobs = userRequests.where((r) => 
          r.status == RequestStatus.accepted || 
          r.status == RequestStatus.inProgress
        ).toList();
      }
      
      _applyFiltersAndSort();
    } catch (e) {
      print('Error loading jobs: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load jobs: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _applyFiltersAndSort() {
    List<BaseRequest> filteredJobs = List.from(_availableJobs);
    
    // Apply filters
    if (_selectedFilter != 'all') {
      filteredJobs = filteredJobs.where((job) {
        switch (_selectedFilter) {
          case 'delivery':
            return job.type == RequestType.delivery;
          case 'buy_for_me':
            return job.type == RequestType.buyForMe;
          case 'urgent':
            if (job is DeliveryRequest) {
              return job.priority == DeliveryPriority.urgent;
            }
            return false;
          default:
            return true;
        }
      }).toList();
    }
    
    // Apply sorting
    filteredJobs.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'created_at':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'payment':
          comparison = a.payment.amount.compareTo(b.payment.amount);
          break;
        case 'distance':
          // TODO: Implement distance sorting based on current location
          comparison = 0;
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });
    
    setState(() {
      _availableJobs = filteredJobs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Jobs',
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.refresh,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                        onPressed: _loadJobs,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Filters and Sort
                  Row(
                    children: [
                      Expanded(
                        child: _buildFilterChips(),
                      ),
                      const SizedBox(width: 8),
                      _buildSortButton(),
                    ],
                  ),
                ],
              ),
            ),
            
            // Tab Bar
            TabBar(
              controller: _tabController,
              indicatorColor: Theme.of(context).colorScheme.primary,
              labelColor: Theme.of(context).colorScheme.primary,
              unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              tabs: [
                Tab(
                  text: 'Available Jobs (${_availableJobs.length})',
                  icon: const Icon(Icons.work_outline),
                ),
                Tab(
                  text: 'My Jobs (${_myActiveJobs.length})',
                  icon: const Icon(Icons.assignment),
                ),
              ],
            ),
            
            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAvailableJobsTab(),
                  _buildMyJobsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChips() {
    final filters = [
      {'id': 'all', 'label': 'All'},
      {'id': 'delivery', 'label': 'Delivery'},
      {'id': 'buy_for_me', 'label': 'Buy for Me'},
      {'id': 'urgent', 'label': 'Urgent'},
    ];
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: filters.map((filter) {
          final isSelected = _selectedFilter == filter['id'];
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Text(
                filter['label']!,
                style: TextStyle(
                  color: isSelected 
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
              backgroundColor: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1),
              selectedColor: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
              onSelected: (selected) {
                setState(() => _selectedFilter = filter['id']!);
                _applyFiltersAndSort();
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.sort,
        color: Theme.of(context).colorScheme.onPrimary,
      ),
      onSelected: (value) {
        setState(() {
          if (_sortBy == value) {
            _sortAscending = !_sortAscending;
          } else {
            _sortBy = value;
            _sortAscending = false;
          }
        });
        _applyFiltersAndSort();
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'created_at',
          child: Row(
            children: [
              Icon(Icons.access_time, size: 16),
              const SizedBox(width: 8),
              const Text('Date Created'),
              if (_sortBy == 'created_at') ...
              [
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
        PopupMenuItem(
          value: 'payment',
          child: Row(
            children: [
              Icon(Icons.payments, size: 16),
              const SizedBox(width: 8),
              const Text('Payment Amount'),
              if (_sortBy == 'payment') ...
              [
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
        PopupMenuItem(
          value: 'distance',
          child: Row(
            children: [
              Icon(Icons.location_on, size: 16),
              const SizedBox(width: 8),
              const Text('Distance'),
              if (_sortBy == 'distance') ...
              [
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvailableJobsTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (_availableJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Available Jobs',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Pull to refresh or adjust filters',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _availableJobs.length,
        itemBuilder: (context, index) {
          final job = _availableJobs[index];
          return AgentJobCard(
            job: job,
            onAccept: () => _acceptJob(job),
            onViewDetails: () => _viewJobDetails(job),
          );
        },
      ),
    );
  }

  Widget _buildMyJobsTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (_myActiveJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_turned_in,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Active Jobs',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Accept jobs from the Available tab',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myActiveJobs.length,
        itemBuilder: (context, index) {
          final job = _myActiveJobs[index];
          return MyJobCard(
            job: job,
            onUpdateStatus: (status) => _updateJobStatus(job, status),
            onViewDetails: () => _viewJobDetails(job),
          );
        },
      ),
    );
  }

  Future<void> _acceptJob(BaseRequest job) async {
    try {
      final user = AuthService.instance.currentUser;
      if (user == null) return;
      
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Accept Job'),
          content: Text('Accept this ${job.type.name.replaceAll('_', ' ')} job for TSh ${job.payment.amount.toStringAsFixed(0)}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Accept'),
            ),
          ],
        ),
      );
      
      if (confirmed == true) {
        await RequestService.instance.assignRequest(job.id, user.id, job.type);
        await _loadJobs();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Job accepted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to accept job: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateJobStatus(BaseRequest job, RequestStatus status) async {
    try {
      final user = AuthService.instance.currentUser;
      if (user == null) return;
      
      await RequestService.instance.updateRequestStatus(job.id, status, user.id);
      await _loadJobs();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Job status updated to ${status.name}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _viewJobDetails(BaseRequest job) {
    // TODO: Navigate to job details screen
    showDialog(
      context: context,
      builder: (context) => JobDetailsDialog(job: job),
    );
  }
}


