enum VehicleType { bodaboda, bicycle, car, truck, van, other, bajaji, pickup }

class Agent {
  final String id;
  final String userId;
  final VehicleType vehicleType;
  final String? vehicleLicensePlate;
  final bool isVerified;
  final bool isAvailable;
  final double? currentLocationLat;
  final double? currentLocationLng;
  final DateTime? lastLocationUpdate;
  final double rating;
  final int totalDeliveries;
  final double totalEarnings;
  final Map<String, dynamic>? documents;
  final DateTime createdAt;
  final DateTime updatedAt;

  Agent({
    required this.id,
    required this.userId,
    required this.vehicleType,
    this.vehicleLicensePlate,
    this.isVerified = false,
    this.isAvailable = true,
    this.currentLocationLat,
    this.currentLocationLng,
    this.lastLocationUpdate,
    this.rating = 0.0,
    this.totalDeliveries = 0,
    this.totalEarnings = 0.0,
    this.documents,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Agent.fromJson(Map<String, dynamic> json) {
    return Agent(
      id: json['id'],
      userId: json['user_id'],
      vehicleType: VehicleType.values.firstWhere(
        (e) => e.name == json['vehicle_type'],
        orElse: () => VehicleType.bodaboda,
      ),
      vehicleLicensePlate: json['vehicle_license_plate'],
      isVerified: json['is_verified'] ?? false,
      isAvailable: json['is_available'] ?? true,
      currentLocationLat: (json['current_location_lat'] as num?)?.toDouble(),
      currentLocationLng: (json['current_location_lng'] as num?)?.toDouble(),
      lastLocationUpdate: json['last_location_update'] != null
          ? DateTime.parse(json['last_location_update'])
          : null,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalDeliveries: json['total_deliveries'] ?? 0,
      totalEarnings: (json['total_earnings'] as num?)?.toDouble() ?? 0.0,
      documents: json['documents'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'vehicle_type': vehicleType.name,
      'vehicle_license_plate': vehicleLicensePlate,
      'is_verified': isVerified,
      'is_available': isAvailable,
      'current_location_lat': currentLocationLat,
      'current_location_lng': currentLocationLng,
      'last_location_update': lastLocationUpdate?.toIso8601String(),
      'rating': rating,
      'total_deliveries': totalDeliveries,
      'total_earnings': totalEarnings,
      'documents': documents,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Agent copyWith({
    String? id,
    String? userId,
    VehicleType? vehicleType,
    String? vehicleLicensePlate,
    bool? isVerified,
    bool? isAvailable,
    double? currentLocationLat,
    double? currentLocationLng,
    DateTime? lastLocationUpdate,
    double? rating,
    int? totalDeliveries,
    double? totalEarnings,
    Map<String, dynamic>? documents,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Agent(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicleLicensePlate: vehicleLicensePlate ?? this.vehicleLicensePlate,
      isVerified: isVerified ?? this.isVerified,
      isAvailable: isAvailable ?? this.isAvailable,
      currentLocationLat: currentLocationLat ?? this.currentLocationLat,
      currentLocationLng: currentLocationLng ?? this.currentLocationLng,
      lastLocationUpdate: lastLocationUpdate ?? this.lastLocationUpdate,
      rating: rating ?? this.rating,
      totalDeliveries: totalDeliveries ?? this.totalDeliveries,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      documents: documents ?? this.documents,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Agent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Agent{id: $id, userId: $userId, vehicleType: $vehicleType, rating: $rating, isAvailable: $isAvailable}';
  }
}