-- Helper function to insert users to auth.users
CREATE OR REPLACE FUNCTION insert_user_to_auth(
    email text,
    password text
) RETURNS UUID AS $$
DECLARE
  user_id uuid;
  encrypted_pw text;
BEGIN
  user_id := gen_random_uuid();
  encrypted_pw := crypt(password, gen_salt('bf'));
  
  INSERT INTO auth.users
    (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, recovery_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at, confirmation_token, email_change, email_change_token_new, recovery_token)
  VALUES
    (gen_random_uuid(), user_id, 'authenticated', 'authenticated', email, encrypted_pw, '2023-05-03 19:41:43.585805+00', '2023-04-22 13:10:03.275387+00', '2023-04-22 13:10:31.458239+00', '{"provider":"email","providers":["email"]}', '{}', '2023-05-03 19:41:43.580424+00', '2023-05-03 19:41:43.585948+00', '', '', '', '');
  
  INSERT INTO auth.identities (provider_id, user_id, identity_data, provider, last_sign_in_at, created_at, updated_at)
  VALUES
    (gen_random_uuid(), user_id, format('{"sub":"%s","email":"%s"}', user_id::text, email)::jsonb, 'email', '2023-05-03 19:41:43.582456+00', '2023-05-03 19:41:43.582497+00', '2023-05-03 19:41:43.582497+00');
  
  RETURN user_id;
END;
$$ LANGUAGE plpgsql;

-- Insert sample users to auth.users first
DO $$
DECLARE
  customer1_id UUID;
  customer2_id UUID;
  customer3_id UUID;
  agent1_id UUID;
  agent2_id UUID;
  agent3_id UUID;
  technician1_id UUID;
  technician2_id UUID;
  technician3_id UUID;
  admin_id UUID;
BEGIN
  -- Create customers
  customer1_id := insert_user_to_auth('<EMAIL>', 'password123');
  customer2_id := insert_user_to_auth('<EMAIL>', 'password123');
  customer3_id := insert_user_to_auth('<EMAIL>', 'password123');
  
  -- Create agents
  agent1_id := insert_user_to_auth('<EMAIL>', 'password123');
  agent2_id := insert_user_to_auth('<EMAIL>', 'password123');
  agent3_id := insert_user_to_auth('<EMAIL>', 'password123');
  
  -- Create technicians
  technician1_id := insert_user_to_auth('<EMAIL>', 'password123');
  technician2_id := insert_user_to_auth('<EMAIL>', 'password123');
  technician3_id := insert_user_to_auth('<EMAIL>', 'password123');
  
  -- Create admin
  admin_id := insert_user_to_auth('<EMAIL>', 'password123');
  
  -- Insert users data
  INSERT INTO users (id, email, full_name, phone_number, role, status, created_at, updated_at) VALUES
    (customer1_id, '<EMAIL>', 'John Doe', '+************', 'customer', 'active', NOW(), NOW()),
    (customer2_id, '<EMAIL>', 'Jane Smith', '+************', 'customer', 'active', NOW(), NOW()),
    (customer3_id, '<EMAIL>', 'Michael Johnson', '+************', 'customer', 'active', NOW(), NOW()),
    (agent1_id, '<EMAIL>', 'Hassan Mwalimu', '+255745678901', 'agent', 'active', NOW(), NOW()),
    (agent2_id, '<EMAIL>', 'Fatima Ally', '+255756789012', 'agent', 'active', NOW(), NOW()),
    (agent3_id, '<EMAIL>', 'James Kinyua', '+255767890123', 'agent', 'active', NOW(), NOW()),
    (technician1_id, '<EMAIL>', 'Ali Mfaume', '+255778901234', 'technician', 'active', NOW(), NOW()),
    (technician2_id, '<EMAIL>', 'Grace Mwakamba', '+************', 'technician', 'active', NOW(), NOW()),
    (technician3_id, '<EMAIL>', 'David Mwangi', '+************', 'technician', 'active', NOW(), NOW()),
    (admin_id, '<EMAIL>', 'M-Link Admin', '+************', 'admin', 'active', NOW(), NOW());
  
  -- Insert agents data
  INSERT INTO agents (id, user_id, vehicle_type, vehicle_license_plate, is_verified, is_available, current_location_lat, current_location_lng, rating, total_deliveries, total_earnings, created_at, updated_at) VALUES
    (gen_random_uuid(), agent1_id, 'bodaboda', 'MC 123 ABC', true, true, -6.792354, 39.208328, 4.5, 156, 2340000.00, NOW(), NOW()),
    (gen_random_uuid(), agent2_id, 'bicycle', 'BC 456 DEF', true, true, -6.824040, 39.269566, 4.7, 89, 1120000.00, NOW(), NOW()),
    (gen_random_uuid(), agent3_id, 'car', 'T 789 GHI', true, false, -6.800000, 39.283333, 4.3, 234, 3510000.00, NOW(), NOW());
  
  -- Insert technicians data
  INSERT INTO technicians (id, user_id, nida_number, is_nida_verified, status, rating, total_jobs, earnings, available_balance, is_available, location, bio, experience_years, certifications, created_at, updated_at) VALUES
    (gen_random_uuid(), technician1_id, '19850301-12345-12345-12', true, 'active', 4.6, 127, 4445000.00, 225000.00, true, '{"address": "Dar es Salaam", "lat": -6.792354, "lng": 39.208328}', 'Expert electrician with 8+ years experience in residential and commercial wiring', 8, ARRAY['Certified Electrician', 'Solar Installation Certificate'], NOW(), NOW()),
    (gen_random_uuid(), technician2_id, '19890715-98765-98765-21', true, 'active', 4.4, 89, 2670000.00, 150000.00, true, '{"address": "Dar es Salaam", "lat": -6.824040, "lng": 39.269566}', 'Professional plumber specializing in pipe installation and leak repairs', 5, ARRAY['Licensed Plumber'], NOW(), NOW()),
    (gen_random_uuid(), technician3_id, '19750520-11111-22222-33', true, 'active', 4.8, 203, 8120000.00, 450000.00, true, '{"address": "Dar es Salaam & Surroundings", "lat": -6.800000, "lng": 39.283333}', 'Master carpenter with over 12 years experience in furniture making and construction', 12, ARRAY['Master Carpenter Certificate', 'Construction Safety Certificate'], NOW(), NOW());
  
  -- Insert technician skills
  INSERT INTO technician_skills (technician_id, name, category, description, hourly_rate, fixed_rate, created_at) 
  SELECT 
    t.id, 
    'Electrical Wiring', 
    'electrician', 
    'Residential and commercial electrical wiring installation', 
    35000.00, 
    NULL, 
    NOW()
  FROM technicians t WHERE t.user_id = technician1_id;

  INSERT INTO technician_skills (technician_id, name, category, description, hourly_rate, fixed_rate, created_at) 
  SELECT 
    t.id, 
    'Solar Installation', 
    'electrician', 
    'Solar panel installation and maintenance', 
    45000.00, 
    150000.00, 
    NOW()
  FROM technicians t WHERE t.user_id = technician1_id;

  INSERT INTO technician_skills (technician_id, name, category, description, hourly_rate, fixed_rate, created_at) 
  SELECT 
    t.id, 
    'Pipe Installation', 
    'plumber', 
    'Water pipe installation and repair', 
    30000.00, 
    NULL, 
    NOW()
  FROM technicians t WHERE t.user_id = technician2_id;

  INSERT INTO technician_skills (technician_id, name, category, description, hourly_rate, fixed_rate, created_at) 
  SELECT 
    t.id, 
    'Custom Furniture', 
    'carpenter', 
    'Custom furniture design and construction', 
    40000.00, 
    250000.00, 
    NOW()
  FROM technicians t WHERE t.user_id = technician3_id;

  -- Insert technician portfolio
  INSERT INTO technician_portfolio (technician_id, title, description, image_urls, category, created_at)
  SELECT 
    t.id,
    'Residential Electrical Project',
    'Complete electrical wiring for 3-bedroom house including installation of outlets, switches, and lighting',
    ARRAY['https://picsum.photos/400/300?random=101', 'https://picsum.photos/400/300?random=102'],
    'electrician',
    NOW() - INTERVAL '30 days'
  FROM technicians t WHERE t.user_id = technician1_id;

  INSERT INTO technician_portfolio (technician_id, title, description, image_urls, category, created_at)
  SELECT 
    t.id,
    'Kitchen Plumbing Installation',
    'Complete kitchen plumbing including sink installation and water supply connections',
    ARRAY['https://picsum.photos/400/300?random=201', 'https://picsum.photos/400/300?random=202'],
    'plumber',
    NOW() - INTERVAL '45 days'
  FROM technicians t WHERE t.user_id = technician2_id;

  INSERT INTO technician_portfolio (technician_id, title, description, image_urls, category, created_at)
  SELECT 
    t.id,
    'Custom Living Room Set',
    'Handcrafted wooden living room furniture set including sofa, coffee table, and side tables',
    ARRAY['https://picsum.photos/400/300?random=301', 'https://picsum.photos/400/300?random=302', 'https://picsum.photos/400/300?random=303'],
    'carpenter',
    NOW() - INTERVAL '60 days'
  FROM technicians t WHERE t.user_id = technician3_id;

  -- Insert sample requests (unified table structure)
  INSERT INTO requests (id, customer_id, type, status, description, pickup_location, dropoff_location, payment, scheduled_at, image_urls, notes, customer_phone, 
    agent_id, preferred_vehicle, priority, estimated_weight, package_type, requires_signature, recipient_name, recipient_phone,
    store_location, items, estimated_cost, max_budget, preferred_brands, allow_substitutes,
    technician_id, category, skill_required, hourly_rate, estimated_hours, is_urgent, required_tools, 
    created_at, updated_at) VALUES
    
    -- Delivery request
    (gen_random_uuid(), customer1_id, 'delivery', 'pending', 'Deliver documents to city center', 
     '{"latitude": -6.792354, "longitude": 39.208328, "address": "Mikocheni, Dar es Salaam", "landmark": "Near Shoppers Plaza"}',
     '{"latitude": -6.824040, "longitude": 39.269566, "address": "Kariakoo, Dar es Salaam", "landmark": "Near Central Market"}',
     '{"method": "mobileMoney", "amount": 8000.00, "advance": 2000.00, "tip": 0.00, "is_paid": false}',
     NOW() + INTERVAL '2 hours',
     ARRAY['https://picsum.photos/400/300?random=1'],
     'Handle with care - important documents',
     '+************',
     (SELECT id FROM agents WHERE user_id = agent1_id), 'bodaboda', 'normal', 2.5, 'Documents', true, 'John Doe', '+************',
     NULL, NULL, NULL, NULL, NULL, NULL,
     NULL, NULL, NULL, NULL, NULL, NULL, NULL,
     NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour'),
    
    -- Buy for me request  
    (gen_random_uuid(), customer2_id, 'buyForMe', 'accepted', 'Buy groceries from supermarket',
     '{"latitude": -6.800000, "longitude": 39.283333, "address": "Msasani, Dar es Salaam", "landmark": "Near Peninsula Hotel"}',
     '{"latitude": -6.824040, "longitude": 39.269566, "address": "Kariakoo, Dar es Salaam", "landmark": "Near Central Market"}',
     '{"method": "cash", "amount": 45000.00, "advance": 15000.00, "tip": 2000.00, "is_paid": false}',
     NOW() + INTERVAL '3 hours',
     ARRAY['https://picsum.photos/400/300?random=2', 'https://picsum.photos/400/300?random=3'],
     'Please check expiry dates',
     '+************',
     (SELECT id FROM agents WHERE user_id = agent2_id), 'bicycle', 'normal', NULL, NULL, NULL, NULL, NULL,
     'Shoppers Plaza Supermarket', ARRAY['Milk 1L', 'Bread', 'Eggs 12pcs', 'Rice 2kg', 'Cooking Oil 1L'], 40000.00, 45000.00, 'Azam, Cowboy', true,
     NULL, NULL, NULL, NULL, NULL, NULL, NULL,
     NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '10 minutes'),
    
    -- Technician service request
    (gen_random_uuid(), customer3_id, 'technicianService', 'inProgress', 'Fix electrical socket in bedroom',
     '{"latitude": -6.775000, "longitude": 39.250000, "address": "Kinondoni, Dar es Salaam", "landmark": "Near Mwenge Market"}',
     NULL,
     '{"method": "mobileMoney", "amount": 25000.00, "advance": 0.00, "tip": 0.00, "is_paid": false}',
     NOW() + INTERVAL '1 hour',
     ARRAY['https://picsum.photos/400/300?random=4'],
     'Socket is not working, might need replacement',
     '+************',
     NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
     NULL, NULL, NULL, NULL, NULL, NULL,
     (SELECT id FROM technicians WHERE user_id = technician1_id), 'electrician', 'Socket Installation', 35000.00, 1, false, ARRAY['Screwdriver', 'Wire Strippers', 'Electrical Tape'],
     NOW() - INTERVAL '2 hours', NOW() - INTERVAL '30 minutes'),
    
    -- Completed delivery request
    (gen_random_uuid(), customer1_id, 'delivery', 'completed', 'Deliver birthday cake',
     '{"latitude": -6.810000, "longitude": 39.280000, "address": "Upanga, Dar es Salaam", "landmark": "Near Azikiwe Street"}',
     '{"latitude": -6.792354, "longitude": 39.208328, "address": "Mikocheni, Dar es Salaam", "landmark": "Near Shoppers Plaza"}',
     '{"method": "mobileMoney", "amount": 12000.00, "advance": 0.00, "tip": 3000.00, "is_paid": true}',
     NOW() - INTERVAL '1 day',
     ARRAY['https://picsum.photos/400/300?random=5'],
     'Happy birthday cake - handle carefully',
     '+************',
     (SELECT id FROM agents WHERE user_id = agent1_id), 'bodaboda', 'urgent', 3.0, 'Food', false, 'Birthday Person', '+************',
     NULL, NULL, NULL, NULL, NULL, NULL,
     NULL, NULL, NULL, NULL, NULL, NULL, NULL,
     NOW() - INTERVAL '1 day 2 hours', NOW() - INTERVAL '1 day'),
    
    -- Completed technician service request
    (gen_random_uuid(), customer2_id, 'technicianService', 'completed', 'Repair leaking pipe in kitchen',
     '{"latitude": -6.824040, "longitude": 39.269566, "address": "Kariakoo, Dar es Salaam", "landmark": "Near Central Market"}',
     NULL,
     '{"method": "cash", "amount": 35000.00, "advance": 10000.00, "tip": 5000.00, "is_paid": true}',
     NOW() - INTERVAL '2 days',
     ARRAY['https://picsum.photos/400/300?random=6'],
     'Pipe under kitchen sink is leaking',
     '+************',
     NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
     NULL, NULL, NULL, NULL, NULL, NULL,
     (SELECT id FROM technicians WHERE user_id = technician2_id), 'plumber', 'Pipe Repair', 30000.00, 2, false, ARRAY['Pipe Wrench', 'Pipe Sealant', 'Replacement Pipes'],
     NOW() - INTERVAL '2 days 3 hours', NOW() - INTERVAL '2 days');
  
  -- Insert sample reviews
  INSERT INTO reviews (id, reviewer_id, reviewee_id, request_id, rating, comment, created_at, updated_at) VALUES
    (gen_random_uuid(), customer1_id, agent1_id, 
     (SELECT id FROM requests WHERE customer_id = customer1_id AND status = 'completed' AND type = 'delivery' LIMIT 1),
     5, 'Excellent service! Very professional and delivered on time.', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day'),
    
    (gen_random_uuid(), customer2_id, technician2_id,
     (SELECT id FROM requests WHERE customer_id = customer2_id AND status = 'completed' AND type = 'technician_service' LIMIT 1),
     4, 'Good work, fixed the leak quickly. Would recommend.', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days');
  
  -- Insert sample notifications
  INSERT INTO notifications (id, user_id, title, body, type, is_read, data, created_at) VALUES
    (gen_random_uuid(), customer1_id, 'Request Accepted', 'Your delivery request has been accepted by Hassan Mwalimu', 'request', false, '{"request_id": "' || (SELECT id FROM requests WHERE customer_id = customer1_id AND status = 'pending' LIMIT 1) || '"}', NOW() - INTERVAL '30 minutes'),
    
    (gen_random_uuid(), customer2_id, 'Request In Progress', 'Your buy for me request is now in progress', 'request', false, '{"request_id": "' || (SELECT id FROM requests WHERE customer_id = customer2_id AND status = 'accepted' LIMIT 1) || '"}', NOW() - INTERVAL '10 minutes'),
    
    (gen_random_uuid(), customer3_id, 'Technician Assigned', 'Ali Mfaume has been assigned to your electrical repair request', 'request', true, '{"request_id": "' || (SELECT id FROM requests WHERE customer_id = customer3_id AND status = 'inProgress' LIMIT 1) || '"}', NOW() - INTERVAL '1 hour'),
    
    (gen_random_uuid(), agent1_id, 'New Request Available', 'A new delivery request is available in your area', 'request', false, '{"request_type": "delivery", "location": "Mikocheni"}', NOW() - INTERVAL '5 minutes'),
    
    (gen_random_uuid(), technician1_id, 'Payment Received', 'Payment of TSH 30,000 has been received for your recent job', 'success', true, '{"amount": 30000, "currency": "TSH"}', NOW() - INTERVAL '1 day');

  -- Insert sample chats
  INSERT INTO chats (id, user1_id, user2_id, request_id, type, title, last_message, last_message_at, created_at, updated_at) VALUES
    (gen_random_uuid(), customer1_id, agent1_id, 
     (SELECT id FROM requests WHERE customer_id = customer1_id AND status = 'pending' AND type = 'delivery' LIMIT 1),
     'direct', NULL, 'I am on my way to pickup location', NOW() - INTERVAL '15 minutes', NOW() - INTERVAL '1 hour', NOW() - INTERVAL '15 minutes'),
    
    (gen_random_uuid(), customer2_id, agent2_id,
     (SELECT id FROM requests WHERE customer_id = customer2_id AND status = 'accepted' AND type = 'buy_for_me' LIMIT 1),
     'direct', NULL, 'Found all items except Cowboy rice, is Azam rice okay?', NOW() - INTERVAL '5 minutes', NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '5 minutes'),
    
    (gen_random_uuid(), customer3_id, technician1_id,
     (SELECT id FROM requests WHERE customer_id = customer3_id AND status = 'in_progress' AND type = 'technician_service' LIMIT 1),
     'direct', NULL, 'Work completed successfully. Socket is working now.', NOW() - INTERVAL '10 minutes', NOW() - INTERVAL '2 hours', NOW() - INTERVAL '10 minutes');

  -- Insert sample messages
  INSERT INTO messages (id, chat_id, sender_id, content, message_type, is_read, created_at, updated_at) VALUES
    -- Chat 1: Customer and Agent
    (gen_random_uuid(), 
     (SELECT id FROM chats WHERE user1_id = customer1_id AND user2_id = agent1_id LIMIT 1),
     customer1_id, 'Hi Hassan, I have a delivery request for important documents', 'text', true, NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer1_id AND user2_id = agent1_id LIMIT 1), 
     agent1_id, 'Hello John! I have accepted your request. I will be there shortly.', 'text', true, NOW() - INTERVAL '45 minutes', NOW() - INTERVAL '45 minutes'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer1_id AND user2_id = agent1_id LIMIT 1),
     customer1_id, 'Great! Please handle them carefully as they are very important.', 'text', true, NOW() - INTERVAL '40 minutes', NOW() - INTERVAL '40 minutes'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer1_id AND user2_id = agent1_id LIMIT 1),
     agent1_id, 'I am on my way to pickup location', 'text', false, NOW() - INTERVAL '15 minutes', NOW() - INTERVAL '15 minutes'),

    -- Chat 2: Customer and Agent for buy for me
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer2_id AND user2_id = agent2_id LIMIT 1),
     customer2_id, 'Hi Fatima! I need you to buy groceries from Shoppers Plaza', 'text', true, NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '30 minutes'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer2_id AND user2_id = agent2_id LIMIT 1),
     agent2_id, 'Hello Jane! I am at the supermarket now. Let me get your items.', 'text', true, NOW() - INTERVAL '20 minutes', NOW() - INTERVAL '20 minutes'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer2_id AND user2_id = agent2_id LIMIT 1),
     agent2_id, 'Found all items except Cowboy rice, is Azam rice okay?', 'text', false, NOW() - INTERVAL '5 minutes', NOW() - INTERVAL '5 minutes'),

    -- Chat 3: Customer and Technician
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer3_id AND user2_id = technician1_id LIMIT 1),
     customer3_id, 'Hi Ali, the electrical socket in my bedroom is not working', 'text', true, NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer3_id AND user2_id = technician1_id LIMIT 1),
     technician1_id, 'Hello Michael! I will be there in 30 minutes with my tools.', 'text', true, NOW() - INTERVAL '1 hour 30 minutes', NOW() - INTERVAL '1 hour 30 minutes'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer3_id AND user2_id = technician1_id LIMIT 1),
     technician1_id, 'I have arrived. The socket needs replacement.', 'text', true, NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '30 minutes'),
    
    (gen_random_uuid(),
     (SELECT id FROM chats WHERE user1_id = customer3_id AND user2_id = technician1_id LIMIT 1),
     technician1_id, 'Work completed successfully. Socket is working now.', 'text', false, NOW() - INTERVAL '10 minutes', NOW() - INTERVAL '10 minutes');
  
  -- Insert sample payments
  INSERT INTO payments (id, request_id, payer_id, payee_id, amount, method, status, transaction_id, created_at, updated_at) VALUES
    (gen_random_uuid(), 
     (SELECT id FROM requests WHERE customer_id = customer1_id AND status = 'completed' AND type = 'delivery' LIMIT 1),
     customer1_id, agent1_id, 15000.00, 'mobileMoney', 'completed', 'TXN-' || EXTRACT(EPOCH FROM NOW()), NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day'),
    
    (gen_random_uuid(),
     (SELECT id FROM requests WHERE customer_id = customer2_id AND status = 'completed' AND type = 'technicianService' LIMIT 1),
     customer2_id, technician2_id, 40000.00, 'cash', 'completed', 'TXN-' || EXTRACT(EPOCH FROM NOW()) + 1, NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days');

  -- Insert wallet transactions
  INSERT INTO wallet_transactions (id, user_id, type, amount, description, balance_before, balance_after, created_at) VALUES
    (gen_random_uuid(), agent1_id, 'earning', 12000.00, 'Delivery service fee from cake delivery', 100000.00, 112000.00, NOW() - INTERVAL '1 day'),
    (gen_random_uuid(), technician2_id, 'earning', 32000.00, 'Plumbing service fee from pipe repair', 50000.00, 82000.00, NOW() - INTERVAL '2 days'),
    (gen_random_uuid(), agent1_id, 'withdrawal', 50000.00, 'Cash withdrawal from M-Link wallet', 112000.00, 62000.00, NOW() - INTERVAL '12 hours');

  -- Insert user balances
  INSERT INTO user_balances (user_id, available_balance, pending_balance, total_earned, total_withdrawn, updated_at) VALUES
    (agent1_id, 62000.00, 0.00, 2352000.00, 500000.00, NOW()),
    (agent2_id, 85000.00, 15000.00, 1135000.00, 200000.00, NOW()),
    (agent3_id, 125000.00, 0.00, 3510000.00, 1000000.00, NOW()),
    (technician1_id, 225000.00, 0.00, 4445000.00, 800000.00, NOW()),
    (technician2_id, 82000.00, 0.00, 2702000.00, 400000.00, NOW()),
    (technician3_id, 450000.00, 0.00, 8120000.00, 2000000.00, NOW());
  
END $$;

-- Drop the helper function after use
DROP FUNCTION IF EXISTS insert_user_to_auth(text, text);