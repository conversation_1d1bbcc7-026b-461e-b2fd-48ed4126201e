class TanzaniaLocation {
  final String region;
  final double latitude;
  final double longitude;
  final List<LocationPoint> points;

  const TanzaniaLocation({
    required this.region,
    required this.latitude,
    required this.longitude,
    required this.points,
  });
}

class LocationPoint {
  final String name;
  final double latitude;
  final double longitude;

  const LocationPoint({
    required this.name,
    required this.latitude,
    required this.longitude,
  });
}

class TanzaniaRegion {
  final String name;
  final List<LocationPoint> districts;

  const TanzaniaRegion({
    required this.name,
    required this.districts,
  });
}

class TanzaniaLocations {
  static const List<TanzaniaRegion> regions = [
    TanzaniaRegion(
      name: 'Dar es Salaam',
      districts: [
        LocationPoint(name: 'Ilala', latitude: -6.8235, longitude: 39.2695),
        LocationPoint(name: 'Kinondoni', latitude: -6.7924, longitude: 39.2083),
        LocationPoint(name: 'Temeke', latitude: -6.8500, longitude: 39.2500),
        LocationPoint(name: 'Ubungo', latitude: -6.7833, longitude: 39.2667),
        LocationPoint(name: 'Kigamboni', latitude: -6.8833, longitude: 39.3000),
      ],
    ),
    TanzaniaRegion(
      name: 'Arusha',
      districts: [
        LocationPoint(name: 'Arusha City', latitude: -3.3833, longitude: 36.6833),
        LocationPoint(name: 'Arusha District', latitude: -3.4000, longitude: 36.7000),
        LocationPoint(name: 'Karatu', latitude: -3.5333, longitude: 35.8000),
        LocationPoint(name: 'Longido', latitude: -2.6833, longitude: 36.7000),
        LocationPoint(name: 'Monduli', latitude: -3.3500, longitude: 36.4500),
        LocationPoint(name: 'Ngorongoro', latitude: -3.2000, longitude: 35.5833),
      ],
    ),
    TanzaniaRegion(
      name: 'Dodoma',
      districts: [
        LocationPoint(name: 'Dodoma City', latitude: -6.1833, longitude: 35.7500),
        LocationPoint(name: 'Bahi', latitude: -5.9667, longitude: 35.3167),
        LocationPoint(name: 'Chamwino', latitude: -6.0333, longitude: 35.7167),
        LocationPoint(name: 'Chemba', latitude: -6.0167, longitude: 35.0833),
        LocationPoint(name: 'Kondoa', latitude: -4.9000, longitude: 35.7833),
        LocationPoint(name: 'Kongwa', latitude: -6.1667, longitude: 36.2833),
        LocationPoint(name: 'Mpwapwa', latitude: -6.3500, longitude: 36.4833),
      ],
    ),
    TanzaniaRegion(
      name: 'Mwanza',
      districts: [
        LocationPoint(name: 'Ilemela', latitude: -2.4667, longitude: 32.9167),
        LocationPoint(name: 'Nyamagana', latitude: -2.5167, longitude: 32.9000),
        LocationPoint(name: 'Bukoba', latitude: -1.3317, longitude: 31.8117),
        LocationPoint(name: 'Geita', latitude: -2.8667, longitude: 32.2167),
        LocationPoint(name: 'Kwimba', latitude: -2.8333, longitude: 33.5833),
        LocationPoint(name: 'Misungwi', latitude: -2.5333, longitude: 33.1000),
        LocationPoint(name: 'Sengerema', latitude: -2.4333, longitude: 33.2500),
      ],
    ),
    TanzaniaRegion(
      name: 'Mbeya',
      districts: [
        LocationPoint(name: 'Mbeya City', latitude: -8.9000, longitude: 33.4500),
        LocationPoint(name: 'Busokelo', latitude: -8.8167, longitude: 33.6000),
        LocationPoint(name: 'Chunya', latitude: -8.1167, longitude: 33.0000),
        LocationPoint(name: 'Ileje', latitude: -9.5333, longitude: 33.2000),
        LocationPoint(name: 'Kyela', latitude: -9.5167, longitude: 33.8333),
        LocationPoint(name: 'Mbarali', latitude: -8.5500, longitude: 34.0833),
        LocationPoint(name: 'Mbozi', latitude: -9.2333, longitude: 32.7333),
        LocationPoint(name: 'Momba', latitude: -9.2333, longitude: 33.2333),
        LocationPoint(name: 'Rungwe', latitude: -9.1333, longitude: 33.6667),
      ],
    ),
    TanzaniaRegion(
      name: 'Morogoro',
      districts: [
        LocationPoint(name: 'Morogoro City', latitude: -6.8167, longitude: 37.6667),
        LocationPoint(name: 'Gairo', latitude: -6.1167, longitude: 36.8833),
        LocationPoint(name: 'Kilombero', latitude: -8.2500, longitude: 36.5000),
        LocationPoint(name: 'Kilosa', latitude: -6.8333, longitude: 36.9833),
        LocationPoint(name: 'Mvomero', latitude: -6.2833, longitude: 37.4333),
        LocationPoint(name: 'Ulanga', latitude: -8.6667, longitude: 36.6833),
      ],
    ),
    TanzaniaRegion(
      name: 'Tanga',
      districts: [
        LocationPoint(name: 'Tanga City', latitude: -5.0833, longitude: 39.1000),
        LocationPoint(name: 'Handeni', latitude: -5.4500, longitude: 37.9833),
        LocationPoint(name: 'Kilifi', latitude: -3.6333, longitude: 39.8500),
        LocationPoint(name: 'Korogwe', latitude: -5.1833, longitude: 38.4833),
        LocationPoint(name: 'Lushoto', latitude: -4.7833, longitude: 38.2833),
        LocationPoint(name: 'Muheza', latitude: -5.1667, longitude: 38.7833),
        LocationPoint(name: 'Pangani', latitude: -5.4167, longitude: 38.9833),
      ],
    ),
    TanzaniaRegion(
      name: 'Iringa',
      districts: [
        LocationPoint(name: 'Iringa City', latitude: -7.7667, longitude: 35.6833),
        LocationPoint(name: 'Iringa District', latitude: -7.8000, longitude: 35.7000),
        LocationPoint(name: 'Kilolo', latitude: -8.0000, longitude: 35.8333),
        LocationPoint(name: 'Mafinga', latitude: -7.2500, longitude: 35.0667),
        LocationPoint(name: 'Mufindi', latitude: -8.1167, longitude: 35.2833),
        LocationPoint(name: 'Njombe', latitude: -9.3333, longitude: 34.7667),
      ],
    ),
    TanzaniaRegion(
      name: 'Tabora',
      districts: [
        LocationPoint(name: 'Tabora City', latitude: -5.0167, longitude: 32.8000),
        LocationPoint(name: 'Igunga', latitude: -4.3000, longitude: 33.2667),
        LocationPoint(name: 'Kaliua', latitude: -4.6167, longitude: 31.7333),
        LocationPoint(name: 'Nzega', latitude: -4.2167, longitude: 33.1833),
        LocationPoint(name: 'Sikonge', latitude: -5.6333, longitude: 32.7667),
        LocationPoint(name: 'Uyui', latitude: -5.3500, longitude: 32.9167),
      ],
    ),
    TanzaniaRegion(
      name: 'Mtwara',
      districts: [
        LocationPoint(name: 'Mtwara City', latitude: -10.2667, longitude: 40.1833),
        LocationPoint(name: 'Masasi', latitude: -10.7333, longitude: 38.7833),
        LocationPoint(name: 'Mtwara District', latitude: -10.3000, longitude: 40.2000),
        LocationPoint(name: 'Nanyumbu', latitude: -10.6333, longitude: 39.4000),
        LocationPoint(name: 'Newala', latitude: -10.9500, longitude: 39.2833),
        LocationPoint(name: 'Tandahimba', latitude: -10.6167, longitude: 39.5167),
      ],
    ),
    TanzaniaRegion(
      name: 'Lindi',
      districts: [
        LocationPoint(name: 'Lindi City', latitude: -10.0000, longitude: 39.7167),
        LocationPoint(name: 'Kilwa', latitude: -8.9167, longitude: 39.5167),
        LocationPoint(name: 'Lindi District', latitude: -10.0500, longitude: 39.7500),
        LocationPoint(name: 'Liwale', latitude: -9.5833, longitude: 37.8333),
        LocationPoint(name: 'Nachingwea', latitude: -10.3833, longitude: 38.7500),
        LocationPoint(name: 'Ruangwa', latitude: -10.1833, longitude: 38.9500),
      ],
    ),
    TanzaniaRegion(
      name: 'Ruvuma',
      districts: [
        LocationPoint(name: 'Songea', latitude: -10.6833, longitude: 35.6500),
        LocationPoint(name: 'Mbinga', latitude: -11.0000, longitude: 35.0000),
        LocationPoint(name: 'Namtumbo', latitude: -10.4333, longitude: 35.4833),
        LocationPoint(name: 'Nyasa', latitude: -11.4167, longitude: 34.7833),
        LocationPoint(name: 'Tunduru', latitude: -11.2833, longitude: 37.3833),
      ],
    ),
    TanzaniaRegion(
      name: 'Mara',
      districts: [
        LocationPoint(name: 'Musoma', latitude: -1.5000, longitude: 33.8000),
        LocationPoint(name: 'Bunda', latitude: -2.0500, longitude: 33.8667),
        LocationPoint(name: 'Butiama', latitude: -1.7500, longitude: 33.9167),
        LocationPoint(name: 'Serengeti', latitude: -2.3333, longitude: 34.8333),
        LocationPoint(name: 'Tarime', latitude: -1.3500, longitude: 34.3833),
      ],
    ),
    TanzaniaRegion(
      name: 'Kagera',
      districts: [
        LocationPoint(name: 'Bukoba', latitude: -1.3317, longitude: 31.8117),
        LocationPoint(name: 'Biharamulo', latitude: -2.6333, longitude: 31.3000),
        LocationPoint(name: 'Karagwe', latitude: -1.4833, longitude: 30.9500),
        LocationPoint(name: 'Kyerwa', latitude: -1.0833, longitude: 31.2500),
        LocationPoint(name: 'Misenyi', latitude: -1.1833, longitude: 31.6500),
        LocationPoint(name: 'Muleba', latitude: -1.8333, longitude: 31.6667),
        LocationPoint(name: 'Ngara', latitude: -2.5167, longitude: 30.6833),
      ],
    ),
    TanzaniaRegion(
      name: 'Shinyanga',
      districts: [
        LocationPoint(name: 'Shinyanga City', latitude: -3.6667, longitude: 33.4167),
        LocationPoint(name: 'Kahama', latitude: -3.8167, longitude: 32.5833),
        LocationPoint(name: 'Kishapu', latitude: -3.4833, longitude: 33.1833),
        LocationPoint(name: 'Maswa', latitude: -3.0333, longitude: 33.6167),
        LocationPoint(name: 'Meatu', latitude: -3.5333, longitude: 34.3667),
        LocationPoint(name: 'Msalala', latitude: -4.1667, longitude: 33.6667),
      ],
    ),
    TanzaniaRegion(
      name: 'Simiyu',
      districts: [
        LocationPoint(name: 'Bariadi', latitude: -2.8000, longitude: 33.9833),
        LocationPoint(name: 'Busega', latitude: -3.2667, longitude: 34.2167),
        LocationPoint(name: 'Itilima', latitude: -3.5833, longitude: 34.4833),
        LocationPoint(name: 'Maswa', latitude: -3.0333, longitude: 33.6167),
        LocationPoint(name: 'Meatu', latitude: -3.5333, longitude: 34.3667),
      ],
    ),
    TanzaniaRegion(
      name: 'Singida',
      districts: [
        LocationPoint(name: 'Singida City', latitude: -4.8167, longitude: 34.7500),
        LocationPoint(name: 'Ikungi', latitude: -4.2833, longitude: 34.7667),
        LocationPoint(name: 'Iramba', latitude: -4.5833, longitude: 34.5833),
        LocationPoint(name: 'Manyoni', latitude: -5.7333, longitude: 34.0833),
        LocationPoint(name: 'Mkalama', latitude: -4.6333, longitude: 34.8333),
        LocationPoint(name: 'Singida District', latitude: -4.8500, longitude: 34.8000),
      ],
    ),
    TanzaniaRegion(
      name: 'Kigoma',
      districts: [
        LocationPoint(name: 'Kigoma City', latitude: -4.8833, longitude: 29.6333),
        LocationPoint(name: 'Buhigwe', latitude: -4.4167, longitude: 30.0833),
        LocationPoint(name: 'Kakonko', latitude: -4.1333, longitude: 30.9833),
        LocationPoint(name: 'Kasulu', latitude: -4.5833, longitude: 30.1167),
        LocationPoint(name: 'Kibondo', latitude: -3.5833, longitude: 30.7000),
        LocationPoint(name: 'Kigoma District', latitude: -4.9000, longitude: 29.7000),
        LocationPoint(name: 'Uvinza', latitude: -5.1000, longitude: 30.3833),
      ],
    ),
    TanzaniaRegion(
      name: 'Katavi',
      districts: [
        LocationPoint(name: 'Mpanda', latitude: -6.3500, longitude: 31.0667),
        LocationPoint(name: 'Mlele', latitude: -6.0167, longitude: 31.4333),
        LocationPoint(name: 'Nsimbo', latitude: -6.2833, longitude: 31.1833),
      ],
    ),
    TanzaniaRegion(
      name: 'Rukwa',
      districts: [
        LocationPoint(name: 'Sumbawanga', latitude: -7.9667, longitude: 31.6167),
        LocationPoint(name: 'Kalambo', latitude: -8.6167, longitude: 31.2000),
        LocationPoint(name: 'Nkasi', latitude: -7.6833, longitude: 31.0500),
        LocationPoint(name: 'Sumbawanga District', latitude: -8.0000, longitude: 31.7000),
      ],
    ),
    TanzaniaRegion(
      name: 'Pwani (Coast)',
      districts: [
        LocationPoint(name: 'Kibaha', latitude: -6.7667, longitude: 38.9167),
        LocationPoint(name: 'Bagamoyo', latitude: -6.4333, longitude: 38.9000),
        LocationPoint(name: 'Chalinze', latitude: -6.7167, longitude: 38.2833),
        LocationPoint(name: 'Kisarawe', latitude: -6.9667, longitude: 38.9333),
        LocationPoint(name: 'Mafia', latitude: -7.9167, longitude: 39.6667),
        LocationPoint(name: 'Mkuranga', latitude: -7.2500, longitude: 39.2500),
        LocationPoint(name: 'Rufiji', latitude: -7.9500, longitude: 39.1333),
      ],
    ),
    TanzaniaRegion(
      name: 'Geita',
      districts: [
        LocationPoint(name: 'Geita', latitude: -2.8667, longitude: 32.2167),
        LocationPoint(name: 'Bukombe', latitude: -3.2500, longitude: 32.0000),
        LocationPoint(name: 'Chato', latitude: -2.6333, longitude: 31.7667),
        LocationPoint(name: 'Mbogwe', latitude: -3.4333, longitude: 31.5833),
        LocationPoint(name: 'Nyang\'hwale', latitude: -3.1167, longitude: 31.9167),
      ],
    ),
    TanzaniaRegion(
      name: 'Njombe',
      districts: [
        LocationPoint(name: 'Njombe', latitude: -9.3333, longitude: 34.7667),
        LocationPoint(name: 'Ludewa', latitude: -10.1333, longitude: 35.7000),
        LocationPoint(name: 'Makambako', latitude: -8.8833, longitude: 35.0000),
        LocationPoint(name: 'Makete', latitude: -9.0000, longitude: 34.8000),
        LocationPoint(name: 'Njombe District', latitude: -9.3500, longitude: 34.8000),
        LocationPoint(name: 'Wanging\'ombe', latitude: -9.1333, longitude: 34.5333),
      ],
    ),
    TanzaniaRegion(
      name: 'Songwe',
      districts: [
        LocationPoint(name: 'Vwawa', latitude: -9.1500, longitude: 33.3167),
        LocationPoint(name: 'Ileje', latitude: -9.5333, longitude: 33.2000),
        LocationPoint(name: 'Mbozi', latitude: -9.2333, longitude: 32.7333),
        LocationPoint(name: 'Momba', latitude: -9.2333, longitude: 33.2333),
        LocationPoint(name: 'Songwe', latitude: -9.5000, longitude: 33.0000),
      ],
    ),
  ];

  static TanzaniaRegion? getRegionByName(String name) {
    try {
      return regions.firstWhere((region) => region.name == name);
    } catch (e) {
      return null;
    }
  }

  static LocationPoint? getLocationByName(String regionName, String locationName) {
    final region = getRegionByName(regionName);
    if (region == null) return null;

    try {
      return region.districts.firstWhere((location) => location.name == locationName);
    } catch (e) {
      return null;
    }
  }

  static List<String> getRegionNames() {
    return regions.map((region) => region.name).toList();
  }

  static List<String> getDistrictNames(String regionName) {
    final region = getRegionByName(regionName);
    return region?.districts.map((district) => district.name).toList() ?? [];
  }

  /// Convert TanzaniaRegions to TanzaniaLocation objects for the new structure
  static List<TanzaniaLocation> toTanzaniaLocationList() {
    return regions.map((region) {
      // Calculate region center from districts
      if (region.districts.isNotEmpty) {
        double avgLat = region.districts.map((d) => d.latitude).reduce((a, b) => a + b) / region.districts.length;
        double avgLng = region.districts.map((d) => d.longitude).reduce((a, b) => a + b) / region.districts.length;
        
        return TanzaniaLocation(
          region: region.name,
          latitude: avgLat,
          longitude: avgLng,
          points: region.districts,
        );
      } else {
        return TanzaniaLocation(
          region: region.name,
          latitude: 0.0,
          longitude: 0.0,
          points: [],
        );
      }
    }).toList();
  }
}