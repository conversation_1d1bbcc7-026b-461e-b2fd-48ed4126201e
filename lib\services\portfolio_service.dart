import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/models/request.dart';

/// Service for managing portfolio data with enhanced backend integration
class PortfolioService {
  static final PortfolioService _instance = PortfolioService._internal();
  factory PortfolioService() => _instance;
  PortfolioService._internal();

  static PortfolioService get instance => _instance;

  /// Get comprehensive portfolio data
  Future<PortfolioData> getPortfolioData(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getUserPortfolio(userId, userRole.name);
      
      if (response['success'] == true && response['portfolio'] != null) {
        return PortfolioData.fromJson(response['portfolio']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get portfolio data');
      }
    } catch (e) {
      throw Exception('Failed to get portfolio data: $e');
    }
  }

  /// Get portfolio statistics with timeframe
  Future<PortfolioStats> getPortfolioStats(
    String userId, 
    UserRole userRole, 
    {String? timeframe}
  ) async {
    try {
      final response = await BackendApiService.getPortfolioStats(
        userId, 
        userRole.name, 
        timeframe ?? 'all_time'
      );
      
      if (response['success'] == true && response['stats'] != null) {
        return PortfolioStats.fromJson(response['stats']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get portfolio stats');
      }
    } catch (e) {
      throw Exception('Failed to get portfolio stats: $e');
    }
  }

  /// Get work history with pagination
  Future<List<WorkHistoryItem>> getWorkHistory(
    String userId, 
    UserRole userRole,
    {int? limit, int? offset}
  ) async {
    try {
      final response = await BackendApiService.getPortfolioWorkHistory(
        userId, 
        userRole.name, 
        limit ?? 20, 
        offset ?? 0
      );
      
      if (response['success'] == true && response['work_history'] != null) {
        final workHistoryData = response['work_history'] as List;
        return workHistoryData.map((item) => WorkHistoryItem.fromJson(item)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to get work history');
      }
    } catch (e) {
      throw Exception('Failed to get work history: $e');
    }
  }

  /// Get achievements and milestones
  Future<List<Achievement>> getAchievements(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getPortfolioAchievements(userId, userRole.name);
      
      if (response['success'] == true && response['achievements'] != null) {
        final achievementsData = response['achievements'] as List;
        return achievementsData.map((item) => Achievement.fromJson(item)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to get achievements');
      }
    } catch (e) {
      throw Exception('Failed to get achievements: $e');
    }
  }

  /// Get skills and ratings
  Future<SkillsProfile> getSkillsProfile(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getPortfolioSkills(userId, userRole.name);
      
      if (response['success'] == true && response['skills'] != null) {
        return SkillsProfile.fromJson(response['skills']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get skills profile');
      }
    } catch (e) {
      throw Exception('Failed to get skills profile: $e');
    }
  }

  /// Calculate dynamic statistics from requests
  Map<String, int> calculateStatsFromRequests(List<BaseRequest> requests) {
    final stats = <String, int>{};
    
    // Status counts
    for (final status in RequestStatus.values) {
      stats['status_${status.name}'] = requests.where((r) => r.status == status).length;
    }
    
    // Type counts  
    for (final type in RequestType.values) {
      stats['type_${type.name}'] = requests.where((r) => r.type == type).length;
    }
    
    // Time-based stats
    final now = DateTime.now();
    final thisMonth = requests.where((r) => 
      r.createdAt.month == now.month && r.createdAt.year == now.year).length;
    final lastMonth = requests.where((r) {
      final lastMonthDate = DateTime(now.year, now.month - 1);
      return r.createdAt.month == lastMonthDate.month && 
             r.createdAt.year == lastMonthDate.year;
    }).length;
    
    stats['this_month'] = thisMonth;
    stats['last_month'] = lastMonth;
    stats['total_earnings'] = requests.where((r) => r.status == RequestStatus.completed)
        .fold(0, (sum, r) => sum + r.payment.amount.toInt());
    
    return stats;
  }

  /// Get performance trends
  Future<PerformanceTrend> getPerformanceTrends(String userId, UserRole userRole) async {
    try {
      // Get data for last 6 months
      final trends = <String, List<double>>{};
      final now = DateTime.now();
      
      for (int i = 5; i >= 0; i--) {
        final targetMonth = DateTime(now.year, now.month - i);
        final monthKey = '${targetMonth.year}-${targetMonth.month.toString().padLeft(2, '0')}';
        
        final monthStats = await getPortfolioStats(
          userId, 
          userRole, 
          timeframe: monthKey
        );
        
        trends['earnings'] ??= [];
        trends['jobs'] ??= [];
        trends['rating'] ??= [];
        
        trends['earnings']!.add(monthStats.totalEarnings.toDouble());
        trends['jobs']!.add(monthStats.totalJobs.toDouble());
        trends['rating']!.add(monthStats.averageRating);
      }
      
      return PerformanceTrend(
        earningsTrend: trends['earnings'] ?? [],
        jobsTrend: trends['jobs'] ?? [],
        ratingTrend: trends['rating'] ?? [],
        period: 'last_6_months',
      );
    } catch (e) {
      // Return empty trends on error
      return PerformanceTrend(
        earningsTrend: [],
        jobsTrend: [],
        ratingTrend: [],
        period: 'last_6_months',
      );
    }
  }
}

/// Portfolio data model
class PortfolioData {
  final PortfolioStats stats;
  final List<WorkHistoryItem> recentWork;
  final List<Achievement> achievements;
  final SkillsProfile skills;
  final PerformanceTrend trends;

  PortfolioData({
    required this.stats,
    required this.recentWork,
    required this.achievements,
    required this.skills,
    required this.trends,
  });

  factory PortfolioData.fromJson(Map<String, dynamic> json) {
    return PortfolioData(
      stats: PortfolioStats.fromJson(json['stats'] ?? {}),
      recentWork: (json['recent_work'] as List? ?? [])
          .map((item) => WorkHistoryItem.fromJson(item)).toList(),
      achievements: (json['achievements'] as List? ?? [])
          .map((item) => Achievement.fromJson(item)).toList(),
      skills: SkillsProfile.fromJson(json['skills'] ?? {}),
      trends: PerformanceTrend.fromJson(json['trends'] ?? {}),
    );
  }
}

/// Portfolio statistics model
class PortfolioStats {
  final int totalJobs;
  final int completedJobs;
  final int totalEarnings;
  final double averageRating;
  final double successRate;
  final int thisMonthJobs;
  final int thisMonthEarnings;
  final Map<String, int> serviceBreakdown;
  final Map<String, int> statusBreakdown;

  PortfolioStats({
    required this.totalJobs,
    required this.completedJobs,
    required this.totalEarnings,
    required this.averageRating,
    required this.successRate,
    required this.thisMonthJobs,
    required this.thisMonthEarnings,
    required this.serviceBreakdown,
    required this.statusBreakdown,
  });

  factory PortfolioStats.fromJson(Map<String, dynamic> json) {
    return PortfolioStats(
      totalJobs: json['total_jobs'] ?? 0,
      completedJobs: json['completed_jobs'] ?? 0,
      totalEarnings: json['total_earnings'] ?? 0,
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      successRate: (json['success_rate'] ?? 0.0).toDouble(),
      thisMonthJobs: json['this_month_jobs'] ?? 0,
      thisMonthEarnings: json['this_month_earnings'] ?? 0,
      serviceBreakdown: Map<String, int>.from(json['service_breakdown'] ?? {}),
      statusBreakdown: Map<String, int>.from(json['status_breakdown'] ?? {}),
    );
  }
}

/// Work history item model
class WorkHistoryItem {
  final String id;
  final String title;
  final String description;
  final RequestType type;
  final RequestStatus status;
  final double amount;
  final double? rating;
  final String? review;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? customerName;
  final Map<String, dynamic>? metadata;

  WorkHistoryItem({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.amount,
    this.rating,
    this.review,
    required this.createdAt,
    this.completedAt,
    this.customerName,
    this.metadata,
  });

  factory WorkHistoryItem.fromJson(Map<String, dynamic> json) {
    return WorkHistoryItem(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: RequestType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RequestType.delivery,
      ),
      status: RequestStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => RequestStatus.pending,
      ),
      amount: (json['amount'] ?? 0).toDouble(),
      rating: json['rating']?.toDouble(),
      review: json['review'],
      createdAt: DateTime.parse(json['created_at']),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
      customerName: json['customer_name'],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Achievement model
class Achievement {
  final String id;
  final String title;
  final String description;
  final String iconName;
  final String category;
  final DateTime unlockedAt;
  final bool isNew;
  final Map<String, dynamic>? metadata;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    required this.category,
    required this.unlockedAt,
    this.isNew = false,
    this.metadata,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      iconName: json['icon_name'] ?? 'emoji_events',
      category: json['category'] ?? 'general',
      unlockedAt: DateTime.parse(json['unlocked_at']),
      isNew: json['is_new'] ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Skills profile model
class SkillsProfile {
  final List<Skill> skills;
  final double overallRating;
  final int totalReviews;
  final Map<String, double> categoryRatings;

  SkillsProfile({
    required this.skills,
    required this.overallRating,
    required this.totalReviews,
    required this.categoryRatings,
  });

  factory SkillsProfile.fromJson(Map<String, dynamic> json) {
    return SkillsProfile(
      skills: (json['skills'] as List? ?? [])
          .map((item) => Skill.fromJson(item)).toList(),
      overallRating: (json['overall_rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      categoryRatings: Map<String, double>.from(json['category_ratings'] ?? {}),
    );
  }
}

/// Individual skill model
class Skill {
  final String name;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isCertified;
  final DateTime? certificationDate;

  Skill({
    required this.name,
    required this.category,
    required this.rating,
    required this.reviewCount,
    this.isCertified = false,
    this.certificationDate,
  });

  factory Skill.fromJson(Map<String, dynamic> json) {
    return Skill(
      name: json['name'] ?? '',
      category: json['category'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['review_count'] ?? 0,
      isCertified: json['is_certified'] ?? false,
      certificationDate: json['certification_date'] != null 
          ? DateTime.parse(json['certification_date']) 
          : null,
    );
  }
}

/// Performance trend model
class PerformanceTrend {
  final List<double> earningsTrend;
  final List<double> jobsTrend;
  final List<double> ratingTrend;
  final String period;

  PerformanceTrend({
    required this.earningsTrend,
    required this.jobsTrend,
    required this.ratingTrend,
    required this.period,
  });

  factory PerformanceTrend.fromJson(Map<String, dynamic> json) {
    return PerformanceTrend(
      earningsTrend: List<double>.from(json['earnings_trend'] ?? []),
      jobsTrend: List<double>.from(json['jobs_trend'] ?? []),
      ratingTrend: List<double>.from(json['rating_trend'] ?? []),
      period: json['period'] ?? 'unknown',
    );
  }
}