import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:mlink/services/base_service.dart';
import 'package:mlink/supabase/supabase_config.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/utils/error_handler.dart';

/// Enhanced real-time service for M-Link application
/// Provides real-time updates for requests, user status, and messages
class EnhancedRealtimeService extends BaseService {
  // Private constructor for singleton pattern
  EnhancedRealtimeService._internal();
  
  static EnhancedRealtimeService? _instance;
  static EnhancedRealtimeService get instance => _instance ??= EnhancedRealtimeService._internal();

  final Map<String, RealtimeChannel> _channels = {};
  final Map<String, StreamController> _controllers = {};
  final Set<String> _activeSubscriptions = {};
  
  bool _isInitialized = false;
  
  /// Initialize the real-time service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('Initializing Enhanced Real-time Service');
      }
      _isInitialized = true;
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize real-time service: $e');
      }
      throw Exception('Failed to initialize real-time service');
    }
  }
  
  /// Subscribe to request updates for a specific type or all requests
  Stream<Map<String, dynamic>> subscribeToRequests({
    RequestType? type,
    RequestStatus? status,
    String? userId,
  }) {
    final channelKey = 'requests_${type?.name ?? 'all'}_${status?.name ?? 'all'}_${userId ?? 'all'}';
    
    if (_controllers.containsKey(channelKey)) {
      return _controllers[channelKey]!.stream as Stream<Map<String, dynamic>>;
    }
    
    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _controllers[channelKey] = controller;
    
    try {
      final channel = SupabaseConfig.client.channel(channelKey);
      
      // Subscribe to requests table changes
      channel.onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: 'requests',
        callback: (payload) {
          final data = payload.newRecord;
          
          // Apply filters
          if (type != null && data['type'] != type.name) return;
          if (status != null && data['status'] != status.name) return;
          if (userId != null && data['customer_id'] != userId) return;
          
          controller.add({
            'event': payload.eventType.name,
            'data': data,
            'old_data': payload.oldRecord,
            'table': 'requests',
            'timestamp': DateTime.now().toIso8601String(),
          });
        },
      );
      
      channel.subscribe((status, [error]) {
        if (status == RealtimeSubscribeStatus.subscribed) {
          _activeSubscriptions.add(channelKey);
          if (kDebugMode) {
            print('Successfully subscribed to $channelKey');
          }
        } else if (status == RealtimeSubscribeStatus.channelError) {
          if (kDebugMode) {
            print('Error subscribing to $channelKey: $error');
          }
          controller.addError(Exception('Subscription error: $error'));
        }
      });
      
      _channels[channelKey] = channel;
      
    } catch (e) {
      controller.addError(Exception('Failed to subscribe to requests: $e'));
    }
    
    return controller.stream;
  }
  
  /// Subscribe to user status updates
  Stream<Map<String, dynamic>> subscribeToUserStatus(String userId) {
    final channelKey = 'user_status_$userId';
    
    if (_controllers.containsKey(channelKey)) {
      return _controllers[channelKey]!.stream as Stream<Map<String, dynamic>>;
    }
    
    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _controllers[channelKey] = controller;
    
    try {
      final channel = SupabaseConfig.client.channel(channelKey);
      
      // Subscribe to user table changes for specific user
      channel.onPostgresChanges(
        event: PostgresChangeEvent.update,
        schema: 'public',
        table: 'users',
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'id',
          value: userId,
        ),
        callback: (payload) {
          final data = payload.newRecord;
          final oldData = payload.oldRecord;
          
          // Check if status-related fields changed
          if (_hasStatusChanged(data, oldData)) {
            controller.add({
              'event': 'user_status_update',
              'user_id': userId,
              'data': data,
              'old_data': oldData,
              'timestamp': DateTime.now().toIso8601String(),
            });
          }
        },
      );
      
      channel.subscribe();
      _channels[channelKey] = channel;
      
    } catch (e) {
      controller.addError(Exception('Failed to subscribe to user status: $e'));
    }
    
    return controller.stream;
  }
  
  /// Subscribe to agent location updates
  Stream<Map<String, dynamic>> subscribeToAgentLocations({
    double? centerLat,
    double? centerLng,
    double? radiusKm,
  }) {
    const channelKey = 'agent_locations';
    
    if (_controllers.containsKey(channelKey)) {
      return _controllers[channelKey]!.stream as Stream<Map<String, dynamic>>;
    }
    
    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _controllers[channelKey] = controller;
    
    try {
      final channel = SupabaseConfig.client.channel(channelKey);
      
      // Subscribe to agents table changes for location updates
      channel.onPostgresChanges(
        event: PostgresChangeEvent.update,
        schema: 'public',
        table: 'agents',
        callback: (payload) {
          final data = payload.newRecord;
          final oldData = payload.oldRecord;
          
          // Check if location changed
          if (_hasLocationChanged(data, oldData)) {
            // Apply radius filter if provided
            if (centerLat != null && centerLng != null && radiusKm != null) {
              final agentLat = data['current_location_lat'] as double?;
              final agentLng = data['current_location_lng'] as double?;
              
              if (agentLat != null && agentLng != null) {
                final distance = _calculateDistance(
                  centerLat, centerLng, agentLat, agentLng
                );
                
                if (distance > radiusKm) return;
              }
            }
            
            controller.add({
              'event': 'agent_location_update',
              'agent_id': data['id'],
              'data': data,
              'old_data': oldData,
              'timestamp': DateTime.now().toIso8601String(),
            });
          }
        },
      );
      
      channel.subscribe();
      _channels[channelKey] = channel;
      
    } catch (e) {
      controller.addError(Exception('Failed to subscribe to agent locations: $e'));
    }
    
    return controller.stream;
  }
  
  /// Subscribe to chat messages for a specific chat
  Stream<Map<String, dynamic>> subscribeToChatMessages(String chatId) {
    final channelKey = 'chat_messages_$chatId';
    
    if (_controllers.containsKey(channelKey)) {
      return _controllers[channelKey]!.stream as Stream<Map<String, dynamic>>;
    }
    
    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _controllers[channelKey] = controller;
    
    try {
      final channel = SupabaseConfig.client.channel(channelKey);
      
      // Subscribe to messages table changes for specific chat
      channel.onPostgresChanges(
        event: PostgresChangeEvent.insert,
        schema: 'public',
        table: 'messages',
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'chat_id',
          value: chatId,
        ),
        callback: (payload) {
          controller.add({
            'event': 'new_message',
            'chat_id': chatId,
            'data': payload.newRecord,
            'timestamp': DateTime.now().toIso8601String(),
          });
        },
      );
      
      channel.subscribe();
      _channels[channelKey] = channel;
      
    } catch (e) {
      controller.addError(Exception('Failed to subscribe to chat messages: $e'));
    }
    
    return controller.stream;
  }
  
  /// Subscribe to notifications for a specific user
  Stream<Map<String, dynamic>> subscribeToNotifications(String userId) {
    final channelKey = 'notifications_$userId';
    
    if (_controllers.containsKey(channelKey)) {
      return _controllers[channelKey]!.stream as Stream<Map<String, dynamic>>;
    }
    
    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _controllers[channelKey] = controller;
    
    try {
      final channel = SupabaseConfig.client.channel(channelKey);
      
      // Subscribe to notifications table changes for specific user
      channel.onPostgresChanges(
        event: PostgresChangeEvent.insert,
        schema: 'public',
        table: 'notifications',
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'user_id',
          value: userId,
        ),
        callback: (payload) {
          controller.add({
            'event': 'new_notification',
            'user_id': userId,
            'data': payload.newRecord,
            'timestamp': DateTime.now().toIso8601String(),
          });
        },
      );
      
      channel.subscribe();
      _channels[channelKey] = channel;
      
    } catch (e) {
      controller.addError(Exception('Failed to subscribe to notifications: $e'));
    }
    
    return controller.stream;
  }
  
  /// Subscribe to system-wide status updates (for admin dashboard)
  Stream<Map<String, dynamic>> subscribeToSystemStatus() {
    const channelKey = 'system_status';
    
    if (_controllers.containsKey(channelKey)) {
      return _controllers[channelKey]!.stream as Stream<Map<String, dynamic>>;
    }
    
    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _controllers[channelKey] = controller;
    
    try {
      final channel = SupabaseConfig.client.channel(channelKey);
      
      // Subscribe to multiple tables for system status
      final tables = ['requests', 'users', 'agents', 'technicians'];
      
      for (final table in tables) {
        channel.onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: table,
          callback: (payload) {
            controller.add({
              'event': payload.eventType.name,
              'table': table,
              'data': payload.newRecord,
              'old_data': payload.oldRecord,
              'timestamp': DateTime.now().toIso8601String(),
            });
          },
        );
      }
      
      channel.subscribe();
      _channels[channelKey] = channel;
      
    } catch (e) {
      controller.addError(Exception('Failed to subscribe to system status: $e'));
    }
    
    return controller.stream;
  }
  
  /// Unsubscribe from a specific channel
  Future<void> unsubscribe(String channelKey) async {
    try {
      if (_channels.containsKey(channelKey)) {
        await _channels[channelKey]!.unsubscribe();
        _channels.remove(channelKey);
        _activeSubscriptions.remove(channelKey);
      }
      
      if (_controllers.containsKey(channelKey)) {
        await _controllers[channelKey]!.close();
        _controllers.remove(channelKey);
      }
      
      if (kDebugMode) {
        print('Unsubscribed from $channelKey');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from $channelKey: $e');
      }
    }
  }
  
  /// Unsubscribe from all channels
  Future<void> unsubscribeAll() async {
    final channelKeys = List<String>.from(_channels.keys);
    
    for (final channelKey in channelKeys) {
      await unsubscribe(channelKey);
    }
    
    if (kDebugMode) {
      print('Unsubscribed from all real-time channels');
    }
  }
  
  /// Get active subscription count
  int get activeSubscriptionCount => _activeSubscriptions.length;
  
  /// Get list of active subscriptions
  List<String> get activeSubscriptions => List<String>.from(_activeSubscriptions);
  
  /// Check if a specific subscription is active
  bool isSubscriptionActive(String channelKey) => _activeSubscriptions.contains(channelKey);
  
  /// Dispose of the service and clean up resources
  Future<void> dispose() async {
    await unsubscribeAll();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('Enhanced Real-time Service disposed');
    }
  }
  
  // Private helper methods
  
  bool _hasStatusChanged(Map<String, dynamic> newData, Map<String, dynamic> oldData) {
    final statusFields = ['status', 'online_status', 'last_seen', 'is_available'];
    
    for (final field in statusFields) {
      if (newData[field] != oldData[field]) {
        return true;
      }
    }
    
    return false;
  }
  
  bool _hasLocationChanged(Map<String, dynamic> newData, Map<String, dynamic> oldData) {
    return newData['current_location_lat'] != oldData['current_location_lat'] ||
           newData['current_location_lng'] != oldData['current_location_lng'];
  }
  
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371; // km
    
    final dLat = _degreesToRadians(lat2 - lat1);
    final dLng = _degreesToRadians(lng2 - lng1);
    
    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLng / 2) * math.sin(dLng / 2);
    
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return earthRadius * c;
  }
  
  double _degreesToRadians(double degrees) => degrees * (math.pi / 180);
}