import 'package:mlink/supabase/supabase_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static NotificationService get instance => _instance;

  Future<List<Map<String, dynamic>>> getUserNotifications(String userId) async {
    try {
      final response = await SupabaseConfig.getUserNotifications(userId);
      return response;
    } catch (e) {
      throw Exception('Failed to get user notifications: $e');
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await SupabaseConfig.markNotificationAsRead(notificationId);
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  Future<void> markAllNotificationsAsRead(String userId) async {
    try {
      await SupabaseConfig.client
          .from('notifications')
          .update({'is_read': true})
          .eq('user_id', userId)
          .eq('is_read', false);
    } catch (e) {
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  Future<void> createNotification({
    required String userId,
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      await SupabaseConfig.createNotification({
        'user_id': userId,
        'title': title,
        'message': message,
        'type': type,
        'data': data,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('Failed to create notification: $e');
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await SupabaseConfig.client
          .from('notifications')
          .delete()
          .eq('id', notificationId);
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  Future<void> deleteAllNotifications(String userId) async {
    try {
      await SupabaseConfig.client
          .from('notifications')
          .delete()
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('Failed to delete all notifications: $e');
    }
  }

  Future<int> getUnreadNotificationCount(String userId) async {
    try {
      final response = await SupabaseConfig.client
          .from('notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('is_read', false);
      
      return response.length;
    } catch (e) {
      throw Exception('Failed to get unread notification count: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getNotificationsByType(String userId, String type) async {
    try {
      final response = await SupabaseConfig.client
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .eq('type', type)
          .order('created_at', ascending: false);
      
      return response;
    } catch (e) {
      throw Exception('Failed to get notifications by type: $e');
    }
  }

  // Real-time notification subscription
  RealtimeChannel subscribeToNotifications(String userId, Function(Map<String, dynamic>) onNotification) {
    return SupabaseConfig.subscribeToUserNotifications(userId, onNotification);
  }

  void unsubscribeFromNotifications(RealtimeChannel channel) {
    try {
      channel.unsubscribe();
    } catch (e) {
      print('Error unsubscribing from notifications: $e');
    }
  }

  // Predefined notification types for the app
  static const String typeRequestUpdate = 'request_update';
  static const String typePayment = 'payment';
  static const String typeSystem = 'system';
  static const String typePromotion = 'promotion';
  static const String typeReview = 'review';

  // Helper methods for creating specific types of notifications
  Future<void> createRequestUpdateNotification({
    required String userId,
    required String requestId,
    required String status,
    String? agentName,
    String? technicianName,
  }) async {
    String message;
    switch (status) {
      case 'accepted':
        message = 'Your request has been accepted';
        if (agentName != null) message += ' by $agentName';
        if (technicianName != null) message += ' by $technicianName';
        break;
      case 'in_progress':
        message = 'Your request is now in progress';
        break;
      case 'completed':
        message = 'Your request has been completed';
        break;
      case 'cancelled':
        message = 'Your request has been cancelled';
        break;
      default:
        message = 'Your request status has been updated to $status';
    }

    await createNotification(
      userId: userId,
      title: 'Request Update',
      message: message,
      type: typeRequestUpdate,
      data: {'request_id': requestId, 'status': status},
    );
  }

  Future<void> createPaymentNotification({
    required String userId,
    required double amount,
    required String currency,
    required String type, // 'received' or 'sent'
  }) async {
    await createNotification(
      userId: userId,
      title: 'Payment $type',
      message: 'Payment of $currency ${amount.toStringAsFixed(2)} has been $type',
      type: typePayment,
      data: {'amount': amount, 'currency': currency, 'payment_type': type},
    );
  }

  Future<void> createSystemNotification({
    required String userId,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    await createNotification(
      userId: userId,
      title: title,
      message: message,
      type: typeSystem,
      data: data,
    );
  }

  Future<void> createReviewNotification({
    required String userId,
    required String reviewerName,
    required int rating,
    required String requestId,
  }) async {
    await createNotification(
      userId: userId,
      title: 'New Review',
      message: '$reviewerName rated you $rating stars',
      type: typeReview,
      data: {'reviewer_name': reviewerName, 'rating': rating, 'request_id': requestId},
    );
  }

  // Batch notifications for agents/technicians
  Future<void> notifyAvailableAgents({
    required String requestId,
    required String requestType,
    required String location,
    required double amount,
  }) async {
    try {
      // Get all available agents
      final agents = await SupabaseConfig.client
          .from('agents')
          .select('user_id')
          .eq('is_available', true)
          .eq('is_verified', true);

      // Create notifications for all agents
      for (final agent in agents) {
        await createNotification(
          userId: agent['user_id'],
          title: 'New Request Available',
          message: 'A new $requestType request is available in $location for ${amount.toStringAsFixed(0)} TSH',
          type: typeRequestUpdate,
          data: {
            'request_id': requestId,
            'request_type': requestType,
            'location': location,
            'amount': amount,
          },
        );
      }
    } catch (e) {
      print('Error notifying available agents: $e');
    }
  }

  Future<void> notifyAvailableTechnicians({
    required String requestId,
    required String category,
    required String location,
    required double amount,
  }) async {
    try {
      // Get all available technicians for the specific category
      final technicians = await SupabaseConfig.client
          .from('technicians')
          .select('user_id')
          .eq('is_available', true)
          .eq('is_verified', true)
          .eq('category', category);

      // Create notifications for all technicians
      for (final technician in technicians) {
        await createNotification(
          userId: technician['user_id'],
          title: 'New $category Job Available',
          message: 'A new $category job is available in $location for ${amount.toStringAsFixed(0)} TSH',
          type: typeRequestUpdate,
          data: {
            'request_id': requestId,
            'category': category,
            'location': location,
            'amount': amount,
          },
        );
      }
    } catch (e) {
      print('Error notifying available technicians: $e');
    }
  }
}