import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface NotificationPayload {
  action: 'create' | 'mark_read' | 'mark_all_read' | 'delete' | 'get_user_notifications' | 'send_push'
  notificationData?: any
  notificationId?: string
  userId?: string
  pushToken?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, notificationData, notificationId, userId, pushToken } = await req.json() as NotificationPayload

    switch (action) {
      case 'create':
        return await createNotification(supabase, notificationData)
      case 'mark_read':
        return await markNotificationAsRead(supabase, notificationId!)
      case 'mark_all_read':
        return await markAllNotificationsAsRead(supabase, userId!)
      case 'delete':
        return await deleteNotification(supabase, notificationId!)
      case 'get_user_notifications':
        return await getUserNotifications(supabase, userId!)
      case 'send_push':
        return await sendPushNotification(supabase, notificationData, pushToken!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Notification error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function createNotification(supabase: any, notificationData: any) {
  const { data: notification, error } = await supabase
    .from('notifications')
    .insert({
      ...notificationData,
      created_at: new Date().toISOString(),
      is_read: false,
    })
    .select()
    .single()

  if (error) throw error

  // Send push notification if user has push token
  const { data: user } = await supabase
    .from('users')
    .select('push_token')
    .eq('id', notificationData.user_id)
    .single()

  if (user?.push_token) {
    await sendPushNotification(supabase, notificationData, user.push_token)
  }

  return new Response(
    JSON.stringify({ success: true, notification }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function markNotificationAsRead(supabase: any, notificationId: string) {
  const { data: notification, error } = await supabase
    .from('notifications')
    .update({ 
      is_read: true,
      read_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', notificationId)
    .select()
    .single()

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, notification }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function markAllNotificationsAsRead(supabase: any, userId: string) {
  const { data: notifications, error } = await supabase
    .from('notifications')
    .update({ 
      is_read: true,
      read_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .eq('is_read', false)
    .select()

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, count: notifications?.length || 0 }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function deleteNotification(supabase: any, notificationId: string) {
  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', notificationId)

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getUserNotifications(supabase: any, userId: string) {
  const { data: notifications, error } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(50)

  if (error) throw error

  const unreadCount = notifications?.filter(n => !n.is_read).length || 0

  return new Response(
    JSON.stringify({ success: true, notifications, unreadCount }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function sendPushNotification(supabase: any, notificationData: any, pushToken: string) {
  // This would integrate with a push notification service like Firebase Cloud Messaging
  // For now, we'll just log the attempt
  console.log('Sending push notification:', {
    token: pushToken,
    title: notificationData.title,
    body: notificationData.message,
    data: notificationData
  })

  // In a real implementation, you would send the push notification here
  // using Firebase Admin SDK or another push notification service
  
  try {
    // Example implementation with Firebase Cloud Messaging
    const fcmPayload = {
      notification: {
        title: notificationData.title,
        body: notificationData.message,
      },
      data: {
        type: notificationData.type,
        request_id: notificationData.request_id || '',
        user_id: notificationData.user_id || '',
      },
      token: pushToken,
    }

    // This is where you would actually send the push notification
    // const response = await admin.messaging().send(fcmPayload)
    
    return new Response(
      JSON.stringify({ success: true, message: 'Push notification sent' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Push notification error:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}