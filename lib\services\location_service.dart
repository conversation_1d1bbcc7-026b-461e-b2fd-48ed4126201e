import '../models/tanzania_locations.dart';
import '../supabase/lookup_service.dart';

/// Service for managing location data with Supabase as primary source
/// and hardcoded data as fallback
class LocationService {
  static bool _useSupabase = true;
  static List<TanzaniaLocation>? _cachedLocations;

  /// Get Tanzania locations from Supabase or fallback to hardcoded data
  static Future<List<TanzaniaLocation>> getTanzaniaLocations() async {
    if (_useSupabase) {
      try {
        // Try to get from Supabase first
        final locations = await LookupService.getTanzaniaLocations();
        if (locations.isNotEmpty) {
          _cachedLocations = locations;
          return locations;
        }
      } catch (e) {
        print('LocationService: Error getting locations from Supabase: $e');
        // Fall back to hardcoded data
        _useSupabase = false;
      }
    }
    
    // Use hardcoded data as fallback
    if (_cachedLocations == null) {
      _cachedLocations = TanzaniaLocations.toTanzaniaLocationList();
    }
    return _cachedLocations!;
  }

  /// Get region names
  static Future<List<String>> getRegionNames() async {
    if (_useSupabase) {
      try {
        final regions = await LookupService.getActiveRegions();
        return regions.map((r) => r['name'] as String).toList();
      } catch (e) {
        print('LocationService: Error getting regions from Supabase: $e');
      }
    }
    
    // Fallback to hardcoded data
    return TanzaniaLocations.getRegionNames();
  }

  /// Get district names for a region
  static Future<List<String>> getDistrictNames(String regionName) async {
    if (_useSupabase) {
      try {
        final regions = await LookupService.getActiveRegions();
        final region = regions.firstWhere(
          (r) => r['name'] == regionName,
          orElse: () => <String, dynamic>{},
        );
        
        if (region.isNotEmpty) {
          final districts = await LookupService.getDistrictsByRegion(region['id']);
          return districts.map((d) => d['name'] as String).toList();
        }
      } catch (e) {
        print('LocationService: Error getting districts from Supabase: $e');
      }
    }
    
    // Fallback to hardcoded data
    return TanzaniaLocations.getDistrictNames(regionName);
  }

  /// Get location point by region and district name
  static Future<LocationPoint?> getLocationByName(String regionName, String districtName) async {
    if (_useSupabase) {
      try {
        final locations = await getTanzaniaLocations();
        final region = locations.firstWhere(
          (l) => l.region == regionName,
          orElse: () => TanzaniaLocation(region: '', latitude: 0, longitude: 0, points: []),
        );
        
        if (region.points.isNotEmpty) {
          try {
            return region.points.firstWhere((p) => p.name == districtName);
          } catch (e) {
            // Point not found
          }
        }
      } catch (e) {
        print('LocationService: Error getting location by name from Supabase: $e');
      }
    }
    
    // Fallback to hardcoded data
    return TanzaniaLocations.getLocationByName(regionName, districtName);
  }

  /// Force refresh from Supabase
  static void forceSupabaseRefresh() {
    _useSupabase = true;
    _cachedLocations = null;
  }

  /// Check if we have lookup data in Supabase
  static Future<bool> hasSupabaseData() async {
    try {
      return await LookupService.hasLookupData();
    } catch (e) {
      return false;
    }
  }
}