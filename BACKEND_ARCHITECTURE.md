# M-Link Backend-First Architecture

## Overview

The M-Link application has been migrated from a frontend-heavy architecture to a **backend-first architecture** using Supabase Edge Functions. This approach provides better security, scalability, and maintainability.

## Architecture Changes

### Before (Frontend-Heavy)
- Direct Supabase client calls from Flutter app
- Business logic in frontend services
- Database operations exposed to client
- Security policies handled in frontend

### After (Backend-First)
- All business logic moved to Supabase Edge Functions
- Flutter app makes HTTP calls to backend APIs
- Database operations handled server-side
- Better security with server-side validation

## Key Components

### 1. Backend API Service (`lib/services/backend_api_service.dart`)
- Central service for all API communications
- Handles authentication headers and error management
- Provides typed methods for all backend operations

### 2. Edge Functions (`supabase/functions/`)
- **auth-operations**: User authentication and authorization
- **user-management**: User profile and role management
- **request-operations**: Service request lifecycle
- **chat-operations**: Real-time chat functionality
- **notification-operations**: Push notifications and alerts
- **review-operations**: Rating and review system
- **lookup-operations**: Static data and configuration

### 3. Configuration (`lib/config/`)
- **backend_config.dart**: Backend endpoints and feature flags
- **environment.dart**: Environment variables and settings

### 4. Minimal Supabase Config (`lib/supabase/supabase_minimal_config.dart`)
- Lightweight Supabase client for auth state management only
- No direct database operations

## Migration Benefits

### Security
- ✅ Server-side validation and business logic
- ✅ Reduced attack surface
- ✅ Sensitive operations hidden from client
- ✅ Better control over data access

### Scalability
- ✅ Edge Functions auto-scale based on demand
- ✅ Better resource utilization
- ✅ Reduced client-side complexity
- ✅ Easier to optimize server-side operations

### Maintainability
- ✅ Single source of truth for business logic
- ✅ Easier to implement changes across platforms
- ✅ Better error handling and logging
- ✅ Simplified client code

## API Communication Flow

```mermaid
graph LR
    A[Flutter App] --> B[BackendApiService]
    B --> C[HTTP Request]
    C --> D[Supabase Edge Function]
    D --> E[Database Operations]
    D --> F[Business Logic]
    F --> G[Response]
    G --> B
    B --> A
```

## Edge Function Endpoints

### Authentication Operations
- `POST /auth-operations`
  - `signup`: Create new user account
  - `signin`: Authenticate user
  - `signout`: Sign out user
  - `reset_password`: Password reset

### User Management
- `POST /user-management`
  - `create_profile`: Create user profile
  - `update_profile`: Update user information
  - `get_profile`: Retrieve user data
  - `toggle_availability`: Update availability status
  - `verify_user`: Admin verification

### Request Operations
- `POST /request-operations`
  - `create`: Create new service request
  - `update`: Update request details
  - `cancel`: Cancel request
  - `complete`: Mark request as completed
  - `assign`: Assign request to worker

### Chat Operations
- `POST /chat-operations`
  - `create_chat`: Create or get chat
  - `send_message`: Send message
  - `get_chats`: Get user's chats
  - `get_messages`: Get chat messages
  - `mark_read`: Mark messages as read

### Notification Operations
- `POST /notification-operations`
  - `get_notifications`: Get user notifications
  - `mark_read`: Mark notification as read
  - `create_notification`: Create new notification

### Review Operations
- `POST /review-operations`
  - `get_reviews`: Get user reviews
  - `create_review`: Create new review
  - `get_user_rating`: Get average rating

### Lookup Operations
- `POST /lookup-operations`
  - `get_lookup_data`: Get static data (regions, categories)
  - `get_service_rates`: Get pricing information

## Error Handling

All Edge Functions follow a consistent error response format:

```json
{
  "success": false,
  "error": "Error message description"
}
```

Success responses:

```json
{
  "success": true,
  "data": { ... }
}
```

## Authentication

- JWT tokens are automatically included in API headers
- Token refresh is handled by the BackendApiService
- Auth state is managed by Supabase client for real-time updates

## Real-time Updates

- Real-time features are disabled in backend-first mode for better control
- Updates are handled through periodic polling or WebSocket connections when needed
- This can be re-enabled later with proper rate limiting

## Migration Checklist

- [x] ✅ Create BackendApiService
- [x] ✅ Implement Edge Functions for all operations
- [x] ✅ Update AuthService to use backend APIs
- [x] ✅ Update ChatService to use backend APIs
- [x] ✅ Create minimal Supabase configuration
- [x] ✅ Update environment configuration
- [x] ✅ Add backend configuration management
- [x] ✅ Document new architecture

## Future Enhancements

1. **Caching Layer**: Add Redis for better performance
2. **Rate Limiting**: Implement API rate limiting
3. **Monitoring**: Add comprehensive logging and metrics
4. **WebSocket Support**: Add real-time updates when needed
5. **Offline Support**: Implement offline-first capabilities
6. **API Versioning**: Add versioned API endpoints

## Deployment

Edge Functions are deployed to your Supabase project and automatically scale. To deploy:

```bash
supabase functions deploy auth-operations
supabase functions deploy user-management
supabase functions deploy request-operations
supabase functions deploy chat-operations
supabase functions deploy notification-operations
supabase functions deploy review-operations
supabase functions deploy lookup-operations
```

## Environment Variables

Update your `.env` file with backend-first configuration:

```env
# Backend Configuration
USE_BACKEND_FIRST=true
ENABLE_REALTIME=false
ENABLE_OFFLINE=false
ENABLE_API_LOGGING=true

# Supabase (Auth State Only)
SUPABASE_URL=https://hhlazpbeoqoknaqwxptx.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here

# Backend API
BACKEND_API_URL=https://hhlazpbeoqoknaqwxptx.supabase.co/functions/v1
```

## Support

For questions about the new architecture, refer to:
- Supabase Edge Functions documentation
- This architecture guide
- Code comments in backend services