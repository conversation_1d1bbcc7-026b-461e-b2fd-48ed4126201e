import 'package:flutter/material.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/models/agent.dart';
import 'package:mlink/models/technician.dart';

class AgentJobCard extends StatelessWidget {
  final BaseRequest job;
  final VoidCallback onAccept;
  final VoidCallback onViewDetails;

  const AgentJobCard({
    super.key,
    required this.job,
    required this.onAccept,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with job type and priority
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getJobTypeColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getJobTypeLabel(),
                    style: TextStyle(
                      color: _getJobTypeColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (_isUrgent()) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'URGENT',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                Text(
                  'TSh ${job.payment.amount.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Job description
            Text(
              job.description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            
            // Location details
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    job.pickupLocation.address ?? 'Pickup location',
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            if (job.dropoffLocation != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.flag,
                    size: 16,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      job.dropoffLocation!.address ?? 'Dropoff location',
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
            
            // Special requirements or items
            if (job is BuyForMeRequest) ...[
              const SizedBox(height: 8),
              Text(
                'Items: ${(job as BuyForMeRequest).items.take(3).join(', ')}${(job as BuyForMeRequest).items.length > 3 ? '...' : ''}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
            
            if (job is DeliveryRequest) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if ((job as DeliveryRequest).estimatedWeight != null) ...[
                    Icon(
                      Icons.fitness_center,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${(job as DeliveryRequest).estimatedWeight!.toStringAsFixed(1)} kg',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  Icon(
                    _getVehicleIcon((job as DeliveryRequest).preferredVehicle),
                    size: 14,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    (job as DeliveryRequest).preferredVehicle.name.replaceAll('_', ' ').toUpperCase(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ],
            
            const SizedBox(height: 12),
            
            // Time and action buttons
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  _getTimeAgo(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: onViewDetails,
                  child: const Text('Details'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: onAccept,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                  child: const Text('Accept'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getJobTypeColor() {
    switch (job.type) {
      case RequestType.delivery:
        return Colors.blue;
      case RequestType.buyForMe:
        return Colors.green;
      case RequestType.technicianService:
        return Colors.orange;
    }
  }

  String _getJobTypeLabel() {
    switch (job.type) {
      case RequestType.delivery:
        return 'DELIVERY';
      case RequestType.buyForMe:
        return 'BUY FOR ME';
      case RequestType.technicianService:
        return 'SERVICE';
    }
  }

  bool _isUrgent() {
    if (job is DeliveryRequest) {
      return (job as DeliveryRequest).priority == DeliveryPriority.urgent;
    }
    if (job is TechnicianRequest) {
      return (job as TechnicianRequest).isUrgent;
    }
    return false;
  }

  IconData _getVehicleIcon(VehicleType vehicle) {
    switch (vehicle) {
      case VehicleType.bodaboda:
        return Icons.two_wheeler;
      case VehicleType.bicycle:
        return Icons.pedal_bike;
      case VehicleType.car:
        return Icons.directions_car;
      case VehicleType.truck:
        return Icons.local_shipping;
      case VehicleType.van:
        return Icons.airport_shuttle;
      case VehicleType.bajaji:
        return Icons.directions_car;
      case VehicleType.pickup:
        return Icons.local_shipping;
      default:
        return Icons.delivery_dining;
    }
  }

  String _getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(job.createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class MyJobCard extends StatelessWidget {
  final BaseRequest job;
  final Function(RequestStatus) onUpdateStatus;
  final VoidCallback onViewDetails;

  const MyJobCard({
    super.key,
    required this.job,
    required this.onUpdateStatus,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status and payment
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job.status.name.toUpperCase().replaceAll('_', ' '),
                    style: TextStyle(
                      color: _getStatusColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  'TSh ${job.payment.amount.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Job description
            Text(
              job.description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            
            // Progress indicator
            LinearProgressIndicator(
              value: _getProgressValue(),
              backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
            ),
            const SizedBox(height: 12),
            
            // Action buttons based on status
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onViewDetails,
                    child: const Text('View Details'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _showStatusUpdateDialog(context),
                    child: Text(_getNextActionLabel()),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (job.status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.accepted:
        return Colors.blue;
      case RequestStatus.inProgress:
        return Colors.purple;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.failed:
        return Colors.red;
      case RequestStatus.paymentPending:
        return Colors.amber;
    }
  }

  double _getProgressValue() {
    switch (job.status) {
      case RequestStatus.pending:
        return 0.1;
      case RequestStatus.accepted:
        return 0.3;
      case RequestStatus.inProgress:
        return 0.7;
      case RequestStatus.completed:
        return 1.0;
      case RequestStatus.cancelled:
      case RequestStatus.failed:
        return 0.0;
      case RequestStatus.paymentPending:
        return 0.9;
    }
  }

  String _getNextActionLabel() {
    switch (job.status) {
      case RequestStatus.accepted:
        return 'Start Job';
      case RequestStatus.inProgress:
        return 'Mark Complete';
      case RequestStatus.completed:
        return 'Completed';
      case RequestStatus.paymentPending:
        return 'Awaiting Payment';
      default:
        return 'Update Status';
    }
  }

  void _showStatusUpdateDialog(BuildContext context) {
    if (job.status == RequestStatus.completed || 
        job.status == RequestStatus.cancelled ||
        job.status == RequestStatus.paymentPending) {
      return;
    }

    RequestStatus? nextStatus;
    String dialogTitle = '';
    String dialogContent = '';

    if (job.status == RequestStatus.accepted) {
      nextStatus = RequestStatus.inProgress;
      dialogTitle = 'Start Job';
      dialogContent = 'Mark this job as in progress?';
    } else if (job.status == RequestStatus.inProgress) {
      nextStatus = RequestStatus.completed;
      dialogTitle = 'Complete Job';
      dialogContent = 'Mark this job as completed?';
    }

    if (nextStatus == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(dialogTitle),
        content: Text(dialogContent),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onUpdateStatus(nextStatus!);
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}

class TechnicianJobCard extends StatelessWidget {
  final TechnicianRequest job;
  final VoidCallback onAccept;
  final VoidCallback onViewDetails;

  const TechnicianJobCard({
    super.key,
    required this.job,
    required this.onAccept,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with category and urgency
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getCategoryColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job.category.name.toUpperCase().replaceAll('_', ' '),
                    style: TextStyle(
                      color: _getCategoryColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (job.isUrgent) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'URGENT',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (job.fixedRate != null) ...[
                      Text(
                        'TSh ${job.fixedRate!.toStringAsFixed(0)}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        'Fixed Rate',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ] else if (job.hourlyRate != null) ...[
                      Text(
                        'TSh ${job.hourlyRate!.toStringAsFixed(0)}/hr',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      if (job.estimatedHours != null) ...[
                        Text(
                          '≈${job.estimatedHours}h',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Job description
            Text(
              job.description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            
            // Location
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    job.pickupLocation.address ?? 'Service location',
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            // Required skills and tools
            if (job.skillRequired != null || job.requiredTools.isNotEmpty) ...[
              const SizedBox(height: 8),
              if (job.skillRequired != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.build,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Skill: ${job.skillRequired}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],
              if (job.requiredTools.isNotEmpty) ...[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.construction,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'Tools: ${job.requiredTools.take(3).join(', ')}${job.requiredTools.length > 3 ? '...' : ''}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
            
            const SizedBox(height: 12),
            
            // Time and action buttons
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  _getTimeAgo(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: onViewDetails,
                  child: const Text('Details'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: onAccept,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                  child: const Text('Accept'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor() {
    switch (job.category) {
      case TechnicianCategory.electrician:
        return Colors.amber;
      case TechnicianCategory.plumber:
        return Colors.blue;
      case TechnicianCategory.mechanic:
        return Colors.grey;
      case TechnicianCategory.carpenter:
        return Colors.brown;
      case TechnicianCategory.painter:
        return Colors.purple;
      case TechnicianCategory.cleaner:
        return Colors.teal;
      case TechnicianCategory.gardener:
        return Colors.green;
      case TechnicianCategory.applianceRepair:
        return Colors.indigo;
      case TechnicianCategory.computerRepair:
        return Colors.cyan;
      case TechnicianCategory.phoneRepair:
        return Colors.pink;
      case TechnicianCategory.hvac:
        return Colors.orange;
      case TechnicianCategory.other:
        return Colors.grey;
    }
  }

  String _getTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(job.createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class JobDetailsDialog extends StatelessWidget {
  final BaseRequest job;

  const JobDetailsDialog({
    super.key,
    required this.job,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Job Details'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Job type and status
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job.type.name.toUpperCase().replaceAll('_', ' '),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job.status.name.toUpperCase().replaceAll('_', ' '),
                    style: TextStyle(
                      color: _getStatusColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Description
            Text(
              'Description',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(job.description),
            const SizedBox(height: 16),
            
            // Payment
            Text(
              'Payment',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Amount:'),
                Text(
                  'TSh ${job.payment.amount.toStringAsFixed(0)}',
                  style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.green),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Method:'),
                Text(job.payment.method.name.toUpperCase().replaceAll('_', ' ')),
              ],
            ),
            const SizedBox(height: 16),
            
            // Locations
            Text(
              'Locations',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(job.pickupLocation.address ?? 'Pickup location'),
                ),
              ],
            ),
            if (job.dropoffLocation != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.flag, size: 16, color: Theme.of(context).colorScheme.secondary),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(job.dropoffLocation!.address ?? 'Dropoff location'),
                  ),
                ],
              ),
            ],
            
            // Specific details based on job type
            if (job is DeliveryRequest) ..._buildDeliveryDetails(job as DeliveryRequest),
            if (job is BuyForMeRequest) ..._buildBuyForMeDetails(job as BuyForMeRequest),
            if (job is TechnicianRequest) ..._buildTechnicianDetails(job as TechnicianRequest),
            
            const SizedBox(height: 16),
            
            // Timestamps
            Text(
              'Timeline',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Created:'),
                Text(_formatDateTime(job.createdAt)),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Updated:'),
                Text(_formatDateTime(job.updatedAt)),
              ],
            ),
            if (job.scheduledAt != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Scheduled:'),
                  Text(_formatDateTime(job.scheduledAt!)),
                ],
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  List<Widget> _buildDeliveryDetails(DeliveryRequest delivery) {
    return [
      const SizedBox(height: 16),
      Text(
        'Delivery Details',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      const SizedBox(height: 4),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Vehicle:'),
          Text(delivery.preferredVehicle.name.replaceAll('_', ' ').toUpperCase()),
        ],
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Priority:'),
          Text(delivery.priority.name.toUpperCase()),
        ],
      ),
      if (delivery.estimatedWeight != null) ...[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Weight:'),
            Text('${delivery.estimatedWeight!.toStringAsFixed(1)} kg'),
          ],
        ),
      ],
      if (delivery.packageType != null) ...[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Package Type:'),
            Text(delivery.packageType!),
          ],
        ),
      ],
      if (delivery.recipientName != null) ...[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Recipient:'),
            Text(delivery.recipientName!),
          ],
        ),
      ],
    ];
  }

  List<Widget> _buildBuyForMeDetails(BuyForMeRequest buyForMe) {
    return [
      const SizedBox(height: 16),
      Text(
        'Shopping Details',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      const SizedBox(height: 4),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Store:'),
          Expanded(
            child: Text(
              buyForMe.storeLocation,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
      if (buyForMe.maxBudget != null) ...[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Max Budget:'),
            Text('TSh ${buyForMe.maxBudget!.toStringAsFixed(0)}'),
          ],
        ),
      ],
      const SizedBox(height: 8),
      Text(
        'Items (${buyForMe.items.length}):',
        style: TextStyle(fontWeight: FontWeight.w600),
      ),
      const SizedBox(height: 4),
      ...buyForMe.items.map((item) => Padding(
        padding: const EdgeInsets.only(left: 16, bottom: 2),
        child: Row(
          children: [
            Text('• ', style: TextStyle(color: Colors.grey[600])),
            Expanded(child: Text(item)),
          ],
        ),
      )),
    ];
  }

  List<Widget> _buildTechnicianDetails(TechnicianRequest technician) {
    return [
      const SizedBox(height: 16),
      Text(
        'Service Details',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      const SizedBox(height: 4),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Category:'),
          Text(technician.category.name.replaceAll('_', ' ').toUpperCase()),
        ],
      ),
      if (technician.skillRequired != null) ...[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Skill Required:'),
            Expanded(
              child: Text(
                technician.skillRequired!,
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
      ],
      if (technician.estimatedHours != null) ...[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Estimated Hours:'),
            Text('${technician.estimatedHours}h'),
          ],
        ),
      ],
      if (technician.requiredTools.isNotEmpty) ...[
        const SizedBox(height: 8),
        Text(
          'Required Tools:',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 4),
        ...technician.requiredTools.map((tool) => Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 2),
          child: Row(
            children: [
              Text('• ', style: TextStyle(color: Colors.grey[600])),
              Expanded(child: Text(tool)),
            ],
          ),
        )),
      ],
    ];
  }

  Color _getStatusColor() {
    switch (job.status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.accepted:
        return Colors.blue;
      case RequestStatus.inProgress:
        return Colors.purple;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.failed:
        return Colors.red;
      case RequestStatus.paymentPending:
        return Colors.amber;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}