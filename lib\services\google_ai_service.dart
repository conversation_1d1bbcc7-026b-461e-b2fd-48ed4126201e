import 'dart:convert';
import 'dart:typed_data';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:mlink/google_ai/google_ai_config.dart';
import 'package:mlink/models/user.dart';

class GoogleAIService {
  static final GoogleAIService _instance = GoogleAIService._internal();
  factory GoogleAIService() => _instance;
  GoogleAIService._internal();

  static GoogleAIService get instance => _instance;

  // System prompt for M-link AI assistant
  static const String _systemPrompt = """
You are M-link AI Assistant, a helpful AI assistant for the M-link platform - a unified digital platform connecting customers, delivery agents, and technicians in Tanzania.

Your role is to help users with:
- Understanding how to use the M-link platform
- Guidance on making delivery requests
- Information about technician services
- General support and troubleshooting
- Connecting with appropriate services

You should be friendly, professional, and knowledgeable about the M-link platform. When users ask about specific services, guide them to the appropriate sections of the app.

Always respond in a helpful and conversational manner. If you don't know something specific about the platform, acknowledge it and suggest they contact customer support.

Remember to:
- Be concise but informative
- Use simple language
- Provide actionable guidance
- Be encouraging and supportive
- Suggest using specific features when relevant
""";

  Future<String> generateResponse({
    required String userMessage,
    List<Map<String, dynamic>>? conversationHistory,
    User? currentUser,
  }) async {
    try {
      // Build the full conversation context
      String fullPrompt = _systemPrompt + '\n\n';
      
      // Add conversation history if provided
      if (conversationHistory != null && conversationHistory.isNotEmpty) {
        for (final message in conversationHistory) {
          final role = message['role'] as String;
          final content = message['content'] as String;
          
          if (role == 'user') {
            fullPrompt += 'User: $content\n';
          } else if (role == 'assistant') {
            fullPrompt += 'Assistant: $content\n';
          }
        }
      }
      
      // Add current user message
      fullPrompt += 'User: $userMessage\n\nAssistant:';

      final response = await GoogleAIConfig.generateResponse(
        prompt: fullPrompt,
        temperature: 0.7,
        maxTokens: 1000,
      );

      return response;
    } catch (e) {
      final userFriendlyError = GoogleAIConfig.getUserFriendlyError(e);
      throw GoogleAIException(userFriendlyError);
    }
  }

  Future<String> generateHelpResponse({
    required String helpTopic,
    User? currentUser,
  }) async {
    try {
      final prompt = """
$_systemPrompt

The user needs help with: $helpTopic

Provide a helpful, step-by-step guide specific to the M-link platform. Include:
1. Clear instructions
2. What features to use
3. Any important tips or warnings
4. Next steps they should take

Keep the response concise but comprehensive.
""";

      final response = await GoogleAIConfig.generateResponse(
        prompt: prompt,
        temperature: 0.3,
        maxTokens: 800,
      );

      return response;
    } catch (e) {
      final userFriendlyError = GoogleAIConfig.getUserFriendlyError(e);
      throw GoogleAIException(userFriendlyError);
    }
  }

  Future<String> generateServiceRecommendation({
    required String userNeed,
    required String location,
    User? currentUser,
  }) async {
    try {
      final prompt = """
$_systemPrompt

The user needs: $userNeed
Location: $location

Based on this, recommend the most appropriate M-link service:
- Delivery service for package/food delivery
- Technician service for repairs, maintenance, or technical work
- Buy-for-me service for shopping assistance

Provide:
1. Which service is most suitable
2. Why this service fits their need
3. How to access it in the app
4. What information they'll need to provide
5. Estimated timeframe and cost considerations

Be specific and actionable.
""";

      final response = await GoogleAIConfig.generateResponse(
        prompt: prompt,
        temperature: 0.5,
        maxTokens: 1000,
      );

      return response;
    } catch (e) {
      final userFriendlyError = GoogleAIConfig.getUserFriendlyError(e);
      throw GoogleAIException(userFriendlyError);
    }
  }

  Future<String> generateTroubleshootingHelp({
    required String issue,
    User? currentUser,
  }) async {
    try {
      final prompt = """
$_systemPrompt

The user is experiencing this issue: $issue

Provide troubleshooting steps for the M-link platform:
1. Common causes of this issue
2. Step-by-step solutions to try
3. When to contact customer support
4. How to prevent this issue in the future

Be practical and easy to follow.
""";

      final response = await GoogleAIConfig.generateResponse(
        prompt: prompt,
        temperature: 0.3,
        maxTokens: 1000,
      );

      return response;
    } catch (e) {
      final userFriendlyError = GoogleAIConfig.getUserFriendlyError(e);
      throw GoogleAIException(userFriendlyError);
    }
  }

  Future<List<String>> generateQuickSuggestions({
    User? currentUser,
  }) async {
    try {
      final prompt = """
$_systemPrompt

Generate 4 quick suggestion prompts that users might want to ask the M-link AI assistant.
These should be common questions or requests related to:
- Using the platform
- Making requests
- Understanding services
- Getting help

Return only the suggestions, one per line, without numbers or bullets.
Each suggestion should be a complete question or request.
""";

      final response = await GoogleAIConfig.generateResponse(
        prompt: prompt,
        temperature: 0.8,
        maxTokens: 200,
      );

      return response.split('\n').where((line) => line.trim().isNotEmpty).toList();
    } catch (e) {
      // Return fallback suggestions if AI fails
      return [
        'How do I make a delivery request?',
        'What technician services are available?',
        'How do I track my request?',
        'How do I contact customer support?',
      ];
    }
  }

  Future<String> generateContextualResponse({
    required String userMessage,
    required String userContext,
    List<Map<String, dynamic>>? conversationHistory,
    User? currentUser,
  }) async {
    try {
      String fullPrompt = _systemPrompt + '\n\n';
      fullPrompt += 'User context: $userContext\n\n';
      
      // Add conversation history if provided
      if (conversationHistory != null && conversationHistory.isNotEmpty) {
        for (final message in conversationHistory) {
          final role = message['role'] as String;
          final content = message['content'] as String;
          
          if (role == 'user') {
            fullPrompt += 'User: $content\n';
          } else if (role == 'assistant') {
            fullPrompt += 'Assistant: $content\n';
          }
        }
      }
      
      fullPrompt += 'User: $userMessage\n\n';
      fullPrompt += 'Provide a response that takes into account the user\'s current context and situation.\n\nAssistant:';

      final response = await GoogleAIConfig.generateResponse(
        prompt: fullPrompt,
        temperature: 0.7,
        maxTokens: 1000,
      );

      return response;
    } catch (e) {
      final userFriendlyError = GoogleAIConfig.getUserFriendlyError(e);
      throw GoogleAIException(userFriendlyError);
    }
  }

  Future<Map<String, dynamic>> analyzeMessageSentiment({
    required String message,
    User? currentUser,
  }) async {
    try {
      final schema = {
        'type': 'object',
        'properties': {
          'sentiment': {'type': 'string', 'enum': ['positive', 'negative', 'neutral']},
          'confidence': {'type': 'number', 'minimum': 0, 'maximum': 1},
          'toxicity': {'type': 'number', 'minimum': 0, 'maximum': 1},
          'categories': {'type': 'array', 'items': {'type': 'string'}},
          'needs_moderation': {'type': 'boolean'},
          'reason': {'type': 'string'}
        },
        'required': ['sentiment', 'confidence', 'toxicity', 'categories', 'needs_moderation']
      };

      final prompt = '''
Analyze the sentiment and content of this message for moderation purposes:

Message: "$message"

Categories can include: harassment, hate, violence, self-harm, sexual, spam, inappropriate

Return a JSON response with sentiment analysis and moderation assessment.
''';

      final response = await GoogleAIConfig.generateStructuredResponse(
        prompt: prompt,
        schema: schema,
        temperature: 0.3,
        maxTokens: 300,
      );

      return response;
    } catch (e) {
      // Return safe defaults if analysis fails
      return {
        'sentiment': 'neutral',
        'confidence': 0.5,
        'toxicity': 0.0,
        'categories': [],
        'needs_moderation': false,
        'reason': ''
      };
    }
  }

  Future<String> generateResponseWithImage({
    required String prompt,
    required Uint8List imageBytes,
    String mimeType = 'image/jpeg',
    User? currentUser,
  }) async {
    try {
      final fullPrompt = '''
$_systemPrompt

$prompt

Please analyze the image and provide a helpful response based on the context of the M-link platform.
''';

      final response = await GoogleAIConfig.generateResponseWithImage(
        prompt: fullPrompt,
        imageBytes: imageBytes,
        mimeType: mimeType,
        temperature: 0.4,
        maxTokens: 1000,
      );

      return response;
    } catch (e) {
      final userFriendlyError = GoogleAIConfig.getUserFriendlyError(e);
      throw GoogleAIException(userFriendlyError);
    }
  }

  String getWelcomeMessage({User? currentUser}) {
    final userName = currentUser?.fullName?.isNotEmpty == true 
        ? currentUser!.fullName!.split(' ').first 
        : 'there';
    return """
Hello $userName! 👋

I'm your M-link AI Assistant powered by Google Gemini, here to help you make the most of the M-link platform.

I can help you with:
• 📦 Making delivery requests
• 🔧 Finding technician services  
• 🛍️ Buy-for-me assistance
• ❓ Platform guidance & support

What would you like to know about today?
""";
  }
}

class GoogleAIException implements Exception {
  final String message;
  GoogleAIException(this.message);
  
  @override
  String toString() => 'GoogleAIException: $message';
}