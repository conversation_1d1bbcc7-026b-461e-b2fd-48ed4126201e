import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/screens/auth/login_screen.dart';
import 'package:mlink/screens/customer/delivery_request_screen.dart';
import 'package:mlink/screens/customer/buy_for_me_screen.dart';
import 'package:mlink/screens/customer/technician_request_screen.dart';
import 'package:mlink/widgets/app_logo.dart';
import 'package:mlink/widgets/profile_components.dart';
import 'package:mlink/widgets/request_components.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/services/request_service.dart';
import 'package:mlink/screens/chat/chat_screen.dart';
import 'package:mlink/screens/ai_assistant/ai_assistant_screen.dart';
import 'package:mlink/utils/supabase_error_handler.dart';

class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  bool _isNavVisible = true;
  late AnimationController _navAnimationController;
  late Animation<Offset> _navSlideAnimation;

  final List<Widget> _screens = [
    const CustomerDashboard(),
    const CustomerRequestsScreen(),
    const ChatScreen(),
    const CustomerProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _navAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _navSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, 1),
    ).animate(CurvedAnimation(
      parent: _navAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _navAnimationController.dispose();
    super.dispose();
  }

  void _toggleNavigation(bool visible) {
    if (_isNavVisible != visible) {
      setState(() {
        _isNavVisible = visible;
      });
      if (visible) {
        _navAnimationController.reverse();
      } else {
        _navAnimationController.forward();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is UserScrollNotification) {
            if (scrollNotification.direction == ScrollDirection.forward) {
              _toggleNavigation(true);
            } else if (scrollNotification.direction == ScrollDirection.reverse) {
              _toggleNavigation(false);
            }
          }
          return false;
        },
        child: _screens[_selectedIndex],
      ),
      floatingActionButton: _selectedIndex == 0 && _isNavVisible
        ? FloatingActionButton.extended(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AIAssistantScreen(),
                ),
              );
            },
            icon: const Icon(Icons.assistant),
            label: const Text('AI Assistant'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            elevation: 4,
            heroTag: "ai_assistant_fab",
          )
        : null,
      bottomNavigationBar: SlideTransition(
        position: _navSlideAnimation,
        child: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() => _selectedIndex = index);
              _toggleNavigation(true); // Show nav when tapped
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            selectedFontSize: 12,
            unselectedFontSize: 10,
            backgroundColor: Theme.of(context).colorScheme.surface,
            elevation: 0,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined),
                activeIcon: Icon(Icons.home),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list_alt_outlined),
                activeIcon: Icon(Icons.list_alt),
                label: 'Requests',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.chat_bubble_outline),
                activeIcon: Icon(Icons.chat_bubble),
                label: 'Chat',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_outline),
                activeIcon: Icon(Icons.person),
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomerDashboard extends StatefulWidget {
  const CustomerDashboard({super.key});

  @override
  State<CustomerDashboard> createState() => _CustomerDashboardState();
}

class _CustomerDashboardState extends State<CustomerDashboard> {
  List<BaseRequest> _recentRequests = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecentActivity();
  }

  Future<void> _loadRecentActivity() async {
    try {
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final requests = await RequestService.instance.getUserRequests(user.id);
        setState(() {
          _recentRequests = requests.take(3).toList(); // Show only last 3 requests
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      user?.fullName?.substring(0, 1).toUpperCase() ?? 'U',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Hello, ${user?.fullName?.split(' ').first ?? 'User'}!',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'What can we help you with today?',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.notifications_outlined,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    onPressed: () {
                      // TODO: Implement notifications
                    },
                  ),
                  const SizedBox(width: 8),
                  AppLogo(
                    width: AppLogoSizes.small,
                    height: AppLogoSizes.small,
                  ),
                ],
              ),
              const SizedBox(height: 32),
              
              // Quick Actions
              Text(
                'Quick Actions',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Service Cards
              Column(
                children: [
                  ServiceCard(
                    icon: Icons.motorcycle,
                    title: 'Delivery Service',
                    description: 'Send packages across the city',
                    color: Theme.of(context).colorScheme.primary,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const DeliveryRequestScreen(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  ServiceCard(
                    icon: Icons.shopping_cart,
                    title: 'Buy For Me',
                    description: 'Get groceries and essentials delivered',
                    color: Theme.of(context).colorScheme.secondary,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const BuyForMeScreen(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  ServiceCard(
                    icon: Icons.build,
                    title: 'Technician Service',
                    description: 'Professional repair and maintenance',
                    color: Theme.of(context).colorScheme.tertiary,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const TechnicianRequestScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 32),
              
              // Recent Activity
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Activity Cards
              _isLoading 
                ? const Center(child: CircularProgressIndicator())
                : _recentRequests.isEmpty
                    ? Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'No recent activity',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your recent orders and requests will appear here',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: _recentRequests.map((request) => Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _getServiceColor(request.type).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  _getServiceIcon(request.type),
                                  color: _getServiceColor(request.type),
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _getServiceTitle(request.type),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _getStatusText(request.status),
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: _getStatusColor(request.status),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Text(
                                _formatDate(request.createdAt),
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        )).toList(),
                      ),
              const SizedBox(height: 32),
              
              // Features Banner
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.secondary,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Why Choose M-Link?',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(Icons.flash_on, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Fast and reliable service',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.security, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Verified agents and technicians',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.support_agent, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          '24/7 customer support',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getServiceIcon(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Icons.motorcycle;
      case RequestType.buyForMe:
        return Icons.shopping_cart;
      case RequestType.technicianService:
        return Icons.build;
    }
  }

  String _getServiceTitle(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return 'Delivery Service';
      case RequestType.buyForMe:
        return 'Buy For Me';
      case RequestType.technicianService:
        return 'Technician Service';
    }
  }

  Color _getServiceColor(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Colors.blue;
      case RequestType.buyForMe:
        return Colors.green;
      case RequestType.technicianService:
        return Colors.orange;
    }
  }

  String _getStatusText(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return 'Pending';
      case RequestStatus.accepted:
        return 'Accepted';
      case RequestStatus.inProgress:
        return 'In Progress';
      case RequestStatus.completed:
        return 'Completed';
      case RequestStatus.cancelled:
        return 'Cancelled';
      case RequestStatus.failed:
        return 'Failed';
      case RequestStatus.paymentPending:
        return 'Payment Pending';
    }
  }

  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.accepted:
        return Colors.blue;
      case RequestStatus.inProgress:
        return Colors.blue;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.failed:
        return Colors.red;
      case RequestStatus.paymentPending:
        return Colors.orange;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final diff = now.difference(date);
    
    if (diff.inDays > 0) {
      return '${diff.inDays}d ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}h ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class ServiceCard extends StatefulWidget {
  final IconData icon;
  final String title;
  final String description;
  final Color color;
  final VoidCallback onTap;

  const ServiceCard({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
    required this.onTap,
  });

  @override
  State<ServiceCard> createState() => _ServiceCardState();
}

class _ServiceCardState extends State<ServiceCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: _isPressed ? 8 : 2,
            shadowColor: widget.color.withValues(alpha: 0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: _isPressed 
                    ? widget.color.withValues(alpha: 0.5) 
                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: _isPressed ? 2 : 1,
              ),
            ),
            child: InkWell(
              onTap: widget.onTap,
              onTapDown: (_) {
                setState(() => _isPressed = true);
                _animationController.forward();
              },
              onTapUp: (_) {
                setState(() => _isPressed = false);
                _animationController.reverse();
              },
              onTapCancel: () {
                setState(() => _isPressed = false);
                _animationController.reverse();
              },
              borderRadius: BorderRadius.circular(16),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: _isPressed 
                      ? LinearGradient(
                          colors: [
                            widget.color.withValues(alpha: 0.05),
                            widget.color.withValues(alpha: 0.02),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                ),
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: widget.color.withValues(alpha: _isPressed ? 0.2 : 0.1),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: _isPressed 
                            ? [
                                BoxShadow(
                                  color: widget.color.withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Icon(
                        widget.icon,
                        color: widget.color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _isPressed 
                                  ? widget.color 
                                  : Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.description,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: _isPressed 
                          ? widget.color 
                          : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class CustomerRequestsScreen extends StatefulWidget {
  const CustomerRequestsScreen({super.key});

  @override
  State<CustomerRequestsScreen> createState() => _CustomerRequestsScreenState();
}

class _CustomerRequestsScreenState extends State<CustomerRequestsScreen> with SingleTickerProviderStateMixin {
  List<BaseRequest> _allRequests = [];
  List<BaseRequest> _filteredRequests = [];
  RequestStatus? _selectedStatus;
  bool _isLoading = true;
  String? _error;
  Map<RequestStatus, int> _statusCounts = {};
  bool _isSideNavVisible = true;
  late AnimationController _sideNavController;
  late Animation<double> _sideNavAnimation;

  @override
  void initState() {
    super.initState();
    _sideNavController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _sideNavAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _sideNavController,
      curve: Curves.easeInOut,
    ));
    _loadUserRequests();
  }

  @override
  void dispose() {
    _sideNavController.dispose();
    super.dispose();
  }

  Future<void> _loadUserRequests() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });
      
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final requests = await RequestService.instance.getUserRequests(user.id);
        setState(() {
          _allRequests = requests;
          _filteredRequests = requests;
          _calculateStatusCounts();
          _isLoading = false;
        });
      }
    } catch (e) {
      SupabaseErrorHandler.logError('CustomerRequestsScreen._loadUserRequests', e);
      setState(() {
        _error = SupabaseErrorHandler.getErrorMessage(e);
        _isLoading = false;
      });
      
      // Show error snackbar if context is available
      if (mounted) {
        SupabaseErrorHandler.showErrorSnackBar(context, e);
      }
    }
  }

  void _calculateStatusCounts() {
    _statusCounts = {};
    for (final status in RequestStatus.values) {
      _statusCounts[status] = _allRequests.where((r) => r.status == status).length;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('My Requests'),
        leading: IconButton(
          icon: Icon(_isSideNavVisible ? Icons.menu_open : Icons.menu),
          onPressed: () {
            setState(() {
              _isSideNavVisible = !_isSideNavVisible;
            });
            if (_isSideNavVisible) {
              _sideNavController.reverse();
            } else {
              _sideNavController.forward();
            }
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUserRequests,
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _error != null
              ? _buildErrorState()
              : Stack(
                  children: [
                    // Main Content
                    Padding(
                      padding: EdgeInsets.only(
                        left: _isSideNavVisible ? 120 : 0,
                      ),
                      child: NotificationListener<ScrollNotification>(
                        onNotification: (scrollNotification) {
                          if (scrollNotification is UserScrollNotification) {
                            if (scrollNotification.direction == ScrollDirection.reverse && _isSideNavVisible) {
                              setState(() {
                                _isSideNavVisible = false;
                              });
                              _sideNavController.forward();
                            }
                          }
                          return false;
                        },
                        child: _filteredRequests.isEmpty
                            ? _buildEmptyState()
                            : ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: _filteredRequests.length,
                                itemBuilder: (context, index) {
                                  final request = _filteredRequests[index];
                                  return RequestCard(
                                    request: request,
                                    onTap: () => _showRequestDetails(request),
                                  );
                                },
                              ),
                      ),
                    ),
                    
                    // Side Navigation with slide animation
                    AnimatedBuilder(
                      animation: _sideNavAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(-120 * _sideNavAnimation.value, 0),
                          child: _buildSideNavigation(),
                        );
                      },
                    ),
                  ],
                ),
    );
  }

  void _filterRequests(RequestStatus? status) {
    setState(() {
      _selectedStatus = status;
      
      if (status == null) {
        _filteredRequests = _allRequests;
      } else {
        _filteredRequests = _allRequests.where((request) => request.status == status).toList();
      }
    });
  }

  Widget _buildSideNavigation() {
    return Container(
      width: 120,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 16),
          // All requests
          _buildNavItem(
            label: 'All',
            count: _allRequests.length,
            icon: Icons.all_inbox,
            isSelected: _selectedStatus == null,
            onTap: () => _filterRequests(null),
          ),
          const SizedBox(height: 8),
          // Pending
          _buildNavItem(
            label: 'Pending',
            count: _statusCounts[RequestStatus.pending] ?? 0,
            icon: Icons.schedule,
            color: Colors.orange,
            isSelected: _selectedStatus == RequestStatus.pending,
            onTap: () => _filterRequests(RequestStatus.pending),
          ),
          const SizedBox(height: 8),
          // Accepted
          _buildNavItem(
            label: 'Accepted',
            count: _statusCounts[RequestStatus.accepted] ?? 0,
            icon: Icons.check_circle_outline,
            color: Colors.blue,
            isSelected: _selectedStatus == RequestStatus.accepted,
            onTap: () => _filterRequests(RequestStatus.accepted),
          ),
          const SizedBox(height: 8),
          // In Progress
          _buildNavItem(
            label: 'In Progress',
            count: _statusCounts[RequestStatus.inProgress] ?? 0,
            icon: Icons.hourglass_empty,
            color: Colors.blue,
            isSelected: _selectedStatus == RequestStatus.inProgress,
            onTap: () => _filterRequests(RequestStatus.inProgress),
          ),
          const SizedBox(height: 8),
          // Completed
          _buildNavItem(
            label: 'Done',
            count: _statusCounts[RequestStatus.completed] ?? 0,
            icon: Icons.check_circle,
            color: Colors.green,
            isSelected: _selectedStatus == RequestStatus.completed,
            onTap: () => _filterRequests(RequestStatus.completed),
          ),
          const SizedBox(height: 8),
          // Cancelled
          _buildNavItem(
            label: 'Cancelled',
            count: _statusCounts[RequestStatus.cancelled] ?? 0,
            icon: Icons.cancel,
            color: Colors.red,
            isSelected: _selectedStatus == RequestStatus.cancelled,
            onTap: () => _filterRequests(RequestStatus.cancelled),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String label,
    required int count,
    required IconData icon,
    Color? color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3))
              : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : color ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;
    String subtitle;
    
    if (_selectedStatus == null) {
      message = 'Not posted yet';
      subtitle = 'Your requests will appear here once you create them from the home screen';
      icon = Icons.inbox_outlined;
    } else {
      switch (_selectedStatus!) {
        case RequestStatus.pending:
          message = 'No pending requests';
          subtitle = 'Requests waiting for acceptance will appear here';
          icon = Icons.schedule;
          break;
        case RequestStatus.accepted:
          message = 'No accepted requests';
          subtitle = 'Requests that have been accepted will appear here';
          icon = Icons.check_circle_outline;
          break;
        case RequestStatus.inProgress:
          message = 'No requests in progress';
          subtitle = 'Active requests will appear here';
          icon = Icons.hourglass_empty;
          break;
        case RequestStatus.completed:
          message = 'No completed requests';
          subtitle = 'Successfully completed requests will appear here';
          icon = Icons.check_circle;
          break;
        case RequestStatus.cancelled:
          message = 'No cancelled requests';
          subtitle = 'Cancelled requests will appear here';
          icon = Icons.cancel;
          break;
        case RequestStatus.failed:
          message = 'No failed requests';
          subtitle = 'Failed requests will appear here';
          icon = Icons.error_outline;
          break;
        case RequestStatus.paymentPending:
          message = 'No payment pending requests';
          subtitle = 'Requests with pending payments will appear here';
          icon = Icons.payment;
          break;
      }
    }
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedStatus == null) ...[
              const SizedBox(height: 24),
              FilledButton.icon(
                onPressed: () {
                  // Navigate back to home tab to create request
                  if (mounted) {
                    final homeScreen = context.findAncestorStateOfType<_CustomerHomeScreenState>();
                    homeScreen?.setState(() {
                      homeScreen._selectedIndex = 0;
                    });
                  }
                },
                icon: const Icon(Icons.add),
                label: const Text('Create Request'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Row(
      children: [
        // Side Navigation Placeholder
        Container(
          width: 120,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            children: [
              const SizedBox(height: 16),
              // Loading placeholders for nav items
              for (int i = 0; i < 6; i++) ...[
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  height: 60,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Main Content Loading
        const Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading requests...'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState() {
    return Row(
      children: [
        // Side Navigation Placeholder
        Container(
          width: 120,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            children: [
              const SizedBox(height: 16),
              // Error placeholders for nav items
              for (int i = 0; i < 6; i++) ...[
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  height: 60,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Main Error Content
        Expanded(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading requests',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _error ?? 'Unknown error occurred',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  FilledButton.icon(
                    onPressed: _loadUserRequests,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Try Again'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showRequestDetails(BaseRequest request) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => RequestDetailsSheet(request: request),
    );
  }
}

class RequestDetailsSheet extends StatelessWidget {
  final BaseRequest request;
  
  const RequestDetailsSheet({super.key, required this.request});
  
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getServiceColor(request.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getServiceIcon(request.type),
                      color: _getServiceColor(request.type),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getServiceTitle(request.type),
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '#${request.id.toUpperCase()}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Scrollable Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      _buildDetailSection(
                        context,
                        'Description',
                        request.description,
                        Icons.description_outlined,
                      ),
                      
                      // Locations
                      _buildDetailSection(
                        context,
                        'Pickup Location',
                        request.pickupLocation.address ?? 'Not specified',
                        Icons.location_on_outlined,
                      ),
                      
                      if (request.dropoffLocation != null)
                        _buildDetailSection(
                          context,
                          'Dropoff Location',
                          request.dropoffLocation!.address ?? 'Not specified',
                          Icons.location_off_outlined,
                        ),
                      
                      // Payment Info
                      _buildDetailSection(
                        context,
                        'Payment',
                        'UGX ${request.payment.amount.toStringAsFixed(0)} (${request.payment.method.name.replaceAll('_', ' ').toUpperCase()})',
                        Icons.payment_outlined,
                      ),
                      
                      // Status
                      _buildDetailSection(
                        context,
                        'Status',
                        _getStatusText(request.status),
                        Icons.info_outline,
                      ),
                      
                      // Dates
                      _buildDetailSection(
                        context,
                        'Created',
                        _formatFullDate(request.createdAt),
                        Icons.schedule_outlined,
                      ),
                      
                      // Notes
                      if (request.notes != null)
                        _buildDetailSection(
                          context,
                          'Notes',
                          request.notes!,
                          Icons.note_outlined,
                        ),
                      
                      const SizedBox(height: 24),
                      
                      // Action Buttons
                      if (request.status == RequestStatus.pending)
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              // TODO: Implement cancel request
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.error,
                              foregroundColor: Theme.of(context).colorScheme.onError,
                            ),
                            child: const Text('Cancel Request'),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildDetailSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  IconData _getServiceIcon(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Icons.motorcycle;
      case RequestType.buyForMe:
        return Icons.shopping_cart;
      case RequestType.technicianService:
        return Icons.build;
    }
  }
  
  String _getServiceTitle(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return 'Delivery Service';
      case RequestType.buyForMe:
        return 'Buy For Me';
      case RequestType.technicianService:
        return 'Technician Service';
    }
  }
  
  Color _getServiceColor(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Colors.blue;
      case RequestType.buyForMe:
        return Colors.green;
      case RequestType.technicianService:
        return Colors.orange;
    }
  }
  
  String _getStatusText(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return 'Pending';
      case RequestStatus.accepted:
        return 'Accepted';
      case RequestStatus.inProgress:
        return 'In Progress';
      case RequestStatus.completed:
        return 'Completed';
      case RequestStatus.cancelled:
        return 'Cancelled';
      case RequestStatus.failed:
        return 'Failed';
      case RequestStatus.paymentPending:
        return 'Payment Pending';
    }
  }
  
  String _formatFullDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}

class CustomerProfileScreen extends StatelessWidget {
  const CustomerProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // TODO: Navigate to edit profile screen
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Profile Header Card
            ProfileHeaderCard(user: user),
            const SizedBox(height: 24),
            
            // Account Information Section
            ProfileSection(
              title: 'Account Information',
              icon: Icons.person_outline,
              children: [
                ProfileListItem(
                  icon: Icons.email_outlined,
                  title: 'Email',
                  subtitle: user?.email ?? 'Not provided',
                ),
                ProfileListItem(
                  icon: Icons.phone_outlined,
                  title: 'Phone Number',
                  subtitle: user?.phoneNumber ?? 'Not provided',
                  onTap: () {
                    // TODO: Edit phone number
                  },
                ),
                ProfileListItem(
                  icon: Icons.calendar_today_outlined,
                  title: 'Member Since',
                  subtitle: user?.createdAt != null 
                      ? '${user!.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}'
                      : 'Unknown',
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Settings Section
            ProfileSection(
              title: 'Settings',
              icon: Icons.settings_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Manage your notifications',
                  onTap: () {
                    // TODO: Navigate to notifications settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.security_outlined,
                  title: 'Privacy & Security',
                  subtitle: 'Manage your privacy settings',
                  onTap: () {
                    // TODO: Navigate to privacy settings
                  },
                ),
                ProfileListItem(
                  icon: Icons.language_outlined,
                  title: 'Language',
                  subtitle: 'English',
                  onTap: () {
                    // TODO: Navigate to language settings
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Support Section
            ProfileSection(
              title: 'Support',
              icon: Icons.support_agent_outlined,
              children: [
                ProfileListItem(
                  icon: Icons.help_outline,
                  title: 'Help Center',
                  subtitle: 'Get help and support',
                  onTap: () {
                    // TODO: Navigate to help center
                  },
                ),
                ProfileListItem(
                  icon: Icons.feedback_outlined,
                  title: 'Send Feedback',
                  subtitle: 'Help us improve the app',
                  onTap: () {
                    // TODO: Navigate to feedback form
                  },
                ),
                ProfileListItem(
                  icon: Icons.info_outline,
                  title: 'About',
                  subtitle: 'Learn more about M-Link',
                  onTap: () {
                    // TODO: Navigate to about screen
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),
            
            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await _showLogoutDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.logout, color: Theme.of(context).colorScheme.onError),
                    const SizedBox(width: 8),
                    Text(
                      'Logout',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onError,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await AuthService.instance.logout();
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );
    }
  }
}