import 'package:flutter/material.dart';
import 'package:mlink/models/admin/app_config.dart';
import 'package:mlink/services/admin/admin_service.dart';

class SettingsManager extends StatefulWidget {
  const SettingsManager({super.key});

  @override
  State<SettingsManager> createState() => _SettingsManagerState();
}

class _SettingsManagerState extends State<SettingsManager> {
  Map<String, List<AppConfig>> configsByCategory = {};
  Map<String, TextEditingController> controllers = {};
  Set<String> modifiedConfigs = {};
  bool isLoading = true;
  bool isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadConfigs();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (final controller in controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadConfigs() async {
    try {
      final data = await AdminService.instance.getConfigsByCategory();
      setState(() {
        configsByCategory = data;
        isLoading = false;
      });
      
      // Initialize controllers
      _initializeControllers();
    } catch (e) {
      print('Error loading configs: $e');
      setState(() => isLoading = false);
    }
  }

  void _initializeControllers() {
    controllers.clear();
    configsByCategory.forEach((category, configs) {
      for (final config in configs) {
        controllers[config.id] = TextEditingController(text: config.value ?? '');
        // Listen for changes
        controllers[config.id]!.addListener(() {
          if (controllers[config.id]!.text != (config.value ?? '')) {
            setState(() => modifiedConfigs.add(config.id));
          } else {
            setState(() => modifiedConfigs.remove(config.id));
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Global Settings',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          if (modifiedConfigs.isNotEmpty) ...[
            OutlinedButton.icon(
              onPressed: _discardChanges,
              icon: const Icon(Icons.close),
              label: const Text('Discard'),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: isSaving ? null : _saveChanges,
              icon: isSaving 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              label: Text(isSaving ? 'Saving...' : 'Save Changes'),
            ),
          ],
          const SizedBox(width: 16),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : configsByCategory.isEmpty
              ? _buildEmptyState()
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Modified configs indicator
                      if (modifiedConfigs.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.edit, color: Colors.orange, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                '${modifiedConfigs.length} setting${modifiedConfigs.length != 1 ? 's' : ''} modified',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.orange.shade800,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const Spacer(),
                              TextButton(
                                onPressed: _saveChanges,
                                child: const Text('Save All'),
                              ),
                            ],
                          ),
                        ),
                      
                      // Config categories
                      ...configsByCategory.entries.map((entry) =>
                          _buildCategorySection(entry.key, entry.value)),
                    ],
                  ),
                ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No Settings Found',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _loadConfigs,
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(String category, List<AppConfig> configs) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getCategoryIcon(category),
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  _getCategoryTitle(category),
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const Spacer(),
                Chip(
                  label: Text('${configs.length} settings'),
                  backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
                  side: BorderSide.none,
                ),
              ],
            ),
          ),
          
          // Config Items
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: configs.map((config) => _buildConfigItem(config)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigItem(AppConfig config) {
    final theme = Theme.of(context);
    final isModified = modifiedConfigs.contains(config.id);
    final controller = controllers[config.id]!;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isModified 
            ? Colors.orange.withValues(alpha: 0.05)
            : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isModified 
              ? Colors.orange.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          config.key,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isModified) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'MODIFIED',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                        if (config.isSensitive) ...[
                          const SizedBox(width: 8),
                          Icon(
                            Icons.security,
                            size: 16,
                            color: Colors.red,
                          ),
                        ],
                      ],
                    ),
                    if (config.description?.isNotEmpty == true) ...[
                      const SizedBox(height: 4),
                      Text(
                        config.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Chip(
                label: Text(config.valueType.name.toUpperCase()),
                backgroundColor: _getTypeColor(config.valueType).withValues(alpha: 0.1),
                side: BorderSide.none,
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Value input based on type
          _buildValueInput(config, controller, theme),
        ],
      ),
    );
  }

  Widget _buildValueInput(AppConfig config, TextEditingController controller, ThemeData theme) {
    switch (config.valueType) {
      case ConfigValueType.boolean:
        return _buildBooleanInput(config, controller, theme);
      case ConfigValueType.number:
        return _buildNumberInput(config, controller, theme);
      case ConfigValueType.json:
        return _buildJsonInput(config, controller, theme);
      case ConfigValueType.string:
      default:
        return _buildStringInput(config, controller, theme);
    }
  }

  Widget _buildStringInput(AppConfig config, TextEditingController controller, ThemeData theme) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: 'Enter ${config.key}',
        prefixIcon: Icon(_getConfigIcon(config.key)),
      ),
      obscureText: config.isSensitive,
      maxLines: config.key.contains('url') || config.key.contains('description') ? 2 : 1,
    );
  }

  Widget _buildNumberInput(AppConfig config, TextEditingController controller, ThemeData theme) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: 'Enter ${config.key}',
        prefixIcon: Icon(_getConfigIcon(config.key)),
        suffixText: _getNumberSuffix(config.key),
      ),
      keyboardType: TextInputType.number,
    );
  }

  Widget _buildBooleanInput(AppConfig config, TextEditingController controller, ThemeData theme) {
    final value = controller.text.toLowerCase() == 'true';
    
    return Row(
      children: [
        Switch(
          value: value,
          onChanged: (newValue) {
            controller.text = newValue.toString();
          },
        ),
        const SizedBox(width: 12),
        Text(
          value ? 'Enabled' : 'Disabled',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: value ? Colors.green : Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildJsonInput(AppConfig config, TextEditingController controller, ThemeData theme) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: 'Enter JSON for ${config.key}',
        prefixIcon: const Icon(Icons.code),
        helperText: 'Valid JSON format required',
      ),
      maxLines: 4,
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'pricing':
        return Icons.attach_money;
      case 'features':
        return Icons.toggle_on;
      case 'limits':
        return Icons.speed;
      case 'contact':
        return Icons.contact_support;
      case 'legal':
        return Icons.gavel;
      case 'general':
      default:
        return Icons.settings;
    }
  }

  String _getCategoryTitle(String category) {
    switch (category.toLowerCase()) {
      case 'pricing':
        return 'Pricing & Fees';
      case 'features':
        return 'Feature Toggles';
      case 'limits':
        return 'System Limits';
      case 'contact':
        return 'Contact Information';
      case 'legal':
        return 'Legal & Compliance';
      case 'general':
      default:
        return 'General Settings';
    }
  }

  Color _getTypeColor(ConfigValueType type) {
    switch (type) {
      case ConfigValueType.string:
        return Colors.blue;
      case ConfigValueType.number:
        return Colors.green;
      case ConfigValueType.boolean:
        return Colors.orange;
      case ConfigValueType.json:
        return Colors.purple;
    }
  }

  IconData _getConfigIcon(String key) {
    if (key.contains('fee') || key.contains('rate') || key.contains('cost') || key.contains('price')) {
      return Icons.attach_money;
    } else if (key.contains('phone')) {
      return Icons.phone;
    } else if (key.contains('email')) {
      return Icons.email;
    } else if (key.contains('url') || key.contains('link')) {
      return Icons.link;
    } else if (key.contains('enable') || key.contains('maintenance')) {
      return Icons.toggle_on;
    } else if (key.contains('max') || key.contains('min') || key.contains('limit')) {
      return Icons.speed;
    } else if (key.contains('distance') || key.contains('km')) {
      return Icons.straighten;
    } else if (key.contains('version')) {
      return Icons.info;
    }
    return Icons.settings;
  }

  String? _getNumberSuffix(String key) {
    if (key.contains('percentage') || key.contains('rate')) {
      return '%';
    } else if (key.contains('fee') || key.contains('cost') || key.contains('price')) {
      return 'TSh';
    } else if (key.contains('distance') || key.contains('km')) {
      return 'km';
    } else if (key.contains('hour')) {
      return 'hrs';
    }
    return null;
  }

  Future<void> _saveChanges() async {
    setState(() => isSaving = true);
    
    try {
      final List<Future> updateFutures = [];
      
      for (final configId in modifiedConfigs) {
        final newValue = controllers[configId]?.text;
        if (newValue != null) {
          // Find the config
          AppConfig? configToUpdate;
          for (final configs in configsByCategory.values) {
            final config = configs.firstWhere(
              (c) => c.id == configId,
              orElse: () => configs.first, // This will never be used due to the outer loop
            );
            if (config.id == configId) {
              configToUpdate = config;
              break;
            }
          }
          
          if (configToUpdate != null) {
            final updatedConfig = configToUpdate.copyWith(
              value: newValue,
              updatedAt: DateTime.now(),
            );
            updateFutures.add(AdminService.instance.updateConfig(updatedConfig));
          }
        }
      }
      
      await Future.wait(updateFutures);
      
      setState(() {
        modifiedConfigs.clear();
        isSaving = false;
      });
      
      // Reload configs to reflect changes
      await _loadConfigs();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Settings updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() => isSaving = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating settings: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _discardChanges() {
    setState(() {
      modifiedConfigs.clear();
      // Reset controllers to original values
      for (final category in configsByCategory.values) {
        for (final config in category) {
          controllers[config.id]?.text = config.value ?? '';
        }
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Changes discarded'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}