import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/services/maps_service.dart';

class TrackingScreen extends StatefulWidget {
  final BaseRequest request;
  final bool isAgent; // true if viewing as agent, false if viewing as customer

  const TrackingScreen({
    super.key,
    required this.request,
    this.isAgent = false,
  });

  @override
  State<TrackingScreen> createState() => _TrackingScreenState();
}

class _TrackingScreenState extends State<TrackingScreen> {
  GoogleMapController? _controller;
  final MapsService _mapsService = MapsService.instance;
  
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  
  LatLng? _agentLocation;
  StreamSubscription<Position>? _locationSubscription;
  
  Timer? _updateTimer;
  bool _isTracking = false;

  @override
  void initState() {
    super.initState();
    _initializeMap();
    if (widget.isAgent) {
      _startLocationTracking();
    }
  }

  void _initializeMap() async {
    await _mapsService.initialize();
    _setupMarkers();
    _setupRoute();
  }

  void _setupMarkers() {
    final markers = <Marker>{};
    
    // Pickup marker
    markers.add(_mapsService.createPickupMarker(widget.request.pickupLocation));
    
    // Dropoff marker (if exists)
    if (widget.request.dropoffLocation != null) {
      markers.add(_mapsService.createDropoffMarker(widget.request.dropoffLocation!));
    }
    
    // Agent location marker (if available)
    if (_agentLocation != null) {
      markers.add(_mapsService.createAgentMarker(
        agentId: 'current_agent',
        position: _agentLocation!,
        name: widget.isAgent ? 'Your Location' : 'Agent Location',
      ));
    }
    
    setState(() {
      _markers = markers;
    });
  }

  void _setupRoute() {
    final List<LatLng> routePoints = [];
    
    // Add pickup location
    routePoints.add(LatLng(
      widget.request.pickupLocation.latitude,
      widget.request.pickupLocation.longitude,
    ));
    
    // Add agent location if available
    if (_agentLocation != null) {
      routePoints.add(_agentLocation!);
    }
    
    // Add dropoff location if exists
    if (widget.request.dropoffLocation != null) {
      routePoints.add(LatLng(
        widget.request.dropoffLocation!.latitude,
        widget.request.dropoffLocation!.longitude,
      ));
    }
    
    if (routePoints.length >= 2) {
      final polyline = _mapsService.createRoutePolyline(
        polylineId: 'delivery_route',
        points: routePoints,
        color: Theme.of(context).colorScheme.primary,
        width: 4,
      );
      
      setState(() {
        _polylines = {polyline};
      });
    }
  }

  void _startLocationTracking() async {
    if (!widget.isAgent || _isTracking) return;
    
    setState(() {
      _isTracking = true;
    });
    
    _locationSubscription = _mapsService.locationStream.listen((position) {
      final newLocation = LatLng(position.latitude, position.longitude);
      
      setState(() {
        _agentLocation = newLocation;
      });
      
      _setupMarkers();
      _setupRoute();
      
      // Auto-center map on agent location
      if (_controller != null) {
        _mapsService.moveCamera(newLocation, zoom: 16);
      }
    });
    
    await _mapsService.startLocationTracking();
  }

  void _stopLocationTracking() {
    if (!_isTracking) return;
    
    _locationSubscription?.cancel();
    _mapsService.stopLocationTracking();
    
    setState(() {
      _isTracking = false;
    });
  }

  void _fitToShowAllLocations() {
    final List<LatLng> locations = [];
    
    locations.add(LatLng(
      widget.request.pickupLocation.latitude,
      widget.request.pickupLocation.longitude,
    ));
    
    if (widget.request.dropoffLocation != null) {
      locations.add(LatLng(
        widget.request.dropoffLocation!.latitude,
        widget.request.dropoffLocation!.longitude,
      ));
    }
    
    if (_agentLocation != null) {
      locations.add(_agentLocation!);
    }
    
    if (locations.isNotEmpty) {
      _mapsService.fitToLocations(locations);
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _controller = controller;
    _mapsService.setController(controller);
    
    // Fit to show all locations after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _fitToShowAllLocations();
    });
  }

  String _getStatusText() {
    switch (widget.request.status) {
      case RequestStatus.pending:
        return 'Waiting for agent assignment';
      case RequestStatus.accepted:
        return 'Agent assigned - heading to pickup';
      case RequestStatus.inProgress:
        return widget.request.dropoffLocation != null 
            ? 'On the way to destination'
            : 'Service in progress';
      case RequestStatus.completed:
        return 'Request completed';
      case RequestStatus.cancelled:
        return 'Request cancelled';
      case RequestStatus.failed:
        return 'Request failed';
      case RequestStatus.paymentPending:
        return 'Payment pending';
    }
  }

  Color _getStatusColor() {
    switch (widget.request.status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.accepted:
        return Colors.blue;
      case RequestStatus.inProgress:
        return Colors.green;
      case RequestStatus.completed:
        return Colors.green.shade700;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.failed:
        return Colors.red.shade700;
      case RequestStatus.paymentPending:
        return Colors.amber;
    }
  }

  @override
  void dispose() {
    _stopLocationTracking();
    _updateTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isAgent ? 'Request Tracking' : 'Track Your Request'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.center_focus_strong),
            onPressed: _fitToShowAllLocations,
            tooltip: 'Center on locations',
          ),
        ],
      ),
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: LatLng(
                widget.request.pickupLocation.latitude,
                widget.request.pickupLocation.longitude,
              ),
              zoom: 14,
            ),
            markers: _markers,
            polylines: _polylines,
            myLocationEnabled: widget.isAgent,
            myLocationButtonEnabled: false,
            mapType: MapType.normal,
            zoomControlsEnabled: false,
          ),
          
          // Status card
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getStatusColor(),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _getStatusText(),
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (widget.isAgent && _isTracking)
                          Icon(
                            Icons.gps_fixed,
                            color: Colors.green,
                            size: 16,
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.request.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Location tracking toggle (for agents)
          if (widget.isAgent)
            Positioned(
              bottom: 100,
              right: 16,
              child: FloatingActionButton(
                heroTag: 'tracking_toggle',
                onPressed: _isTracking ? _stopLocationTracking : _startLocationTracking,
                backgroundColor: _isTracking ? Colors.red : Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                child: Icon(_isTracking ? Icons.gps_off : Icons.gps_fixed),
              ),
            ),
          
          // Center on locations button
          Positioned(
            bottom: widget.isAgent ? 40 : 100,
            right: 16,
            child: FloatingActionButton(
              heroTag: 'center_locations',
              onPressed: _fitToShowAllLocations,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
              child: const Icon(Icons.center_focus_strong),
            ),
          ),
          
          // Request details card
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Card(
              margin: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor().withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: _getStatusColor().withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            widget.request.status.name.toUpperCase(),
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: _getStatusColor(),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'Payment: ${widget.request.payment.method.name}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Pickup location
                    _buildLocationRow(
                      icon: Icons.radio_button_unchecked,
                      iconColor: Colors.green,
                      title: 'Pickup',
                      address: widget.request.pickupLocation.address ?? 'Pickup location',
                    ),
                    
                    // Dropoff location (if exists)
                    if (widget.request.dropoffLocation != null) ...[
                      const SizedBox(height: 12),
                      _buildLocationRow(
                        icon: Icons.location_on,
                        iconColor: Colors.red,
                        title: 'Dropoff',
                        address: widget.request.dropoffLocation!.address ?? 'Dropoff location',
                      ),
                    ],
                    
                    if (widget.request.notes?.isNotEmpty == true) ...[
                      const SizedBox(height: 16),
                      Text(
                        'Notes:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.request.notes!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationRow({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String address,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
              Text(
                address,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}