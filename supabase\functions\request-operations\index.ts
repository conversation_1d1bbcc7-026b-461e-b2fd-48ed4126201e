import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RequestPayload {
  action: 'create' | 'update' | 'cancel' | 'complete' | 'assign'
  requestData?: any
  requestId?: string
  userId?: string
  assigneeId?: string
  status?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, requestData, requestId, userId, assigneeId, status } = await req.json() as RequestPayload

    switch (action) {
      case 'create':
        return await createRequest(supabase, requestData)
      case 'update':
        return await updateRequest(supabase, requestId!, requestData)
      case 'cancel':
        return await cancelRequest(supabase, requestId!, userId!)
      case 'complete':
        return await completeRequest(supabase, requestId!, userId!)
      case 'assign':
        return await assignRequest(supabase, requestId!, assigneeId!, userId!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Request operation error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function createRequest(supabase: any, requestData: any) {
  const { data: request, error: requestError } = await supabase
    .from('requests')
    .insert(requestData)
    .select()
    .single()

  if (requestError) throw requestError

  // Create notification for available agents/technicians
  await createNotificationForAvailableWorkers(supabase, request)

  return new Response(
    JSON.stringify({ success: true, request }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function updateRequest(supabase: any, requestId: string, requestData: any) {
  const { data: request, error } = await supabase
    .from('requests')
    .update({ ...requestData, updated_at: new Date().toISOString() })
    .eq('id', requestId)
    .select()
    .single()

  if (error) throw error

  // Log the update
  await supabase.from('request_updates').insert({
    request_id: requestId,
    new_status: requestData.status,
    updated_by: requestData.updated_by,
    created_at: new Date().toISOString(),
  })

  return new Response(
    JSON.stringify({ success: true, request }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function cancelRequest(supabase: any, requestId: string, userId: string) {
  const { data: request, error } = await supabase
    .from('requests')
    .update({ 
      status: 'cancelled', 
      updated_at: new Date().toISOString(),
      cancelled_by: userId,
      cancelled_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single()

  if (error) throw error

  // Notify assigned worker if any
  if (request.agent_id || request.technician_id) {
    await supabase.from('notifications').insert({
      user_id: request.agent_id || request.technician_id,
      title: 'Request Cancelled',
      message: `Request #${requestId} has been cancelled by the customer.`,
      type: 'request_cancelled',
      created_at: new Date().toISOString(),
    })
  }

  return new Response(
    JSON.stringify({ success: true, request }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function completeRequest(supabase: any, requestId: string, userId: string) {
  const { data: request, error } = await supabase
    .from('requests')
    .update({ 
      status: 'completed', 
      updated_at: new Date().toISOString(),
      completed_by: userId,
      completed_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single()

  if (error) throw error

  // Update payment status if needed
  if (request.payment_method === 'cash') {
    await supabase
      .from('requests')
      .update({ 
        payment_status: 'paid',
        paid_at: new Date().toISOString()
      })
      .eq('id', requestId)
  }

  // Notify customer
  await supabase.from('notifications').insert({
    user_id: request.customer_id,
    title: 'Request Completed',
    message: `Your request #${requestId} has been completed successfully.`,
    type: 'request_completed',
    created_at: new Date().toISOString(),
  })

  return new Response(
    JSON.stringify({ success: true, request }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function assignRequest(supabase: any, requestId: string, assigneeId: string, userId: string) {
  const { data: request, error } = await supabase
    .from('requests')
    .update({ 
      status: 'accepted',
      agent_id: assigneeId,
      technician_id: assigneeId,
      updated_at: new Date().toISOString(),
      accepted_by: userId,
      accepted_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single()

  if (error) throw error

  // Notify customer
  await supabase.from('notifications').insert({
    user_id: request.customer_id,
    title: 'Request Accepted',
    message: `Your request #${requestId} has been accepted and will be processed soon.`,
    type: 'request_accepted',
    created_at: new Date().toISOString(),
  })

  return new Response(
    JSON.stringify({ success: true, request }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function createNotificationForAvailableWorkers(supabase: any, request: any) {
  let workers = []
  
  if (request.type === 'delivery' || request.type === 'buy_for_me') {
    const { data: agents } = await supabase
      .from('agents')
      .select('user_id')
      .eq('is_available', true)
      .eq('is_verified', true)
    
    workers = agents?.map((agent: any) => agent.user_id) || []
  } else if (request.type === 'technician_service') {
    const { data: technicians } = await supabase
      .from('technicians')
      .select('user_id')
      .eq('is_available', true)
      .eq('is_verified', true)
    
    workers = technicians?.map((technician: any) => technician.user_id) || []
  }

  // Create notifications for available workers
  if (workers.length > 0) {
    const notifications = workers.map((workerId: string) => ({
      user_id: workerId,
      title: 'New Request Available',
      message: `A new ${request.type.replace('_', ' ')} request is available in your area.`,
      type: 'new_request',
      request_id: request.id,
      created_at: new Date().toISOString(),
    }))

    await supabase.from('notifications').insert(notifications)
  }
}