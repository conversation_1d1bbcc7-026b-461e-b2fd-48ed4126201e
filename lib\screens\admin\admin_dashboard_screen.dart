import 'package:flutter/material.dart';
import 'package:mlink/screens/admin/modules/dashboard_overview.dart';
import 'package:mlink/screens/admin/modules/post_manager.dart';
import 'package:mlink/screens/admin/modules/user_manager.dart';
import 'package:mlink/screens/admin/modules/request_manager.dart';
import 'package:mlink/screens/admin/modules/payments_manager.dart';
import 'package:mlink/screens/admin/modules/settings_manager.dart';
import 'package:mlink/screens/admin/modules/analytics_dashboard.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/services/auth_service.dart';

enum AdminTab {
  dashboard,
  posts,
  users,
  requests,
  payments,
  settings,
  analytics
}

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  AdminTab selectedTab = AdminTab.dashboard;
  bool isDrawerOpen = false;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
  }

  void _checkAdminAccess() {
    final user = AuthService.instance.currentUser;
    if (user?.role != UserRole.admin) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Sidebar Navigation
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: isDrawerOpen || MediaQuery.of(context).size.width > 768 ? 280 : 72,
            child: _buildSidebar(),
          ),
          // Main Content
          Expanded(
            child: _buildMainContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebar() {
    final theme = Theme.of(context);
    final isLargeScreen = MediaQuery.of(context).size.width > 768;
    final showLabels = isDrawerOpen || isLargeScreen;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.admin_panel_settings,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                ),
                if (showLabels) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Admin Panel',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'M-Link Dashboard',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Toggle Button (only on small screens)
          if (!isLargeScreen)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: IconButton(
                onPressed: () => setState(() => isDrawerOpen = !isDrawerOpen),
                icon: Icon(
                  isDrawerOpen ? Icons.menu_open : Icons.menu,
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildNavItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard Overview',
                  tab: AdminTab.dashboard,
                  showLabel: showLabels,
                ),
                _buildNavItem(
                  icon: Icons.campaign,
                  title: 'Post Manager',
                  tab: AdminTab.posts,
                  showLabel: showLabels,
                ),
                _buildNavItem(
                  icon: Icons.people,
                  title: 'User Manager',
                  tab: AdminTab.users,
                  showLabel: showLabels,
                ),
                _buildNavItem(
                  icon: Icons.assignment,
                  title: 'Request Manager',
                  tab: AdminTab.requests,
                  showLabel: showLabels,
                ),
                _buildNavItem(
                  icon: Icons.payment,
                  title: 'Payments & Subscriptions',
                  tab: AdminTab.payments,
                  showLabel: showLabels,
                ),
                _buildNavItem(
                  icon: Icons.settings,
                  title: 'Global Settings',
                  tab: AdminTab.settings,
                  showLabel: showLabels,
                ),
                _buildNavItem(
                  icon: Icons.analytics,
                  title: 'App Analytics',
                  tab: AdminTab.analytics,
                  showLabel: showLabels,
                ),
              ],
            ),
          ),
          
          // Footer
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                if (showLabels) const Divider(color: Colors.white30),
                Row(
                  children: [
                    CircleAvatar(
                      radius: 18,
                      backgroundColor: theme.colorScheme.surface,
                      child: Icon(
                        Icons.person,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    if (showLabels) ...[
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AuthService.instance.currentUser?.fullName ?? 'Admin',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              'Administrator',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () async {
                          await AuthService.instance.logout();
                          if (mounted) {
                            Navigator.of(context).pushReplacementNamed('/');
                          }
                        },
                        icon: Icon(
                          Icons.logout,
                          color: theme.colorScheme.onPrimary,
                          size: 20,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String title,
    required AdminTab tab,
    required bool showLabel,
  }) {
    final theme = Theme.of(context);
    final isSelected = selectedTab == tab;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected 
              ? theme.colorScheme.surface
              : theme.colorScheme.onPrimary.withValues(alpha: 0.8),
        ),
        title: showLabel ? Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected 
                ? theme.colorScheme.surface
                : theme.colorScheme.onPrimary.withValues(alpha: 0.8),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ) : null,
        selected: isSelected,
        selectedTileColor: theme.colorScheme.surface.withValues(alpha: 0.2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        onTap: () => setState(() => selectedTab = tab),
        contentPadding: showLabel 
            ? const EdgeInsets.symmetric(horizontal: 16.0)
            : const EdgeInsets.symmetric(horizontal: 24.0),
        minTileHeight: 48,
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: _getContentWidget(),
    );
  }

  Widget _getContentWidget() {
    switch (selectedTab) {
      case AdminTab.dashboard:
        return const DashboardOverview();
      case AdminTab.posts:
        return const PostManager();
      case AdminTab.users:
        return const UserManager();
      case AdminTab.requests:
        return const RequestManager();
      case AdminTab.payments:
        return const PaymentsManager();
      case AdminTab.settings:
        return const SettingsManager();
      case AdminTab.analytics:
        return const AnalyticsDashboard();
    }
  }
}