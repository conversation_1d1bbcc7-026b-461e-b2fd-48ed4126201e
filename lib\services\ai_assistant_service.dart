import 'dart:convert';
import 'dart:typed_data';
import 'package:mlink/openai/openai_config.dart';
import 'package:mlink/services/google_ai_service.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/services/auth_service.dart';

enum AIProvider { openai, googleai }

class AIAssistantService {
  static final AIAssistantService _instance = AIAssistantService._internal();
  factory AIAssistantService() => _instance;
  AIAssistantService._internal();

  static AIAssistantService get instance => _instance;
  
  // Current AI provider - can be changed by user
  AIProvider _currentProvider = AIProvider.openai;
  
  AIProvider get currentProvider => _currentProvider;
  
  void setProvider(AIProvider provider) {
    _currentProvider = provider;
  }
  
  String get currentProviderName {
    switch (_currentProvider) {
      case AIProvider.openai:
        return 'OpenAI GPT-4';
      case AIProvider.googleai:
        return 'Google Gemini';
    }
  }

  // System prompt for M-link AI assistant
  static const String _systemPrompt = """
You are M-link AI Assistant, a helpful AI assistant for the M-link platform - a unified digital platform connecting customers, delivery agents, and technicians in Tanzania.

Your role is to help users with:
- Understanding how to use the M-link platform
- Guidance on making delivery requests
- Information about technician services
- General support and troubleshooting
- Connecting with appropriate services

You should be friendly, professional, and knowledgeable about the M-link platform. When users ask about specific services, guide them to the appropriate sections of the app.

Always respond in a helpful and conversational manner. If you don't know something specific about the platform, acknowledge it and suggest they contact customer support.

Remember to:
- Be concise but informative
- Use simple language
- Provide actionable guidance
- Be encouraging and supportive
- Suggest using specific features when relevant
""";

  Future<String> generateResponse({
    required String userMessage,
    List<Map<String, dynamic>>? conversationHistory,
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _generateOpenAIResponse(
          userMessage: userMessage,
          conversationHistory: conversationHistory,
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.generateResponse(
          userMessage: userMessage,
          conversationHistory: conversationHistory,
          currentUser: currentUser,
        );
    }
  }

  Future<String> _generateOpenAIResponse({
    required String userMessage,
    List<Map<String, dynamic>>? conversationHistory,
    User? currentUser,
  }) async {
    try {
      final messages = <Map<String, dynamic>>[];
      
      // Add system prompt
      messages.add({
        'role': 'system',
        'content': _systemPrompt,
      });

      // Add conversation history if provided
      if (conversationHistory != null && conversationHistory.isNotEmpty) {
        messages.addAll(conversationHistory);
      }

      // Add current user message
      messages.add({
        'role': 'user',
        'content': userMessage,
      });

      final response = await OpenAIConfig.generateChatCompletion(
        messages: messages,
        model: 'gpt-4o',
        temperature: 0.7,
        maxTokens: 1000,
      );

      return OpenAIConfig.extractMessageContent(response);
    } catch (e) {
      final userFriendlyError = OpenAIConfig.getUserFriendlyError(e);
      throw AIAssistantException(userFriendlyError);
    }
  }

  Future<String> generateHelpResponse({
    required String helpTopic,
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _generateOpenAIHelpResponse(
          helpTopic: helpTopic,
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.generateHelpResponse(
          helpTopic: helpTopic,
          currentUser: currentUser,
        );
    }
  }

  Future<String> _generateOpenAIHelpResponse({
    required String helpTopic,
    User? currentUser,
  }) async {
    try {
      final prompt = """
The user needs help with: $helpTopic

Provide a helpful, step-by-step guide specific to the M-link platform. Include:
1. Clear instructions
2. What features to use
3. Any important tips or warnings
4. Next steps they should take

Keep the response concise but comprehensive.
""";

      final response = await OpenAIConfig.generateChatCompletion(
        messages: [
          {'role': 'system', 'content': _systemPrompt},
          {'role': 'user', 'content': prompt},
        ],
        model: 'gpt-4o-mini',
        temperature: 0.3,
        maxTokens: 800,
      );

      return OpenAIConfig.extractMessageContent(response);
    } catch (e) {
      final userFriendlyError = OpenAIConfig.getUserFriendlyError(e);
      throw AIAssistantException(userFriendlyError);
    }
  }

  Future<String> generateServiceRecommendation({
    required String userNeed,
    required String location,
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _generateOpenAIServiceRecommendation(
          userNeed: userNeed,
          location: location,
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.generateServiceRecommendation(
          userNeed: userNeed,
          location: location,
          currentUser: currentUser,
        );
    }
  }

  Future<String> _generateOpenAIServiceRecommendation({
    required String userNeed,
    required String location,
    User? currentUser,
  }) async {
    try {
      final prompt = """
The user needs: $userNeed
Location: $location

Based on this, recommend the most appropriate M-link service:
- Delivery service for package/food delivery
- Technician service for repairs, maintenance, or technical work
- Buy-for-me service for shopping assistance

Provide:
1. Which service is most suitable
2. Why this service fits their need
3. How to access it in the app
4. What information they'll need to provide
5. Estimated timeframe and cost considerations

Be specific and actionable.
""";

      final response = await OpenAIConfig.generateChatCompletion(
        messages: [
          {'role': 'system', 'content': _systemPrompt},
          {'role': 'user', 'content': prompt},
        ],
        model: 'gpt-4o',
        temperature: 0.5,
        maxTokens: 1000,
      );

      return OpenAIConfig.extractMessageContent(response);
    } catch (e) {
      final userFriendlyError = OpenAIConfig.getUserFriendlyError(e);
      throw AIAssistantException(userFriendlyError);
    }
  }

  Future<String> generateTroubleshootingHelp({
    required String issue,
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _generateOpenAITroubleshootingHelp(
          issue: issue,
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.generateTroubleshootingHelp(
          issue: issue,
          currentUser: currentUser,
        );
    }
  }

  Future<String> _generateOpenAITroubleshootingHelp({
    required String issue,
    User? currentUser,
  }) async {
    try {
      final prompt = """
The user is experiencing this issue: $issue

Provide troubleshooting steps for the M-link platform:
1. Common causes of this issue
2. Step-by-step solutions to try
3. When to contact customer support
4. How to prevent this issue in the future

Be practical and easy to follow.
""";

      final response = await OpenAIConfig.generateChatCompletion(
        messages: [
          {'role': 'system', 'content': _systemPrompt},
          {'role': 'user', 'content': prompt},
        ],
        model: 'o3-mini',
        temperature: 0.3,
        maxTokens: 1000,
      );

      return OpenAIConfig.extractMessageContent(response);
    } catch (e) {
      final userFriendlyError = OpenAIConfig.getUserFriendlyError(e);
      throw AIAssistantException(userFriendlyError);
    }
  }

  Future<List<String>> generateQuickSuggestions({
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _generateOpenAIQuickSuggestions(
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.generateQuickSuggestions(
          currentUser: currentUser,
        );
    }
  }

  Future<List<String>> _generateOpenAIQuickSuggestions({
    User? currentUser,
  }) async {
    try {
      final prompt = """
Generate 4 quick suggestion prompts that users might want to ask the M-link AI assistant.
These should be common questions or requests related to:
- Using the platform
- Making requests
- Understanding services
- Getting help

Return only the suggestions, one per line, without numbers or bullets.
Each suggestion should be a complete question or request.
""";

      final response = await OpenAIConfig.generateChatCompletion(
        messages: [
          {'role': 'system', 'content': _systemPrompt},
          {'role': 'user', 'content': prompt},
        ],
        model: 'gpt-4o-mini',
        temperature: 0.8,
        maxTokens: 200,
      );

      final content = OpenAIConfig.extractMessageContent(response);
      return content.split('\n').where((line) => line.trim().isNotEmpty).toList();
    } catch (e) {
      // Return fallback suggestions if AI fails
      return [
        'How do I make a delivery request?',
        'What technician services are available?',
        'How do I track my request?',
        'How do I contact customer support?',
      ];
    }
  }

  Future<String> generateContextualResponse({
    required String userMessage,
    required String userContext,
    List<Map<String, dynamic>>? conversationHistory,
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _generateOpenAIContextualResponse(
          userMessage: userMessage,
          userContext: userContext,
          conversationHistory: conversationHistory,
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.generateContextualResponse(
          userMessage: userMessage,
          userContext: userContext,
          conversationHistory: conversationHistory,
          currentUser: currentUser,
        );
    }
  }

  Future<String> _generateOpenAIContextualResponse({
    required String userMessage,
    required String userContext,
    List<Map<String, dynamic>>? conversationHistory,
    User? currentUser,
  }) async {
    try {
      final contextPrompt = """
User context: $userContext

User message: $userMessage

Provide a response that takes into account the user's current context and situation.
""";

      final messages = <Map<String, dynamic>>[];
      
      // Add system prompt
      messages.add({
        'role': 'system',
        'content': _systemPrompt,
      });

      // Add conversation history if provided
      if (conversationHistory != null && conversationHistory.isNotEmpty) {
        messages.addAll(conversationHistory);
      }

      // Add contextual prompt
      messages.add({
        'role': 'user',
        'content': contextPrompt,
      });

      final response = await OpenAIConfig.generateChatCompletion(
        messages: messages,
        model: 'gpt-4o',
        temperature: 0.7,
        maxTokens: 1000,
      );

      return OpenAIConfig.extractMessageContent(response);
    } catch (e) {
      final userFriendlyError = OpenAIConfig.getUserFriendlyError(e);
      throw AIAssistantException(userFriendlyError);
    }
  }

  Future<Map<String, dynamic>> analyzeMessageSentiment({
    required String message,
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        return _analyzeOpenAIMessageSentiment(
          message: message,
          currentUser: currentUser,
        );
      case AIProvider.googleai:
        return GoogleAIService.instance.analyzeMessageSentiment(
          message: message,
          currentUser: currentUser,
        );
    }
  }

  Future<Map<String, dynamic>> _analyzeOpenAIMessageSentiment({
    required String message,
    User? currentUser,
  }) async {
    try {
      final prompt = """
Analyze the sentiment and content of this message for moderation purposes:

Message: "$message"

Provide a JSON response with:
{
  "sentiment": "positive|negative|neutral",
  "confidence": 0.0-1.0,
  "toxicity": 0.0-1.0,
  "categories": ["category1", "category2"],
  "needs_moderation": true|false,
  "reason": "explanation if needs moderation"
}

Categories can include: harassment, hate, violence, self-harm, sexual, spam, inappropriate
""";

      final response = await OpenAIConfig.generateStructuredResponse(
        messages: [
          {'role': 'user', 'content': prompt},
        ],
        jsonSchema: {
          'type': 'object',
          'properties': {
            'sentiment': {'type': 'string', 'enum': ['positive', 'negative', 'neutral']},
            'confidence': {'type': 'number', 'minimum': 0, 'maximum': 1},
            'toxicity': {'type': 'number', 'minimum': 0, 'maximum': 1},
            'categories': {'type': 'array', 'items': {'type': 'string'}},
            'needs_moderation': {'type': 'boolean'},
            'reason': {'type': 'string'}
          },
          'required': ['sentiment', 'confidence', 'toxicity', 'categories', 'needs_moderation']
        },
        model: 'gpt-4o-mini',
        temperature: 0.3,
        maxTokens: 300,
      );

      final content = OpenAIConfig.extractMessageContent(response);
      return Map<String, dynamic>.from(json.decode(content));
    } catch (e) {
      // Return safe defaults if analysis fails
      return {
        'sentiment': 'neutral',
        'confidence': 0.5,
        'toxicity': 0.0,
        'categories': [],
        'needs_moderation': false,
        'reason': ''
      };
    }
  }

  String getWelcomeMessage({User? currentUser}) {
    switch (_currentProvider) {
      case AIProvider.openai:
        final userName = currentUser?.fullName?.isNotEmpty == true 
            ? currentUser!.fullName!.split(' ').first 
            : 'there';
        return """
Hello $userName! 👋

I'm your M-link AI Assistant powered by OpenAI GPT-4, here to help you make the most of the M-link platform.

I can help you with:
• 📦 Making delivery requests
• 🔧 Finding technician services  
• 🛍️ Buy-for-me assistance
• ❓ Platform guidance & support

What would you like to know about today?
""";
      case AIProvider.googleai:
        return GoogleAIService.instance.getWelcomeMessage(currentUser: currentUser);
    }
  }

  Future<String> generateResponseWithImage({
    required String prompt,
    required Uint8List imageBytes,
    String mimeType = 'image/jpeg',
    User? currentUser,
  }) async {
    switch (_currentProvider) {
      case AIProvider.openai:
        // OpenAI image processing (if supported)
        throw AIAssistantException('Image processing with OpenAI not implemented in this version');
      case AIProvider.googleai:
        return GoogleAIService.instance.generateResponseWithImage(
          prompt: prompt,
          imageBytes: imageBytes,
          mimeType: mimeType,
          currentUser: currentUser,
        );
    }
  }

  List<AIProvider> getAvailableProviders() {
    return AIProvider.values;
  }

  String getProviderDisplayName(AIProvider provider) {
    switch (provider) {
      case AIProvider.openai:
        return 'OpenAI GPT-4';
      case AIProvider.googleai:
        return 'Google Gemini';
    }
  }
}

class AIAssistantException implements Exception {
  final String message;
  AIAssistantException(this.message);
  
  @override
  String toString() => 'AIAssistantException: $message';
}