import 'package:mlink/models/request.dart';
import 'package:mlink/models/technician.dart';

class SampleRequests {
  static List<BaseRequest> getSampleRequests() {
    final now = DateTime.now();
    
    return [
      // Recent Delivery Request
      DeliveryRequest(
        id: 'del_001',
        customerId: 'cust_001',
        description: 'Urgent document delivery to downtown office',
        pickupLocation: LocationPoint(
          latitude: 0.3476,
          longitude: 32.5825,
          address: 'Kampala Central Business District',
          landmark: 'Near Garden City Mall',
        ),
        dropoffLocation: LocationPoint(
          latitude: 0.3136,
          longitude: 32.5811,
          address: 'Nakasero Hill',
          landmark: 'Workers House Building',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 15000,
          isPaid: true,
          paidAt: now.subtract(const Duration(hours: 2)),
        ),
        createdAt: now.subtract(const Duration(hours: 3)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        status: RequestStatus.completed,
        priority: DeliveryPriority.urgent,
        agentId: 'agent_001',
        customerPhone: '+256700123456',
        recipientName: '<PERSON>',
        recipientPhone: '+256700987654',
        requiresSignature: true,
        packageType: 'Documents',
      ),
      
      // Active Buy For Me Request
      BuyForMeRequest(
        id: 'bfm_001',
        customerId: 'cust_001',
        description: 'Weekly grocery shopping from Tuskys Supermarket',
        pickupLocation: LocationPoint(
          latitude: 0.3476,
          longitude: 32.5825,
          address: 'Tuskys Supermarket, Kampala Road',
          landmark: 'Near Post Office',
        ),
        dropoffLocation: LocationPoint(
          latitude: 0.3200,
          longitude: 32.5900,
          address: 'Bugolobi, Kampala',
          landmark: 'Opposite Shell Station',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 85000,
          advance: 50000,
          isPaid: false,
        ),
        createdAt: now.subtract(const Duration(hours: 1)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
        status: RequestStatus.inProgress,
        agentId: 'agent_002',
        storeLocation: 'Tuskys Supermarket, Kampala Road',
        items: [
          'Rice - 5kg',
          'Cooking oil - 2L',
          'Sugar - 2kg',
          'Bread - 2 loaves',
          'Milk - 1L',
          'Eggs - 30 pieces',
          'Tomatoes - 2kg',
          'Onions - 1kg',
        ],
        estimatedCost: 75000,
        maxBudget: 90000,
        allowSubstitutes: true,
        customerPhone: '+256700123456',
      ),
      
      // Pending Technician Request
      TechnicianRequest(
        id: 'tech_001',
        customerId: 'cust_001',
        description: 'Air conditioning unit repair and maintenance',
        pickupLocation: LocationPoint(
          latitude: 0.3200,
          longitude: 32.5900,
          address: 'Bugolobi, Kampala',
          landmark: 'Opposite Shell Station',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 150000,
          isPaid: false,
        ),
        createdAt: now.subtract(const Duration(minutes: 45)),
        updatedAt: now.subtract(const Duration(minutes: 45)),
        status: RequestStatus.pending,
        category: TechnicianCategory.electrician,
        skillRequired: 'Air conditioning repair',
        estimatedHours: 3,
        isUrgent: true,
        customerPhone: '+256700123456',
        notes: 'AC not cooling properly, might need gas refill',
      ),
      
      // Cancelled Delivery Request
      DeliveryRequest(
        id: 'del_002',
        customerId: 'cust_001',
        description: 'Food delivery from KFC to office',
        pickupLocation: LocationPoint(
          latitude: 0.3476,
          longitude: 32.5825,
          address: 'KFC Garden City',
          landmark: 'Garden City Mall',
        ),
        dropoffLocation: LocationPoint(
          latitude: 0.3136,
          longitude: 32.5811,
          address: 'Nakasero Hill',
          landmark: 'Workers House Building',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 25000,
          isPaid: false,
        ),
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        status: RequestStatus.cancelled,
        priority: DeliveryPriority.normal,
        customerPhone: '+256700123456',
        packageType: 'Food',
      ),
      
      // Completed Buy For Me Request
      BuyForMeRequest(
        id: 'bfm_002',
        customerId: 'cust_001',
        description: 'Medicine purchase from Mukwano Pharmacy',
        pickupLocation: LocationPoint(
          latitude: 0.3476,
          longitude: 32.5825,
          address: 'Mukwano Pharmacy, Kampala Road',
          landmark: 'Near Clock Tower',
        ),
        dropoffLocation: LocationPoint(
          latitude: 0.3200,
          longitude: 32.5900,
          address: 'Bugolobi, Kampala',
          landmark: 'Opposite Shell Station',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 45000,
          isPaid: true,
          paidAt: now.subtract(const Duration(days: 2)),
        ),
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 2)),
        status: RequestStatus.completed,
        agentId: 'agent_003',
        storeLocation: 'Mukwano Pharmacy, Kampala Road',
        items: [
          'Panadol - 2 boxes',
          'Vitamin C - 1 bottle',
          'Cough syrup - 1 bottle',
        ],
        estimatedCost: 42000,
        maxBudget: 50000,
        allowSubstitutes: false,
        customerPhone: '+256700123456',
      ),
      
      // Completed Technician Request
      TechnicianRequest(
        id: 'tech_002',
        customerId: 'cust_001',
        description: 'Plumbing - Fix kitchen sink and bathroom faucet',
        pickupLocation: LocationPoint(
          latitude: 0.3200,
          longitude: 32.5900,
          address: 'Bugolobi, Kampala',
          landmark: 'Opposite Shell Station',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 80000,
          isPaid: true,
          paidAt: now.subtract(const Duration(days: 5)),
        ),
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(days: 5)),
        status: RequestStatus.completed,
        category: TechnicianCategory.plumber,
        skillRequired: 'General plumbing',
        estimatedHours: 2,
        isUrgent: false,
        customerPhone: '+256700123456',
        technicianId: 'tech_001',
        notes: 'Both issues resolved successfully',
      ),
    ];
  }
}