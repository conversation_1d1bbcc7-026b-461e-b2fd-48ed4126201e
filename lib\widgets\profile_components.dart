import 'package:flutter/material.dart';

import 'package:mlink/models/user.dart' as app_user;

// Profile Components
class ProfileHeaderCard extends StatelessWidget {
  final app_user.User? user;
  final Map<String, dynamic>? stats;
  
  const ProfileHeaderCard({
    super.key, 
    required this.user,
    this.stats,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _getGradientColors(context, user?.role),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Picture
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white,
            backgroundImage: user?.avatarUrl != null ? NetworkImage(user!.avatarUrl!) : null,
            child: user?.avatarUrl == null
                ? Text(
                    user?.fullName?.substring(0, 1).toUpperCase() ?? 'U',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: _getPrimaryRoleColor(user?.role),
                    ),
                  )
                : null,
          ),
          const SizedBox(height: 16),
          
          // Name
          Text(
            user?.fullName ?? 'User',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          
          // Role Badge with Icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(_getRoleIcon(user?.role), color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                _getRoleDisplayName(user?.role),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Email
          Text(
            user?.email ?? '',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          
          // Stats row if provided
          if (stats != null && stats!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: _buildStatsRow(),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildStatsRow() {
    final items = <Widget>[];
    
    if (stats!['average_rating'] != null) {
      items.addAll([
        Icon(Icons.star, color: Colors.white, size: 20),
        const SizedBox(width: 4),
        Text(
          '${(stats!['average_rating'] as num).toStringAsFixed(1)} Rating',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ]);
    }
    
    if (stats!['total_requests'] != null || stats!['total_jobs'] != null) {
      if (items.isNotEmpty) {
        items.addAll([
          const SizedBox(width: 8),
          Text('•', style: TextStyle(color: Colors.white)),
          const SizedBox(width: 8),
        ]);
      }
      
      final count = stats!['total_requests'] ?? stats!['total_jobs'] ?? 0;
      final label = user?.role == app_user.UserRole.customer ? 'Requests' : 'Jobs';
      items.add(
        Text(
          '$count $label',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: items,
    );
  }
  
  List<Color> _getGradientColors(BuildContext context, app_user.UserRole? role) {
    switch (role) {
      case app_user.UserRole.agent:
        return [
          Theme.of(context).colorScheme.primary,
          Theme.of(context).colorScheme.tertiary,
        ];
      case app_user.UserRole.technician:
        return [Colors.orange, Colors.deepOrange];
      case app_user.UserRole.admin:
        return [Colors.purple, Colors.deepPurple];
      default:
        return [
          Theme.of(context).colorScheme.primary,
          Theme.of(context).colorScheme.secondary,
        ];
    }
  }
  
  Color _getPrimaryRoleColor(app_user.UserRole? role) {
    switch (role) {
      case app_user.UserRole.agent:
        return Colors.blue;
      case app_user.UserRole.technician:
        return Colors.orange;
      case app_user.UserRole.admin:
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }
  
  IconData _getRoleIcon(app_user.UserRole? role) {
    switch (role) {
      case app_user.UserRole.customer:
        return Icons.verified_user;
      case app_user.UserRole.agent:
        return Icons.verified;
      case app_user.UserRole.technician:
        return Icons.verified;
      case app_user.UserRole.admin:
        return Icons.admin_panel_settings;
      default:
        return Icons.person;
    }
  }
  
  String _getRoleDisplayName(app_user.UserRole? role) {
    switch (role) {
      case app_user.UserRole.customer:
        return 'M-Link Customer';
      case app_user.UserRole.agent:
        return 'Verified Agent';
      case app_user.UserRole.technician:
        return 'Certified Technician';
      case app_user.UserRole.admin:
        return 'Administrator';
      default:
        return 'User';
    }
  }
}

class ProfileSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  
  const ProfileSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
  });
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        
        // Section Content
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: children.map((child) {
              final isLast = children.indexOf(child) == children.length - 1;
              return Column(
                children: [
                  child,
                  if (!isLast)
                    Divider(
                      height: 1,
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                    ),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

class ProfileListItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final Widget? trailing;
  
  const ProfileListItem({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.onTap,
    this.trailing,
  });
  
  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: trailing ?? 
          (onTap != null
              ? Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                )
              : null),
      onTap: onTap,
    );
  }
}