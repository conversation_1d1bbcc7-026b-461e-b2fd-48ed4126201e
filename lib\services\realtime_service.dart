import 'package:mlink/supabase/supabase_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/models/user.dart';

class RealtimeService {
  static final RealtimeService _instance = RealtimeService._internal();
  factory RealtimeService() => _instance;
  RealtimeService._internal();

  static RealtimeService get instance => _instance;

  final Map<String, RealtimeChannel> _activeChannels = {};

  // Subscribe to request updates for a specific user
  RealtimeChannel subscribeToUserRequests(String userId, Function(Map<String, dynamic>) onUpdate) {
    final channelName = 'user_requests_$userId';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'requests',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'customer_id',
            value: userId,
          ),
          callback: (payload) {
            onUpdate(payload.newRecord);
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to available requests of a specific type
  RealtimeChannel subscribeToAvailableRequests(RequestType requestType, Function(Map<String, dynamic>) onUpdate) {
    final channelName = 'available_requests_${requestType.name}';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'requests',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'type',
            value: requestType.name,
          ),
          callback: (payload) {
            final record = payload.newRecord;
            // Only notify about pending requests
            if (record['status'] == 'pending') {
              onUpdate(record);
            }
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to notifications for a specific user
  RealtimeChannel subscribeToUserNotifications(String userId, Function(Map<String, dynamic>) onNotification) {
    final channelName = 'user_notifications_$userId';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'notifications',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) {
            onNotification(payload.newRecord);
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to agent location updates
  RealtimeChannel subscribeToAgentLocations(Function(Map<String, dynamic>) onLocationUpdate) {
    final channelName = 'agent_locations';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'agents',
          callback: (payload) {
            final record = payload.newRecord;
            if (record['current_location_lat'] != null && record['current_location_lng'] != null) {
              onLocationUpdate(record);
            }
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to requests assigned to a specific agent
  RealtimeChannel subscribeToAgentRequests(String agentId, Function(Map<String, dynamic>) onUpdate) {
    final channelName = 'agent_requests_$agentId';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'delivery_requests',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'agent_id',
            value: agentId,
          ),
          callback: (payload) {
            onUpdate(payload.newRecord);
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to requests assigned to a specific technician
  RealtimeChannel subscribeToTechnicianRequests(String technicianId, Function(Map<String, dynamic>) onUpdate) {
    final channelName = 'technician_requests_$technicianId';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'technician_requests',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'technician_id',
            value: technicianId,
          ),
          callback: (payload) {
            onUpdate(payload.newRecord);
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to reviews for a specific user
  RealtimeChannel subscribeToUserReviews(String userId, Function(Map<String, dynamic>) onReview) {
    final channelName = 'user_reviews_$userId';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'reviews',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'reviewee_id',
            value: userId,
          ),
          callback: (payload) {
            onReview(payload.newRecord);
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to request status updates
  RealtimeChannel subscribeToRequestStatus(String requestId, Function(Map<String, dynamic>) onStatusUpdate) {
    final channelName = 'request_status_$requestId';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'requests',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'id',
            value: requestId,
          ),
          callback: (payload) {
            onStatusUpdate(payload.newRecord);
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to agent availability changes
  RealtimeChannel subscribeToAgentAvailability(Function(Map<String, dynamic>) onAvailabilityChange) {
    final channelName = 'agent_availability';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'agents',
          callback: (payload) {
            final record = payload.newRecord;
            if (record['is_available'] != null) {
              onAvailabilityChange(record);
            }
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Subscribe to technician availability changes
  RealtimeChannel subscribeToTechnicianAvailability(Function(Map<String, dynamic>) onAvailabilityChange) {
    final channelName = 'technician_availability';
    
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
    }

    final channel = SupabaseConfig.client.channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'technicians',
          callback: (payload) {
            final record = payload.newRecord;
            if (record['is_available'] != null) {
              onAvailabilityChange(record);
            }
          },
        )
        .subscribe();

    _activeChannels[channelName] = channel;
    return channel;
  }

  // Unsubscribe from a specific channel
  void unsubscribe(String channelName) {
    if (_activeChannels.containsKey(channelName)) {
      _activeChannels[channelName]!.unsubscribe();
      _activeChannels.remove(channelName);
    }
  }

  // Unsubscribe from all channels
  void unsubscribeAll() {
    for (final channel in _activeChannels.values) {
      channel.unsubscribe();
    }
    _activeChannels.clear();
  }

  // Get active channel count
  int get activeChannelCount => _activeChannels.length;

  // Get active channel names
  List<String> get activeChannelNames => _activeChannels.keys.toList();

  // Check if a channel is active
  bool isChannelActive(String channelName) => _activeChannels.containsKey(channelName);

  // Cleanup method to be called when the service is no longer needed
  void dispose() {
    unsubscribeAll();
  }
}