import 'package:mlink/models/agent.dart';
import 'package:mlink/models/technician.dart';

enum RequestType { delivery, buyForMe, technicianService }

enum RequestStatus {
  pending,
  accepted,
  inProgress,
  completed,
  cancelled,
  failed,
  paymentPending
}

enum PaymentMethod { cash, mobileMoney, card, wallet }

enum DeliveryPriority { normal, urgent, scheduled }

class LocationPoint {
  final double latitude;
  final double longitude;
  final String? address;
  final String? landmark;

  LocationPoint({
    required this.latitude,
    required this.longitude,
    this.address,
    this.landmark,
  });

  factory LocationPoint.fromJson(Map<String, dynamic> json) {
    return LocationPoint(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'],
      landmark: json['landmark'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'landmark': landmark,
    };
  }
}

class PaymentInfo {
  final PaymentMethod method;
  final double amount;
  final double? advance;
  final double? tip;
  final bool isPaid;
  final DateTime? paidAt;
  final String? transactionId;

  PaymentInfo({
    required this.method,
    required this.amount,
    this.advance,
    this.tip,
    this.isPaid = false,
    this.paidAt,
    this.transactionId,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      method: PaymentMethod.values.firstWhere(
        (e) => e.name == json['method'],
        orElse: () => PaymentMethod.cash,
      ),
      amount: (json['amount'] as num).toDouble(),
      advance: (json['advance'] as num?)?.toDouble(),
      tip: (json['tip'] as num?)?.toDouble(),
      isPaid: json['is_paid'] ?? false,
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
      transactionId: json['transaction_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method.name,
      'amount': amount,
      'advance': advance,
      'tip': tip,
      'is_paid': isPaid,
      'paid_at': paidAt?.toIso8601String(),
      'transaction_id': transactionId,
    };
  }
}

class BaseRequest {
  final String id;
  final String customerId;
  final RequestType type;
  final RequestStatus status;
  final String description;
  final LocationPoint pickupLocation;
  final LocationPoint? dropoffLocation;
  final PaymentInfo payment;
  final DateTime? scheduledAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> imageUrls;
  final String? notes;
  final String? customerPhone;

  BaseRequest({
    required this.id,
    required this.customerId,
    required this.type,
    this.status = RequestStatus.pending,
    required this.description,
    required this.pickupLocation,
    this.dropoffLocation,
    required this.payment,
    this.scheduledAt,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrls = const [],
    this.notes,
    this.customerPhone,
  });

  factory BaseRequest.fromJson(Map<String, dynamic> json) {
    return BaseRequest(
      id: json['id'],
      customerId: json['customer_id'],
      type: RequestType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RequestType.delivery,
      ),
      status: RequestStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => RequestStatus.pending,
      ),
      description: json['description'],
      pickupLocation: LocationPoint.fromJson(json['pickup_location']),
      dropoffLocation: json['dropoff_location'] != null
          ? LocationPoint.fromJson(json['dropoff_location'])
          : null,
      payment: PaymentInfo.fromJson(json['payment']),
      scheduledAt: json['scheduled_at'] != null
          ? DateTime.parse(json['scheduled_at'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      imageUrls: (json['image_urls'] as List<dynamic>?)
          ?.map((url) => url.toString())
          .toList() ?? [],
      notes: json['notes'],
      customerPhone: json['customer_phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'type': type.name,
      'status': status.name,
      'description': description,
      'pickup_location': pickupLocation.toJson(),
      'dropoff_location': dropoffLocation?.toJson(),
      'payment': payment.toJson(),
      'scheduled_at': scheduledAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'image_urls': imageUrls,
      'notes': notes,
      'customer_phone': customerPhone,
    };
  }
}

class DeliveryRequest extends BaseRequest {
  final String? agentId;
  final VehicleType preferredVehicle;
  final DeliveryPriority priority;
  final double? estimatedWeight;
  final String? packageType;
  final bool requiresSignature;
  final String? recipientName;
  final String? recipientPhone;

  DeliveryRequest({
    required super.id,
    required super.customerId,
    super.type = RequestType.delivery,
    super.status = RequestStatus.pending,
    required super.description,
    required super.pickupLocation,
    super.dropoffLocation,
    required super.payment,
    super.scheduledAt,
    required super.createdAt,
    required super.updatedAt,
    super.imageUrls = const [],
    super.notes,
    super.customerPhone,
    this.agentId,
    this.preferredVehicle = VehicleType.bodaboda,
    this.priority = DeliveryPriority.normal,
    this.estimatedWeight,
    this.packageType,
    this.requiresSignature = false,
    this.recipientName,
    this.recipientPhone,
  });

  factory DeliveryRequest.fromJson(Map<String, dynamic> json) {
    final base = BaseRequest.fromJson(json);
    return DeliveryRequest(
      id: base.id,
      customerId: base.customerId,
      type: base.type,
      status: base.status,
      description: base.description,
      pickupLocation: base.pickupLocation,
      dropoffLocation: base.dropoffLocation,
      payment: base.payment,
      scheduledAt: base.scheduledAt,
      createdAt: base.createdAt,
      updatedAt: base.updatedAt,
      imageUrls: base.imageUrls,
      notes: base.notes,
      customerPhone: base.customerPhone,
      agentId: json['agent_id'],
      preferredVehicle: VehicleType.values.firstWhere(
        (e) => e.name == json['preferred_vehicle'],
        orElse: () => VehicleType.bodaboda,
      ),
      priority: DeliveryPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => DeliveryPriority.normal,
      ),
      estimatedWeight: (json['estimated_weight'] as num?)?.toDouble(),
      packageType: json['package_type'],
      requiresSignature: json['requires_signature'] ?? false,
      recipientName: json['recipient_name'],
      recipientPhone: json['recipient_phone'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'agent_id': agentId,
      'preferred_vehicle': preferredVehicle.name,
      'priority': priority.name,
      'estimated_weight': estimatedWeight,
      'package_type': packageType,
      'requires_signature': requiresSignature,
      'recipient_name': recipientName,
      'recipient_phone': recipientPhone,
    });
    return json;
  }
}

class BuyForMeRequest extends BaseRequest {
  final String? agentId;
  final String storeLocation;
  final List<String> items;
  final double? estimatedCost;
  final double? maxBudget;
  final String? preferredBrands;
  final bool allowSubstitutes;

  BuyForMeRequest({
    required super.id,
    required super.customerId,
    super.type = RequestType.buyForMe,
    super.status = RequestStatus.pending,
    required super.description,
    required super.pickupLocation,
    super.dropoffLocation,
    required super.payment,
    super.scheduledAt,
    required super.createdAt,
    required super.updatedAt,
    super.imageUrls = const [],
    super.notes,
    super.customerPhone,
    this.agentId,
    required this.storeLocation,
    this.items = const [],
    this.estimatedCost,
    this.maxBudget,
    this.preferredBrands,
    this.allowSubstitutes = true,
  });

  factory BuyForMeRequest.fromJson(Map<String, dynamic> json) {
    final base = BaseRequest.fromJson(json);
    return BuyForMeRequest(
      id: base.id,
      customerId: base.customerId,
      type: base.type,
      status: base.status,
      description: base.description,
      pickupLocation: base.pickupLocation,
      dropoffLocation: base.dropoffLocation,
      payment: base.payment,
      scheduledAt: base.scheduledAt,
      createdAt: base.createdAt,
      updatedAt: base.updatedAt,
      imageUrls: base.imageUrls,
      notes: base.notes,
      customerPhone: base.customerPhone,
      agentId: json['agent_id'],
      storeLocation: json['store_location'],
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => item.toString())
          .toList() ?? [],
      estimatedCost: (json['estimated_cost'] as num?)?.toDouble(),
      maxBudget: (json['max_budget'] as num?)?.toDouble(),
      preferredBrands: json['preferred_brands'],
      allowSubstitutes: json['allow_substitutes'] ?? true,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'agent_id': agentId,
      'store_location': storeLocation,
      'items': items,
      'estimated_cost': estimatedCost,
      'max_budget': maxBudget,
      'preferred_brands': preferredBrands,
      'allow_substitutes': allowSubstitutes,
    });
    return json;
  }
}

class TechnicianRequest extends BaseRequest {
  final String? technicianId;
  final TechnicianCategory category;
  final String? skillRequired;
  final double? hourlyRate;
  final double? fixedRate;
  final int? estimatedHours;
  final bool isUrgent;
  final List<String> requiredTools;

  TechnicianRequest({
    required super.id,
    required super.customerId,
    super.type = RequestType.technicianService,
    super.status = RequestStatus.pending,
    required super.description,
    required super.pickupLocation,
    super.dropoffLocation,
    required super.payment,
    super.scheduledAt,
    required super.createdAt,
    required super.updatedAt,
    super.imageUrls = const [],
    super.notes,
    super.customerPhone,
    this.technicianId,
    required this.category,
    this.skillRequired,
    this.hourlyRate,
    this.fixedRate,
    this.estimatedHours,
    this.isUrgent = false,
    this.requiredTools = const [],
  });

  factory TechnicianRequest.fromJson(Map<String, dynamic> json) {
    final base = BaseRequest.fromJson(json);
    return TechnicianRequest(
      id: base.id,
      customerId: base.customerId,
      type: base.type,
      status: base.status,
      description: base.description,
      pickupLocation: base.pickupLocation,
      dropoffLocation: base.dropoffLocation,
      payment: base.payment,
      scheduledAt: base.scheduledAt,
      createdAt: base.createdAt,
      updatedAt: base.updatedAt,
      imageUrls: base.imageUrls,
      notes: base.notes,
      customerPhone: base.customerPhone,
      technicianId: json['technician_id'],
      category: TechnicianCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => TechnicianCategory.other,
      ),
      skillRequired: json['skill_required'],
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      fixedRate: (json['fixed_rate'] as num?)?.toDouble(),
      estimatedHours: json['estimated_hours'],
      isUrgent: json['is_urgent'] ?? false,
      requiredTools: (json['required_tools'] as List<dynamic>?)
          ?.map((tool) => tool.toString())
          .toList() ?? [],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'technician_id': technicianId,
      'category': category.name,
      'skill_required': skillRequired,
      'hourly_rate': hourlyRate,
      'fixed_rate': fixedRate,
      'estimated_hours': estimatedHours,
      'is_urgent': isUrgent,
      'required_tools': requiredTools,
    });
    return json;
  }
}

// Typedef for compatibility
typedef Request = BaseRequest;