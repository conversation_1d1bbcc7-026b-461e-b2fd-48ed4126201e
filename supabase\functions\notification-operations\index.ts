import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface NotificationPayload {
  action: 'get_notifications' | 'mark_read' | 'mark_all_read' | 'create_notification' | 'delete_notification'
  userId?: string
  notificationId?: string
  notificationData?: any
  limit?: number
  offset?: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, userId, notificationId, notificationData, limit, offset } = await req.json() as NotificationPayload

    switch (action) {
      case 'get_notifications':
        return await getUserNotifications(supabase, userId!, limit, offset)
      case 'mark_read':
        return await markNotificationAsRead(supabase, notificationId!)
      case 'mark_all_read':
        return await markAllNotificationsAsRead(supabase, userId!)
      case 'create_notification':
        return await createNotification(supabase, notificationData!)
      case 'delete_notification':
        return await deleteNotification(supabase, notificationId!, userId!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Notification operation error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function getUserNotifications(supabase: any, userId: string, limit = 20, offset = 0) {
  const { data: notifications, error } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) throw error

  // Get unread count
  const { count: unreadCount } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('is_read', false)

  return new Response(
    JSON.stringify({ 
      success: true, 
      notifications,
      unreadCount: unreadCount || 0
    }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function markNotificationAsRead(supabase: any, notificationId: string) {
  const { data: notification, error } = await supabase
    .from('notifications')
    .update({ 
      is_read: true, 
      read_at: new Date().toISOString() 
    })
    .eq('id', notificationId)
    .select()
    .single()

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, notification }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function markAllNotificationsAsRead(supabase: any, userId: string) {
  const { error } = await supabase
    .from('notifications')
    .update({ 
      is_read: true, 
      read_at: new Date().toISOString() 
    })
    .eq('user_id', userId)
    .eq('is_read', false)

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function createNotification(supabase: any, notificationData: any) {
  const { data: notification, error } = await supabase
    .from('notifications')
    .insert({
      ...notificationData,
      created_at: new Date().toISOString(),
      is_read: false,
    })
    .select()
    .single()

  if (error) throw error

  // If this is a push notification, send it via FCM or similar service
  if (notificationData.send_push) {
    await sendPushNotification(notification)
  }

  return new Response(
    JSON.stringify({ success: true, notification }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function deleteNotification(supabase: any, notificationId: string, userId: string) {
  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', notificationId)
    .eq('user_id', userId) // Ensure user can only delete their own notifications

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function sendPushNotification(notification: any) {
  // This is a placeholder for push notification logic
  // In a real implementation, you would integrate with FCM or similar service
  console.log('Sending push notification:', notification.title)
  
  // Example FCM integration would go here:
  // const fcmToken = await getUserFCMToken(notification.user_id)
  // await sendFCMNotification(fcmToken, notification)
}