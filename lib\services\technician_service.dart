import 'dart:math' as math;
import 'package:mlink/models/technician.dart';
import 'package:mlink/supabase/supabase_config.dart';

class TechnicianService {
  static final TechnicianService _instance = TechnicianService._internal();
  factory TechnicianService() => _instance;
  TechnicianService._internal();

  static TechnicianService get instance => _instance;

  Future<List<Technician>> getAvailableTechnicians({TechnicianCategory? category}) async {
    try {
      final response = await SupabaseConfig.getAvailableTechnicians(category?.name);
      return response.map((data) => Technician.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get available technicians: $e');
    }
  }

  Future<Technician?> getTechnicianProfile(String userId) async {
    try {
      final data = await SupabaseConfig.getTechnicianProfile(userId);
      if (data == null) return null;
      return Technician.fromJson(data);
    } catch (e) {
      throw Exception('Failed to get technician profile: $e');
    }
  }

  Future<void> createTechnician<PERSON><PERSON><PERSON><PERSON>(Technician technician) async {
    try {
      await SupabaseConfig.client.from('technicians').insert(technician.toJson());
    } catch (e) {
      throw Exception('Failed to create technician profile: $e');
    }
  }

  Future<void> updateTechnicianProfile(Technician technician) async {
    try {
      await SupabaseConfig.client
          .from('technicians')
          .update(technician.toJson())
          .eq('id', technician.id);
    } catch (e) {
      throw Exception('Failed to update technician profile: $e');
    }
  }

  Future<void> updateTechnicianAvailability(String technicianId, bool isAvailable) async {
    try {
      await SupabaseConfig.client
          .from('technicians')
          .update({
            'is_available': isAvailable,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', technicianId);
    } catch (e) {
      throw Exception('Failed to update technician availability: $e');
    }
  }

  Future<void> updateTechnicianStats(String technicianId, {int? totalJobs, double? totalEarnings, double? rating}) async {
    try {
      Map<String, dynamic> updateData = {'updated_at': DateTime.now().toIso8601String()};
      
      if (totalJobs != null) {
        updateData['total_jobs'] = totalJobs;
      }
      if (totalEarnings != null) {
        updateData['total_earnings'] = totalEarnings;
      }
      if (rating != null) {
        updateData['rating'] = rating;
      }

      await SupabaseConfig.client
          .from('technicians')
          .update(updateData)
          .eq('id', technicianId);
    } catch (e) {
      throw Exception('Failed to update technician stats: $e');
    }
  }

  Future<List<Technician>> getTechniciansByCategory(TechnicianCategory category) async {
    try {
      final response = await SupabaseConfig.client
          .from('technicians')
          .select('*, users!user_id(full_name, phone_number)')
          .eq('category', category.name)
          .eq('is_available', true)
          .eq('is_verified', true);
      
      return response.map((data) => Technician.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get technicians by category: $e');
    }
  }

  Future<List<Technician>> getTechniciansBySkill(String skill) async {
    try {
      final response = await SupabaseConfig.client
          .from('technicians')
          .select('*, users!user_id(full_name, phone_number)')
          .contains('skills', [skill])
          .eq('is_available', true)
          .eq('is_verified', true);
      
      return response.map((data) => Technician.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get technicians by skill: $e');
    }
  }

  Future<List<Technician>> getTechniciansByServiceArea(String serviceArea) async {
    try {
      final response = await SupabaseConfig.client
          .from('technicians')
          .select('*, users!user_id(full_name, phone_number)')
          .ilike('service_area', '%$serviceArea%')
          .eq('is_available', true)
          .eq('is_verified', true);
      
      return response.map((data) => Technician.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get technicians by service area: $e');
    }
  }

  Future<List<Technician>> getTechniciansByHourlyRate(double minRate, double maxRate) async {
    try {
      final response = await SupabaseConfig.client
          .from('technicians')
          .select('*, users!user_id(full_name, phone_number)')
          .gte('hourly_rate', minRate)
          .lte('hourly_rate', maxRate)
          .eq('is_available', true)
          .eq('is_verified', true);
      
      return response.map((data) => Technician.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get technicians by hourly rate: $e');
    }
  }

  Future<void> verifyTechnician(String technicianId, bool isVerified) async {
    try {
      await SupabaseConfig.client
          .from('technicians')
          .update({
            'is_verified': isVerified,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', technicianId);
    } catch (e) {
      throw Exception('Failed to verify technician: $e');
    }
  }

  Future<Map<String, dynamic>> getTechnicianStats(String technicianId) async {
    try {
      final technician = await SupabaseConfig.client
          .from('technicians')
          .select()
          .eq('id', technicianId)
          .single();
      
      // Get additional stats from requests
      final technicianRequests = await SupabaseConfig.client
          .from('technician_requests')
          .select('*, requests!inner(*)')
          .eq('technician_id', technicianId);
      
      final totalRequests = technicianRequests.length;
      final completedRequests = technicianRequests.where((r) => r['requests']['status'] == 'completed').length;
      final inProgressRequests = technicianRequests.where((r) => r['requests']['status'] == 'in_progress').length;
      
      return {
        'total_jobs': technician['total_jobs'] ?? 0,
        'total_earnings': technician['total_earnings'] ?? 0.0,
        'rating': technician['rating'] ?? 0.0,
        'total_requests': totalRequests,
        'completed_requests': completedRequests,
        'in_progress_requests': inProgressRequests,
        'success_rate': totalRequests > 0 ? (completedRequests / totalRequests * 100).round() : 0,
        'is_verified': technician['is_verified'] ?? false,
        'is_available': technician['is_available'] ?? false,
        'experience_years': technician['experience_years'] ?? 0,
        'hourly_rate': technician['hourly_rate'] ?? 0.0,
      };
    } catch (e) {
      throw Exception('Failed to get technician stats: $e');
    }
  }

  Future<List<Technician>> searchTechnicians(String query) async {
    try {
      final response = await SupabaseConfig.client
          .from('technicians')
          .select('*, users!user_id(full_name, phone_number)')
          .or('specialization.ilike.%$query%,service_area.ilike.%$query%')
          .eq('is_available', true)
          .eq('is_verified', true);
      
      return response.map((data) => Technician.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to search technicians: $e');
    }
  }

  Future<void> addTechnicianSkill(String technicianId, String skill) async {
    try {
      // Get current skills
      final technician = await SupabaseConfig.client
          .from('technicians')
          .select('skills')
          .eq('id', technicianId)
          .single();
      
      final currentSkills = List<String>.from(technician['skills'] ?? []);
      if (!currentSkills.contains(skill)) {
        currentSkills.add(skill);
        
        await SupabaseConfig.client
            .from('technicians')
            .update({
              'skills': currentSkills,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', technicianId);
      }
    } catch (e) {
      throw Exception('Failed to add technician skill: $e');
    }
  }

  Future<void> removeTechnicianSkill(String technicianId, String skill) async {
    try {
      // Get current skills
      final technician = await SupabaseConfig.client
          .from('technicians')
          .select('skills')
          .eq('id', technicianId)
          .single();
      
      final currentSkills = List<String>.from(technician['skills'] ?? []);
      currentSkills.remove(skill);
      
      await SupabaseConfig.client
          .from('technicians')
          .update({
            'skills': currentSkills,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', technicianId);
    } catch (e) {
      throw Exception('Failed to remove technician skill: $e');
    }
  }

  Future<void> deleteTechnicianProfile(String technicianId) async {
    try {
      await SupabaseConfig.client
          .from('technicians')
          .delete()
          .eq('id', technicianId);
    } catch (e) {
      throw Exception('Failed to delete technician profile: $e');
    }
  }

  Future<List<String>> getPopularSkills() async {
    try {
      final response = await SupabaseConfig.client
          .from('technicians')
          .select('skills')
          .eq('is_verified', true);
      
      final allSkills = <String>[];
      for (final tech in response) {
        final skills = List<String>.from(tech['skills'] ?? []);
        allSkills.addAll(skills);
      }
      
      // Count skill occurrences
      final skillCount = <String, int>{};
      for (final skill in allSkills) {
        skillCount[skill] = (skillCount[skill] ?? 0) + 1;
      }
      
      // Sort by popularity and return top 10
      final sortedSkills = skillCount.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      return sortedSkills.take(10).map((e) => e.key).toList();
    } catch (e) {
      throw Exception('Failed to get popular skills: $e');
    }
  }
}