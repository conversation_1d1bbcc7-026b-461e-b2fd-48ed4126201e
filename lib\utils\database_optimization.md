# Database Optimization Guide for M-Link

## Required Database Indexes

### Users Table
```sql
-- Primary queries: by id, by email, by role, by status
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, status);
```

### Agents Table
```sql
-- Primary queries: by availability, by verification, by rating
CREATE INDEX IF NOT EXISTS idx_agents_available_verified ON agents(is_available, is_verified);
CREATE INDEX IF NOT EXISTS idx_agents_rating ON agents(rating DESC);
CREATE INDEX IF NOT EXISTS idx_agents_location ON agents(current_location_lat, current_location_lng);
```

### Technicians Table  
```sql
-- Primary queries: by category, by availability, by verification, by rating
CREATE INDEX IF NOT EXISTS idx_technicians_category ON technicians(category);
CREATE INDEX IF NOT EXISTS idx_technicians_available_verified ON technicians(is_available, is_verified);
CREATE INDEX IF NOT EXISTS idx_technicians_rating ON technicians(rating DESC);
CREATE INDEX IF NOT EXISTS idx_technicians_category_available ON technicians(category, is_available, is_verified);
```

### Requests Table
```sql
-- Primary queries: by customer, by type, by status, by date
CREATE INDEX IF NOT EXISTS idx_requests_customer_id ON requests(customer_id);
CREATE INDEX IF NOT EXISTS idx_requests_type_status ON requests(type, status);
CREATE INDEX IF NOT EXISTS idx_requests_status_created ON requests(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_requests_customer_created ON requests(customer_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_requests_type_status_created ON requests(type, status, created_at DESC);
```

### Messages & Chats Tables
```sql
-- Primary queries: by chat, by sender, by date
CREATE INDEX IF NOT EXISTS idx_messages_chat_created ON messages(chat_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chats_users ON chats(user1_id, user2_id);
CREATE INDEX IF NOT EXISTS idx_chats_last_message ON chats(last_message_at DESC);
```

## Row Level Security (RLS) Policies

### Users Table
```sql
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Users can only see their own profile
CREATE POLICY "users_select_own" ON users
  FOR SELECT USING (auth.uid()::text = id);

-- Users can update their own profile  
CREATE POLICY "users_update_own" ON users
  FOR UPDATE USING (auth.uid()::text = id);

-- Admins can see all users
CREATE POLICY "admins_select_all_users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()::text AND role = 'admin'
    )
  );
```

### Agents Table
```sql
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;

-- Agents can see their own profile
CREATE POLICY "agents_select_own" ON agents
  FOR SELECT USING (auth.uid()::text = user_id);

-- Customers can see available verified agents
CREATE POLICY "customers_select_available_agents" ON agents
  FOR SELECT USING (is_available = true AND is_verified = true);

-- Admins can see all agents
CREATE POLICY "admins_select_all_agents" ON agents
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()::text AND role = 'admin'
    )
  );
```

### Requests Table
```sql
ALTER TABLE requests ENABLE ROW LEVEL SECURITY;

-- Users can see their own requests
CREATE POLICY "users_select_own_requests" ON requests
  FOR SELECT USING (auth.uid()::text = customer_id);

-- Agents can see available requests
CREATE POLICY "agents_select_available_requests" ON requests
  FOR SELECT USING (
    status = 'pending' AND 
    EXISTS (
      SELECT 1 FROM users u JOIN agents a ON u.id = a.user_id
      WHERE u.id = auth.uid()::text AND u.role = 'agent'
    )
  );

-- Admins can see all requests
CREATE POLICY "admins_select_all_requests" ON requests
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()::text AND role = 'admin'
    )
  );
```

## Query Optimization Best Practices

### 1. Always Select Specific Columns
❌ Bad:
```dart
.select('*')
```

✅ Good:
```dart
.select('id, name, email, created_at')
```

### 2. Use Proper Filtering
❌ Bad:
```dart
.select().eq('status', 'active').eq('verified', true)
```

✅ Good:
```dart
.select('id, name, status')
.eq('status', 'active')
.eq('verified', true)
.limit(50)
```

### 3. Implement Pagination
```dart
Future<List<Map<String, dynamic>>> getPaginatedRequests({
  int page = 0,
  int limit = 20,
}) async {
  final from = page * limit;
  final to = from + limit - 1;
  
  return await client
    .from('requests')
    .select('id, type, status, created_at')
    .order('created_at', ascending: false)
    .range(from, to);
}
```

### 4. Use Composite Filters for Common Queries
```dart
// For frequently used combinations
.eq('is_available', true)
.eq('is_verified', true)
.order('rating', ascending: false)
.limit(20)
```

## Performance Monitoring

### 1. Add Query Performance Logging
```dart
class QueryPerformanceLogger {
  static Future<T> trackQuery<T>(
    String queryName,
    Future<T> Function() query,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await query();
      stopwatch.stop();
      if (kDebugMode) {
        print('Query $queryName took ${stopwatch.elapsedMilliseconds}ms');
      }
      return result;
    } catch (e) {
      stopwatch.stop();
      if (kDebugMode) {
        print('Query $queryName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      }
      rethrow;
    }
  }
}
```

### 2. Implement Query Caching
```dart
class QueryCache {
  static final Map<String, CacheEntry> _cache = {};
  
  static Future<T> getCached<T>(
    String key,
    Future<T> Function() query, {
    Duration ttl = const Duration(minutes: 5),
  }) async {
    final entry = _cache[key];
    if (entry != null && !entry.isExpired) {
      return entry.value as T;
    }
    
    final result = await query();
    _cache[key] = CacheEntry(result, DateTime.now().add(ttl));
    return result;
  }
}
```

## Security Recommendations

### 1. Input Validation
- Always validate user inputs before database queries
- Use parameterized queries (Supabase handles this automatically)
- Sanitize text inputs to prevent injection attacks

### 2. Rate Limiting
```dart
class RateLimiter {
  static final Map<String, List<DateTime>> _requests = {};
  
  static bool isAllowed(String userId, {int maxRequests = 100, Duration window = const Duration(hours: 1)}) {
    final now = DateTime.now();
    final cutoff = now.subtract(window);
    
    _requests[userId] = _requests[userId]?.where((time) => time.isAfter(cutoff)).toList() ?? [];
    
    if (_requests[userId]!.length >= maxRequests) {
      return false;
    }
    
    _requests[userId]!.add(now);
    return true;
  }
}
```

### 3. Data Encryption
- Sensitive data should be encrypted at rest
- Use HTTPS for all communications
- Never store plain text passwords or tokens

### 4. Audit Logging
```sql
-- Create audit table for sensitive operations
CREATE TABLE audit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  action VARCHAR(50) NOT NULL,
  table_name VARCHAR(50) NOT NULL,
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Migration Scripts

Run these migrations in order:

1. **Create indexes** - Apply all index creation statements
2. **Enable RLS** - Enable row level security on all tables  
3. **Create policies** - Apply all security policies
4. **Create audit triggers** - Set up audit logging
5. **Test policies** - Verify RLS policies work correctly

## Monitoring Queries

### Performance Metrics
```sql
-- Query to find slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Query to find missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
ORDER BY n_distinct DESC;
```