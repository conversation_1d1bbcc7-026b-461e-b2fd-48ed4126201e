import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface LookupPayload {
  action: 'get_lookup_data' | 'get_service_rates' | 'get_technician_categories' | 'get_locations'
  type?: string
  serviceType?: string
  region?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, type, serviceType, region } = await req.json() as LookupPayload

    switch (action) {
      case 'get_lookup_data':
        return await getLookupData(supabase, type!)
      case 'get_service_rates':
        return await getServiceRates(supabase, serviceType!)
      case 'get_technician_categories':
        return await getTechnicianCategories(supabase)
      case 'get_locations':
        return await getLocations(supabase, region)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Lookup operation error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function getLookupData(supabase: any, type: string) {
  let tableName = ''
  
  switch (type) {
    case 'regions':
      tableName = 'regions'
      break
    case 'districts':
      tableName = 'districts'
      break
    case 'wards':
      tableName = 'wards'
      break
    case 'technician_categories':
      tableName = 'technician_categories'
      break
    default:
      throw new Error('Invalid lookup type')
  }

  const { data, error } = await supabase
    .from(tableName)
    .select('*')
    .order('name', { ascending: true })

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getServiceRates(supabase: any, serviceType: string) {
  const { data: rates, error } = await supabase
    .from('service_rates')
    .select('*')
    .eq('service_type', serviceType)
    .eq('is_active', true)
    .order('created_at', { ascending: false })

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, rates }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getTechnicianCategories(supabase: any) {
  const { data: categories, error } = await supabase
    .from('technician_categories')
    .select('*')
    .eq('is_active', true)
    .order('name', { ascending: true })

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, categories }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getLocations(supabase: any, region?: string) {
  let query = supabase
    .from('districts')
    .select(`
      *,
      region:regions!districts_region_id_fkey(id, name),
      wards!districts_id_fkey(id, name)
    `)
    .order('name', { ascending: true })

  if (region) {
    query = query.eq('region_id', region)
  }

  const { data: locations, error } = await query

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, locations }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}