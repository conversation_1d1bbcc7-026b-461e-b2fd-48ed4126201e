import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Backend API service that handles all communication with Supabase Edge Functions
/// This replaces direct Supabase client calls with backend API calls for better architecture
class BackendApiService {
  static const String baseUrl = 'https://hhlazpbeoqoknaqwxptx.supabase.co/functions/v1';
  
  // Headers for API requests
  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ${Supabase.instance.client.auth.currentSession?.accessToken ?? ''}',
    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhobGF6cGJlb3Fva25hcXd4cHR4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDUwMzcsImV4cCI6MjA2ODQyMTAzN30.AlQcAxnGtkYuCWV8hYf18x6hWD5sulMi8IyRqbeEdFE',
  };

  /// Generic API request handler
  static Future<Map<String, dynamic>> _makeRequest(
    String endpoint,
    Map<String, dynamic> body,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: _headers,
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        if (data['success'] == true) {
          return data;
        } else {
          throw BackendApiException(data['error'] ?? 'Unknown error occurred');
        }
      } else {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        throw BackendApiException(errorData['error'] ?? 'Request failed');
      }
    } catch (e) {
      if (e is BackendApiException) rethrow;
      throw BackendApiException('Network error: ${e.toString()}');
    }
  }

  // ======================= AUTHENTICATION OPERATIONS =======================

  /// Sign up with email and password
  static Future<Map<String, dynamic>> signUpWithEmail(
    String email,
    String password,
    Map<String, dynamic> userData,
  ) async {
    return await _makeRequest('auth-operations', {
      'action': 'signup',
      'email': email,
      'password': password,
      'userData': userData,
    });
  }

  /// Sign in with email and password
  static Future<Map<String, dynamic>> signInWithEmail(
    String email,
    String password,
  ) async {
    return await _makeRequest('auth-operations', {
      'action': 'signin',
      'email': email,
      'password': password,
    });
  }

  /// Sign out user
  static Future<Map<String, dynamic>> signOut() async {
    return await _makeRequest('auth-operations', {
      'action': 'signout',
    });
  }

  /// Reset password
  static Future<Map<String, dynamic>> resetPassword(String email) async {
    return await _makeRequest('auth-operations', {
      'action': 'reset_password',
      'email': email,
    });
  }

  // ======================= USER MANAGEMENT OPERATIONS =======================

  /// Create user profile
  static Future<Map<String, dynamic>> createUserProfile(
    Map<String, dynamic> userData,
    String userType,
  ) async {
    return await _makeRequest('user-management', {
      'action': 'create_profile',
      'userData': userData,
      'userType': userType,
    });
  }

  /// Update user profile
  static Future<Map<String, dynamic>> updateUserProfile(
    String userId,
    Map<String, dynamic> userData,
    String userType,
  ) async {
    return await _makeRequest('user-management', {
      'action': 'update_profile',
      'userId': userId,
      'userData': userData,
      'userType': userType,
    });
  }

  /// Get user profile
  static Future<Map<String, dynamic>> getUserProfile(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('user-management', {
      'action': 'get_profile',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Toggle user availability (for agents/technicians)
  static Future<Map<String, dynamic>> toggleAvailability(
    String userId,
    bool availability,
    String userType,
  ) async {
    return await _makeRequest('user-management', {
      'action': 'toggle_availability',
      'userId': userId,
      'availability': availability,
      'userType': userType,
    });
  }

  /// Verify user (admin function)
  static Future<Map<String, dynamic>> verifyUser(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('user-management', {
      'action': 'verify_user',
      'userId': userId,
      'userType': userType,
    });
  }

  // ======================= REQUEST OPERATIONS =======================

  /// Create a new request
  static Future<Map<String, dynamic>> createRequest(
    Map<String, dynamic> requestData,
  ) async {
    return await _makeRequest('request-operations', {
      'action': 'create',
      'requestData': requestData,
    });
  }

  /// Update request
  static Future<Map<String, dynamic>> updateRequest(
    String requestId,
    Map<String, dynamic> requestData,
  ) async {
    return await _makeRequest('request-operations', {
      'action': 'update',
      'requestId': requestId,
      'requestData': requestData,
    });
  }

  /// Cancel request
  static Future<Map<String, dynamic>> cancelRequest(
    String requestId,
    String userId,
  ) async {
    return await _makeRequest('request-operations', {
      'action': 'cancel',
      'requestId': requestId,
      'userId': userId,
    });
  }

  /// Complete request
  static Future<Map<String, dynamic>> completeRequest(
    String requestId,
    String userId,
  ) async {
    return await _makeRequest('request-operations', {
      'action': 'complete',
      'requestId': requestId,
      'userId': userId,
    });
  }

  /// Assign request to agent/technician
  static Future<Map<String, dynamic>> assignRequest(
    String requestId,
    String assigneeId,
    String userId,
  ) async {
    return await _makeRequest('request-operations', {
      'action': 'assign',
      'requestId': requestId,
      'assigneeId': assigneeId,
      'userId': userId,
    });
  }

  // ======================= CHAT OPERATIONS =======================

  /// Create or get existing chat
  static Future<Map<String, dynamic>> createChat(
    Map<String, dynamic> chatData,
  ) async {
    return await _makeRequest('chat-operations', {
      'action': 'create_chat',
      'chatData': chatData,
    });
  }

  /// Send message
  static Future<Map<String, dynamic>> sendMessage(
    Map<String, dynamic> messageData,
  ) async {
    return await _makeRequest('chat-operations', {
      'action': 'send_message',
      'messageData': messageData,
    });
  }

  /// Get user chats
  static Future<Map<String, dynamic>> getUserChats(String userId) async {
    return await _makeRequest('chat-operations', {
      'action': 'get_chats',
      'userId': userId,
    });
  }

  /// Get chat messages
  static Future<Map<String, dynamic>> getChatMessages(String chatId) async {
    return await _makeRequest('chat-operations', {
      'action': 'get_messages',
      'chatId': chatId,
    });
  }

  /// Mark messages as read
  static Future<Map<String, dynamic>> markMessagesAsRead(
    String chatId,
    String userId,
  ) async {
    return await _makeRequest('chat-operations', {
      'action': 'mark_read',
      'chatId': chatId,
      'userId': userId,
    });
  }

  /// Update user status (online/offline/typing)
  static Future<Map<String, dynamic>> updateUserStatus(
    String userId,
    String status,
  ) async {
    return await _makeRequest('chat-operations', {
      'action': 'update_status',
      'userId': userId,
      'status': status,
    });
  }

  // ======================= LOOKUP DATA OPERATIONS =======================

  /// Get lookup data (regions, districts, categories, etc.)
  static Future<Map<String, dynamic>> getLookupData(String type) async {
    return await _makeRequest('lookup-operations', {
      'action': 'get_lookup_data',
      'type': type,
    });
  }

  /// Get service rates
  static Future<Map<String, dynamic>> getServiceRates(String serviceType) async {
    return await _makeRequest('lookup-operations', {
      'action': 'get_service_rates',
      'serviceType': serviceType,
    });
  }

  // ======================= NOTIFICATION OPERATIONS =======================

  /// Get user notifications
  static Future<Map<String, dynamic>> getUserNotifications(String userId) async {
    return await _makeRequest('notification-operations', {
      'action': 'get_notifications',
      'userId': userId,
    });
  }

  /// Mark notification as read
  static Future<Map<String, dynamic>> markNotificationAsRead(
    String notificationId,
  ) async {
    return await _makeRequest('notification-operations', {
      'action': 'mark_read',
      'notificationId': notificationId,
    });
  }

  /// Create notification
  static Future<Map<String, dynamic>> createNotification(
    Map<String, dynamic> notificationData,
  ) async {
    return await _makeRequest('notification-operations', {
      'action': 'create_notification',
      'notificationData': notificationData,
    });
  }

  // ======================= REVIEW OPERATIONS =======================

  /// Get user reviews
  static Future<Map<String, dynamic>> getUserReviews(String userId) async {
    return await _makeRequest('review-operations', {
      'action': 'get_reviews',
      'userId': userId,
    });
  }

  /// Create review
  static Future<Map<String, dynamic>> createReview(
    Map<String, dynamic> reviewData,
  ) async {
    return await _makeRequest('review-operations', {
      'action': 'create_review',
      'reviewData': reviewData,
    });
  }

  // ======================= ANALYTICS OPERATIONS =======================

  /// Get user analytics
  static Future<Map<String, dynamic>> getUserAnalytics(String userId) async {
    return await _makeRequest('analytics-operations', {
      'action': 'get_user_analytics',
      'userId': userId,
    });
  }

  /// Get platform analytics (admin only)
  static Future<Map<String, dynamic>> getPlatformAnalytics() async {
    return await _makeRequest('analytics-operations', {
      'action': 'get_platform_analytics',
    });
  }

  // ======================= PORTFOLIO OPERATIONS =======================

  /// Get comprehensive portfolio data for a user
  static Future<Map<String, dynamic>> getUserPortfolio(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('portfolio-operations', {
      'action': 'get_portfolio',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Get portfolio statistics
  static Future<Map<String, dynamic>> getPortfolioStats(
    String userId,
    String userType,
    String? timeframe,
  ) async {
    return await _makeRequest('portfolio-operations', {
      'action': 'get_stats',
      'userId': userId,
      'userType': userType,
      'timeframe': timeframe,
    });
  }

  /// Get portfolio work history
  static Future<Map<String, dynamic>> getPortfolioWorkHistory(
    String userId,
    String userType,
    int? limit,
    int? offset,
  ) async {
    return await _makeRequest('portfolio-operations', {
      'action': 'get_work_history',
      'userId': userId,
      'userType': userType,
      'limit': limit,
      'offset': offset,
    });
  }

  /// Get portfolio achievements
  static Future<Map<String, dynamic>> getPortfolioAchievements(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('portfolio-operations', {
      'action': 'get_achievements',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Get portfolio skills and ratings
  static Future<Map<String, dynamic>> getPortfolioSkills(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('portfolio-operations', {
      'action': 'get_skills',
      'userId': userId,
      'userType': userType,
    });
  }

  // ======================= PROFILE ENHANCEMENT OPERATIONS =======================

  /// Get enhanced profile data with dynamic metrics
  static Future<Map<String, dynamic>> getEnhancedProfile(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('profile-operations', {
      'action': 'get_enhanced_profile',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Get user activity summary
  static Future<Map<String, dynamic>> getUserActivitySummary(
    String userId,
    String period,
  ) async {
    return await _makeRequest('profile-operations', {
      'action': 'get_activity_summary',
      'userId': userId,
      'period': period,
    });
  }

  /// Get user performance metrics
  static Future<Map<String, dynamic>> getUserPerformanceMetrics(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('profile-operations', {
      'action': 'get_performance_metrics',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Update profile availability/status
  static Future<Map<String, dynamic>> updateProfileAvailability(
    String userId,
    String userType,
    Map<String, dynamic> availabilityData,
  ) async {
    return await _makeRequest('profile-operations', {
      'action': 'update_availability',
      'userId': userId,
      'userType': userType,
      'availabilityData': availabilityData,
    });
  }

  /// Get user settings and preferences
  static Future<Map<String, dynamic>> getUserSettings(String userId) async {
    return await _makeRequest('profile-operations', {
      'action': 'get_settings',
      'userId': userId,
    });
  }

  /// Update user settings
  static Future<Map<String, dynamic>> updateUserSettings(
    String userId,
    Map<String, dynamic> settings,
  ) async {
    return await _makeRequest('profile-operations', {
      'action': 'update_settings',
      'userId': userId,
      'settings': settings,
    });
  }

  // ======================= ENHANCED CHAT OPERATIONS =======================

  /// Get chat overview with enhanced metrics
  static Future<Map<String, dynamic>> getChatOverview(String userId) async {
    return await _makeRequest('enhanced-chat-operations', {
      'action': 'get_overview',
      'userId': userId,
    });
  }

  /// Get chat analytics
  static Future<Map<String, dynamic>> getChatAnalytics(
    String userId,
    String? timeframe,
  ) async {
    return await _makeRequest('enhanced-chat-operations', {
      'action': 'get_analytics',
      'userId': userId,
      'timeframe': timeframe,
    });
  }

  /// Get recent conversations with context
  static Future<Map<String, dynamic>> getRecentConversations(
    String userId,
    int? limit,
  ) async {
    return await _makeRequest('enhanced-chat-operations', {
      'action': 'get_recent_conversations',
      'userId': userId,
      'limit': limit,
    });
  }

  /// Search messages across all chats
  static Future<Map<String, dynamic>> searchMessages(
    String userId,
    String query,
  ) async {
    return await _makeRequest('enhanced-chat-operations', {
      'action': 'search_messages',
      'userId': userId,
      'query': query,
    });
  }

  /// Get message statistics
  static Future<Map<String, dynamic>> getMessageStats(
    String userId,
    String? chatId,
  ) async {
    return await _makeRequest('enhanced-chat-operations', {
      'action': 'get_message_stats',
      'userId': userId,
      'chatId': chatId,
    });
  }

  // ======================= ADDITIONAL DYNAMIC DATA METHODS =======================

  /// Get user profile statistics
  static Future<Map<String, dynamic>> getUserProfileStats(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('profile-operations', {
      'action': 'get_profile_stats',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Get agent profile with dynamic data
  static Future<Map<String, dynamic>> getAgentProfile(
    String userId,
  ) async {
    return await _makeRequest('agent-operations', {
      'action': 'get_agent_profile',
      'userId': userId,
    });
  }

  /// Update agent availability
  static Future<Map<String, dynamic>> updateAgentAvailability(
    String userId,
    bool isAvailable,
  ) async {
    return await _makeRequest('agent-operations', {
      'action': 'update_availability',
      'userId': userId,
      'isAvailable': isAvailable,
    });
  }

  /// Get technician profile with dynamic data
  static Future<Map<String, dynamic>> getTechnicianProfile(
    String userId,
  ) async {
    return await _makeRequest('technician-operations', {
      'action': 'get_technician_profile',
      'userId': userId,
    });
  }

  /// Get user chat statistics
  static Future<Map<String, dynamic>> getUserChatStats(
    String userId,
  ) async {
    return await _makeRequest('chat-operations', {
      'action': 'get_user_chat_stats',
      'userId': userId,
    });
  }

  /// Get user performance dashboard data
  static Future<Map<String, dynamic>> getUserPerformanceDashboard(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('performance-operations', {
      'action': 'get_user_dashboard',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Get real-time user metrics
  static Future<Map<String, dynamic>> getUserRealTimeMetrics(
    String userId,
  ) async {
    return await _makeRequest('analytics-operations', {
      'action': 'get_realtime_metrics',
      'userId': userId,
    });
  }

  /// Batch update user data
  static Future<Map<String, dynamic>> batchUpdateUserData(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    return await _makeRequest('user-management', {
      'action': 'batch_update',
      'userId': userId,
      'updates': updates,
    });
  }

  /// Get user insights and recommendations
  static Future<Map<String, dynamic>> getUserInsights(
    String userId,
    String userType,
  ) async {
    return await _makeRequest('insights-operations', {
      'action': 'get_user_insights',
      'userId': userId,
      'userType': userType,
    });
  }

  /// Sync user data across devices
  static Future<Map<String, dynamic>> syncUserData(
    String userId,
    Map<String, dynamic> deviceData,
  ) async {
    return await _makeRequest('sync-operations', {
      'action': 'sync_user_data',
      'userId': userId,
      'deviceData': deviceData,
    });
  }

  /// Update technician availability
  static Future<Map<String, dynamic>> updateTechnicianAvailability(
    String userId,
    bool isAvailable,
  ) async {
    return await _makeRequest('technician-operations', {
      'action': 'update_availability',
      'userId': userId,
      'isAvailable': isAvailable,
    });
  }

  /// Get agent performance metrics
  static Future<Map<String, dynamic>> getAgentPerformanceMetrics(
    String userId,
  ) async {
    return await _makeRequest('agent-operations', {
      'action': 'get_performance_metrics',
      'userId': userId,
    });
  }
}

/// Custom exception for backend API errors
class BackendApiException implements Exception {
  final String message;
  
  BackendApiException(this.message);
  
  @override
  String toString() => 'BackendApiException: $message';
}