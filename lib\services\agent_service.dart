import 'dart:math' as math;
import 'package:mlink/models/agent.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/supabase/supabase_config.dart';

class AgentService {
  static final AgentService _instance = AgentService._internal();
  factory AgentService() => _instance;
  AgentService._internal();

  static AgentService get instance => _instance;

  Future<List<Agent>> getAvailableAgents() async {
    try {
      final response = await SupabaseConfig.getAvailableAgents();
      return response.map((data) => Agent.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get available agents: $e');
    }
  }

  Future<Agent?> getAgentProfile(String userId) async {
    try {
      final data = await SupabaseConfig.getAgentProfile(userId);
      if (data == null) return null;
      return Agent.fromJson(data);
    } catch (e) {
      throw Exception('Failed to get agent profile: $e');
    }
  }

  Future<void> createAgentProfile(Agent agent) async {
    try {
      await SupabaseConfig.client.from('agents').insert(agent.toJson());
    } catch (e) {
      throw Exception('Failed to create agent profile: $e');
    }
  }

  Future<void> updateAgentProfile(Agent agent) async {
    try {
      await SupabaseConfig.client
          .from('agents')
          .update(agent.toJson())
          .eq('id', agent.id);
    } catch (e) {
      throw Exception('Failed to update agent profile: $e');
    }
  }

  Future<void> updateAgentLocation(String agentId, double latitude, double longitude) async {
    try {
      await SupabaseConfig.client
          .from('agents')
          .update({
            'current_location_lat': latitude,
            'current_location_lng': longitude,
            'last_location_update': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', agentId);
    } catch (e) {
      throw Exception('Failed to update agent location: $e');
    }
  }

  Future<void> updateAgentAvailability(String agentId, bool isAvailable) async {
    try {
      await SupabaseConfig.client
          .from('agents')
          .update({
            'is_available': isAvailable,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', agentId);
    } catch (e) {
      throw Exception('Failed to update agent availability: $e');
    }
  }

  Future<void> updateAgentStats(String agentId, {int? totalDeliveries, double? totalEarnings, double? rating}) async {
    try {
      Map<String, dynamic> updateData = {'updated_at': DateTime.now().toIso8601String()};
      
      if (totalDeliveries != null) {
        updateData['total_deliveries'] = totalDeliveries;
      }
      if (totalEarnings != null) {
        updateData['total_earnings'] = totalEarnings;
      }
      if (rating != null) {
        updateData['rating'] = rating;
      }

      await SupabaseConfig.client
          .from('agents')
          .update(updateData)
          .eq('id', agentId);
    } catch (e) {
      throw Exception('Failed to update agent stats: $e');
    }
  }

  Future<List<Agent>> getAgentsByVehicleType(VehicleType vehicleType) async {
    try {
      final response = await SupabaseConfig.client
          .from('agents')
          .select('*, users!user_id(full_name, phone_number)')
          .eq('vehicle_type', vehicleType.name)
          .eq('is_available', true)
          .eq('is_verified', true);
      
      return response.map((data) => Agent.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get agents by vehicle type: $e');
    }
  }

  Future<List<Agent>> getAgentsByLocation(double latitude, double longitude, double radiusKm) async {
    try {
      final response = await SupabaseConfig.client
          .from('agents')
          .select('*, users!inner(full_name, phone_number)')
          .eq('is_available', true)
          .eq('is_verified', true)
          .not('current_location_lat', 'is', null)
          .not('current_location_lng', 'is', null);
      
      return response
          .map((data) => Agent.fromJson(data))
          .where((agent) => agent.currentLocationLat != null && agent.currentLocationLng != null && 
              _isWithinRadius(agent.currentLocationLat!, agent.currentLocationLng!, latitude, longitude, radiusKm))
          .toList();
    } catch (e) {
      throw Exception('Failed to get agents by location: $e');
    }
  }

  Future<void> verifyAgent(String agentId, bool isVerified) async {
    try {
      await SupabaseConfig.client
          .from('agents')
          .update({
            'is_verified': isVerified,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', agentId);
    } catch (e) {
      throw Exception('Failed to verify agent: $e');
    }
  }

  Future<Map<String, dynamic>> getAgentStats(String agentId) async {
    try {
      final agent = await SupabaseConfig.client
          .from('agents')
          .select()
          .eq('id', agentId)
          .single();
      
      // Get additional stats from requests
      final deliveryRequests = await SupabaseConfig.client
          .from('delivery_requests')
          .select('*, requests!inner(*)')
          .eq('agent_id', agentId);
      
      final buyForMeRequests = await SupabaseConfig.client
          .from('buy_for_me_requests')
          .select('*, requests!inner(*)')
          .eq('agent_id', agentId);
      
      final totalRequests = deliveryRequests.length + buyForMeRequests.length;
      final completedRequests = deliveryRequests.where((r) => r['requests']['status'] == 'completed').length +
          buyForMeRequests.where((r) => r['requests']['status'] == 'completed').length;
      
      return {
        'total_deliveries': agent['total_deliveries'] ?? 0,
        'total_earnings': agent['total_earnings'] ?? 0.0,
        'rating': agent['rating'] ?? 0.0,
        'total_requests': totalRequests,
        'completed_requests': completedRequests,
        'success_rate': totalRequests > 0 ? (completedRequests / totalRequests * 100).round() : 0,
        'is_verified': agent['is_verified'] ?? false,
        'is_available': agent['is_available'] ?? false,
      };
    } catch (e) {
      throw Exception('Failed to get agent stats: $e');
    }
  }

  Future<void> deleteAgentProfile(String agentId) async {
    try {
      await SupabaseConfig.client
          .from('agents')
          .delete()
          .eq('id', agentId);
    } catch (e) {
      throw Exception('Failed to delete agent profile: $e');
    }
  }

  bool _isWithinRadius(double agentLat, double agentLng, double centerLat, double centerLng, double radiusKm) {
    // Simplified distance calculation - in production use a proper geospatial library
    const double earthRadius = 6371; // km
    double dLat = _degreesToRadians(agentLat - centerLat);
    double dLng = _degreesToRadians(agentLng - centerLng);
    double a = math.sin(dLat/2) * math.sin(dLat/2) +
        math.cos(_degreesToRadians(centerLat)) * math.cos(_degreesToRadians(agentLat)) *
        math.sin(dLng/2) * math.sin(dLng/2);
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a));
    double distance = earthRadius * c;
    return distance <= radiusKm;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}