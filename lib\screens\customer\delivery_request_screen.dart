import 'package:flutter/material.dart';
import 'package:mlink/models/agent.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/models/service_rates.dart';
import 'package:mlink/widgets/app_logo.dart';
import 'package:mlink/widgets/location_selector.dart';
import 'package:mlink/screens/maps/location_picker_screen.dart';
import 'package:mlink/services/maps_service.dart';
import 'package:mlink/services/configuration_service.dart';
import 'dart:math' as math;

class DeliveryRequestScreen extends StatefulWidget {
  const DeliveryRequestScreen({super.key});

  @override
  State<DeliveryRequestScreen> createState() => _DeliveryRequestScreenState();
}

class _DeliveryRequestScreenState extends State<DeliveryRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _pickupAddressController = TextEditingController();
  final _dropoffAddressController = TextEditingController();
  final _recipientNameController = TextEditingController();
  final _recipientPhoneController = TextEditingController();
  final _notesController = TextEditingController();
  
  final MapsService _mapsService = MapsService.instance;
  LocationPoint? _pickupLocation;
  LocationPoint? _dropoffLocation;
  
  // Pickup location selector state
  String? _selectedPickupRegion;
  String? _selectedPickupDistrict;
  LocationPoint? _customPickupLocation;
  
  // Dropoff location selector state
  String? _selectedDropoffRegion;
  String? _selectedDropoffDistrict;
  LocationPoint? _customDropoffLocation;
  
  double? _estimatedDistance;
  double? _estimatedCost;

  VehicleType _selectedVehicle = VehicleType.bodaboda;
  DeliveryPriority _selectedPriority = DeliveryPriority.normal;
  PaymentMethod _selectedPayment = PaymentMethod.cash;
  bool _requiresSignature = false;
  bool _isScheduled = false;
  DateTime? _scheduledDate;
  TimeOfDay? _scheduledTime;
  
  // Collapsible section states
  bool _isVehicleTypeExpanded = false;
  bool _isPaymentMethodExpanded = false;
  
  void _updatePickupLocation() {
    final selector = SelectedLocation(
      region: _selectedPickupRegion,
      district: _selectedPickupDistrict,
      customLocation: _customPickupLocation,
    );
    _pickupLocation = selector.locationPoint;
    _calculateDistance();
  }
  
  void _updateDropoffLocation() {
    final selector = SelectedLocation(
      region: _selectedDropoffRegion,
      district: _selectedDropoffDistrict,
      customLocation: _customDropoffLocation,
    );
    _dropoffLocation = selector.locationPoint;
    _calculateDistance();
  }
  
  void _calculateDeliveryCost(double distance) async {
    try {
      final rates = await ConfigurationService.getDeliveryRates();
      final cost = rates['base_cost']! + (distance * rates['cost_per_km']!);
      setState(() {
        _estimatedCost = cost;
      });
    } catch (e) {
      // Fallback to hardcoded rates
      setState(() {
        _estimatedCost = DeliveryCostCalculator.calculateDeliveryCost(distance);
      });
    }
  }

  void _calculateDistance() {
    if (_pickupLocation != null && _dropoffLocation != null) {
      final distance = _calculateDistanceBetweenPoints(
        _pickupLocation!.latitude,
        _pickupLocation!.longitude,
        _dropoffLocation!.latitude,
        _dropoffLocation!.longitude,
      );
      setState(() {
        _estimatedDistance = distance;
        _calculateDeliveryCost(distance);
      });
    } else {
      setState(() {
        _estimatedDistance = null;
        _estimatedCost = null;
      });
    }
  }
  
  double _calculateDistanceBetweenPoints(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);
    
    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);
    
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    double distance = earthRadius * c;
    
    return distance;
  }
  
  double _degreesToRadians(double degrees) {
    return degrees * math.pi / 180;
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _pickupAddressController.dispose();
    _dropoffAddressController.dispose();
    _recipientNameController.dispose();
    _recipientPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Delivery Request'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: AppLogo(
              width: AppLogoSizes.small,
              height: AppLogoSizes.small,
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.motorcycle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Delivery Service',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Send packages anywhere in the city',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Package Description
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: 'Package Description',
                  prefixIcon: Icon(
                    Icons.inventory_2_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  helperText: 'What are you sending?',
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please describe your package';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Pickup Address
              LocationSelector(
                label: 'Pickup Location',
                selectedRegion: _selectedPickupRegion,
                selectedDistrict: _selectedPickupDistrict,
                customLocation: _customPickupLocation,
                onRegionChanged: (region) {
                  setState(() {
                    _selectedPickupRegion = region;
                    _updatePickupLocation();
                  });
                },
                onDistrictChanged: (district) {
                  setState(() {
                    _selectedPickupDistrict = district;
                    _updatePickupLocation();
                  });
                },
                onCustomLocationChanged: (location) {
                  setState(() {
                    _customPickupLocation = location;
                    _updatePickupLocation();
                  });
                },
                hint: 'e.g., Kariakoo, Ilala, Mbezi Beach',
              ),
              const SizedBox(height: 16),

              // Dropoff Address
              LocationSelector(
                label: 'Delivery Location',
                selectedRegion: _selectedDropoffRegion,
                selectedDistrict: _selectedDropoffDistrict,
                customLocation: _customDropoffLocation,
                onRegionChanged: (region) {
                  setState(() {
                    _selectedDropoffRegion = region;
                    _updateDropoffLocation();
                  });
                },
                onDistrictChanged: (district) {
                  setState(() {
                    _selectedDropoffDistrict = district;
                    _updateDropoffLocation();
                  });
                },
                onCustomLocationChanged: (location) {
                  setState(() {
                    _customDropoffLocation = location;
                    _updateDropoffLocation();
                  });
                },
                hint: 'e.g., Masaki, Oyster Bay, Sinza',
              ),
              
              // Distance and Cost Estimation
              if (_estimatedDistance != null && _estimatedCost != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.route_outlined,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Delivery Estimate',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Distance: ${_estimatedDistance!.toStringAsFixed(1)} km',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          Text(
                            'Cost: ${ServiceRates.formatCurrency(_estimatedCost!)}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DeliveryCostCalculator.getDeliveryCostBreakdown(_estimatedDistance!),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 16),

              // Recipient Information
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _recipientNameController,
                      decoration: InputDecoration(
                        labelText: 'Recipient Name',
                        prefixIcon: Icon(
                          Icons.person_outline,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Enter recipient name';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: _recipientPhoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        labelText: 'Phone Number',
                        prefixIcon: Icon(
                          Icons.phone_outlined,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Enter phone number';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Vehicle Selection
              Text(
                'Vehicle Type',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<VehicleType>(
                  value: _selectedVehicle,
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.local_shipping_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    labelText: 'Select Vehicle Type',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  isExpanded: true,
                  menuMaxHeight: 250,
                  items: VehicleType.values.map((vehicle) {
                    return DropdownMenuItem(
                      value: vehicle,
                      child: FutureBuilder<List<String>>(
                        future: Future.wait([
                          _getVehicleDisplayName(vehicle),
                          _getVehicleDescription(vehicle),
                        ]),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    snapshot.data![0],
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    snapshot.data![1],
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                ],
                              ),
                            );
                          }
                          return Text(vehicle.name); // Fallback while loading
                        },
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedVehicle = value!);
                  },
                ),
              ),
              const SizedBox(height: 24),

              // Priority Selection
              Text(
                'Delivery Priority',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<DeliveryPriority>(
                  value: _selectedPriority,
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.speed_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    labelText: 'Select Priority',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  isExpanded: true,
                  menuMaxHeight: 200,
                  items: DeliveryPriority.values.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: FutureBuilder<String>(
                        future: _getPriorityDescription(priority),
                        builder: (context, snapshot) {
                          return Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  _getPriorityDisplayName(priority),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  snapshot.hasData ? snapshot.data! : 'Loading...',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedPriority = value!);
                  },
                ),
              ),
              const SizedBox(height: 24),

              // Additional Options
              Text(
                'Additional Options',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              CheckboxListTile(
                title: const Text('Require recipient signature'),
                value: _requiresSignature,
                onChanged: (value) {
                  setState(() => _requiresSignature = value ?? false);
                },
              ),
              
              CheckboxListTile(
                title: const Text('Schedule for later'),
                value: _isScheduled,
                onChanged: (value) {
                  setState(() => _isScheduled = value ?? false);
                },
              ),

              if (_isScheduled) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _selectDate(context),
                        icon: const Icon(Icons.calendar_today),
                        label: Text(
                          _scheduledDate != null
                              ? '${_scheduledDate!.day}/${_scheduledDate!.month}/${_scheduledDate!.year}'
                              : 'Select Date',
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _selectTime(context),
                        icon: const Icon(Icons.access_time),
                        label: Text(
                          _scheduledTime != null
                              ? _scheduledTime!.format(context)
                              : 'Select Time',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 24),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: 'Special Instructions (Optional)',
                  prefixIcon: Icon(
                    Icons.note_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              // Payment Method
              Text(
                'Payment Method',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<PaymentMethod>(
                  value: _selectedPayment,
                  decoration: InputDecoration(
                    prefixIcon: Icon(
                      Icons.payment_outlined,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    labelText: 'Select Payment Method',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  isExpanded: true,
                  menuMaxHeight: 250,
                  items: PaymentMethod.values.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: FutureBuilder<String>(
                        future: _getPaymentDisplayName(method),
                        builder: (context, snapshot) {
                          return Text(
                            snapshot.hasData ? snapshot.data! : method.name,
                            overflow: TextOverflow.ellipsis,
                          );
                        },
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedPayment = value!);
                  },
                ),
              ),
              const SizedBox(height: 32),

              // Submit Button
              SizedBox(
                height: 56,
                child: ElevatedButton(
                  onPressed: _submitRequest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Submit Request',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (picked != null) {
      setState(() => _scheduledDate = picked);
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() => _scheduledTime = picked);
    }
  }

  Future<void> _selectLocation({required bool isPickup}) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LocationPickerScreen(
          title: isPickup ? 'Select Pickup Location' : 'Select Delivery Location',
          initialLocation: isPickup ? _pickupLocation : _dropoffLocation,
        ),
      ),
    );
    
    if (result != null && result is LocationPoint) {
      setState(() {
        if (isPickup) {
          _pickupLocation = result;
          _pickupAddressController.text = result.address ?? '';
        } else {
          _dropoffLocation = result;
          _dropoffAddressController.text = result.address ?? '';
        }
      });
    }
  }
  
  Future<void> _getCurrentLocation({required bool isPickup}) async {
    final currentLocation = await _mapsService.getCurrentLocationPoint();
    if (currentLocation != null) {
      setState(() {
        if (isPickup) {
          _pickupLocation = currentLocation;
          _pickupAddressController.text = currentLocation.address ?? '';
        } else {
          _dropoffLocation = currentLocation;
          _dropoffAddressController.text = currentLocation.address ?? '';
        }
      });
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to get current location. Please check permissions.'),
          ),
        );
      }
    }
  }

  void _submitRequest() {
    if (!_formKey.currentState!.validate()) return;

    if (_pickupLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a pickup location'),
        ),
      );
      return;
    }

    if (_dropoffLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a delivery location'),
        ),
      );
      return;
    }

    if (_isScheduled && (_scheduledDate == null || _scheduledTime == null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select date and time for scheduled delivery'),
        ),
      );
      return;
    }

    // TODO: Submit delivery request
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Delivery request submitted successfully!'),
      ),
    );
    Navigator.of(context).pop();
  }

  Future<String> _getVehicleDisplayName(VehicleType vehicle) async {
    return await ConfigurationService.getVehicleDisplayName(vehicle.name);
  }

  Future<String> _getVehicleDescription(VehicleType vehicle) async {
    return await ConfigurationService.getVehicleDescription(vehicle.name);
  }

  String _getPriorityDisplayName(DeliveryPriority priority) {
    // Convert enum to readable name
    switch (priority) {
      case DeliveryPriority.normal:
        return 'Normal Delivery';
      case DeliveryPriority.urgent:
        return 'Urgent Delivery';
      case DeliveryPriority.scheduled:
        return 'Scheduled Delivery';
    }
  }

  Future<String> _getPriorityDescription(DeliveryPriority priority) async {
    return await ConfigurationService.getDeliveryPriorityDescription(priority.name);
  }

  Future<String> _getPaymentDisplayName(PaymentMethod method) async {
    return await ConfigurationService.getPaymentMethodDisplayName(method.name);
  }
}