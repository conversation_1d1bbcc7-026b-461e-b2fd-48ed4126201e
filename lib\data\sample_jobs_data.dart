import 'package:mlink/models/request.dart';
import 'package:mlink/models/agent.dart';
import 'package:mlink/models/technician.dart';

class SampleJobsData {
  static List<DeliveryRequest> getSampleDeliveryRequests() {
    final now = DateTime.now();
    
    return [
      DeliveryRequest(
        id: 'delivery_001',
        customerId: 'customer_001',
        description: 'Deliver documents to Kariakoo office',
        pickupLocation: LocationPoint(
          latitude: -6.8175,
          longitude: 39.2860,
          address: 'Mlimani City Mall, Sam Nujoma Road, Dar es Salaam',
          landmark: 'Near Mlimani City Mall main entrance',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.8235,
          longitude: 39.2695,
          address: 'Kariakoo Market, Dar es Salaam',
          landmark: 'Kariakoo Market main building',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 15000,
        ),
        createdAt: now.subtract(const Duration(minutes: 15)),
        updatedAt: now.subtract(const Duration(minutes: 15)),
        preferredVehicle: VehicleType.bodaboda,
        priority: DeliveryPriority.urgent,
        estimatedWeight: 0.5,
        packageType: 'Documents',
        requiresSignature: true,
        recipientName: '<PERSON>',
        recipientPhone: '+255712345678',
        customerPhone: '+255787654321',
      ),
      
      DeliveryRequest(
        id: 'delivery_002',
        customerId: 'customer_002',
        description: 'Deliver lunch from restaurant to office',
        pickupLocation: LocationPoint(
          latitude: -6.7924,
          longitude: 39.2083,
          address: 'Masaki Peninsula, Dar es Salaam',
          landmark: 'Coral Beach Hotel Restaurant',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.7833,
          longitude: 39.2667,
          address: 'Upanga, Dar es Salaam',
          landmark: 'PSPF Tower, 5th Floor',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 8000,
        ),
        createdAt: now.subtract(const Duration(minutes: 30)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
        preferredVehicle: VehicleType.bodaboda,
        priority: DeliveryPriority.normal,
        estimatedWeight: 2.0,
        packageType: 'Food',
        requiresSignature: false,
        recipientName: 'Sarah Hassan',
        recipientPhone: '+255765432109',
        customerPhone: '+255723456789',
      ),
      
      DeliveryRequest(
        id: 'delivery_003',
        customerId: 'customer_003',
        description: 'Pick up and deliver spare parts',
        pickupLocation: LocationPoint(
          latitude: -6.7731,
          longitude: 39.2447,
          address: 'Kariakoo, Dar es Salaam',
          landmark: 'Muhimbili Street, near bus stand',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.6928,
          longitude: 39.2156,
          address: 'Mikocheni, Dar es Salaam',
          landmark: 'Mikocheni Industrial Area',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 25000,
        ),
        createdAt: now.subtract(const Duration(hours: 1)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        preferredVehicle: VehicleType.van,
        priority: DeliveryPriority.normal,
        estimatedWeight: 15.0,
        packageType: 'Auto Parts',
        requiresSignature: true,
        recipientName: 'Ahmed Mfalme',
        recipientPhone: '+255654321098',
        customerPhone: '+255745123678',
        notes: 'Handle with care - fragile electronic parts',
      ),
      
      DeliveryRequest(
        id: 'delivery_004',
        customerId: 'customer_004',
        description: 'Deliver medical supplies urgently',
        pickupLocation: LocationPoint(
          latitude: -6.7833,
          longitude: 39.2667,
          address: 'Ilala District, Dar es Salaam',
          landmark: 'Muhimbili National Hospital Pharmacy',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.8500,
          longitude: 39.2700,
          address: 'Temeke District, Dar es Salaam',
          landmark: 'Temeke Regional Hospital',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 30000,
          advance: 15000,
        ),
        createdAt: now.subtract(const Duration(minutes: 5)),
        updatedAt: now.subtract(const Duration(minutes: 5)),
        preferredVehicle: VehicleType.car,
        priority: DeliveryPriority.urgent,
        estimatedWeight: 5.0,
        packageType: 'Medical Supplies',
        requiresSignature: true,
        recipientName: 'Dr. Fatuma Kikwete',
        recipientPhone: '+255789012345',
        customerPhone: '+255756789012',
        notes: 'URGENT: Temperature-sensitive medication',
      ),
      
      DeliveryRequest(
        id: 'delivery_005',
        customerId: 'customer_005',
        description: 'Furniture delivery from shop to home',
        pickupLocation: LocationPoint(
          latitude: -6.7924,
          longitude: 39.2951,
          address: 'Sinza, Dar es Salaam',
          landmark: 'Sinza Furniture Center',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.7500,
          longitude: 39.2800,
          address: 'Mbezi Beach, Dar es Salaam',
          landmark: 'Mbezi Beach Estate, Block C',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 45000,
        ),
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        preferredVehicle: VehicleType.truck,
        priority: DeliveryPriority.scheduled,
        estimatedWeight: 80.0,
        packageType: 'Furniture',
        requiresSignature: true,
        recipientName: 'Grace Mwamba',
        recipientPhone: '+255678901234',
        customerPhone: '+255712345678',
        scheduledAt: now.add(const Duration(hours: 3)),
        notes: 'Large sofa set - needs 2 people to carry',
      ),
    ];
  }
  
  static List<BuyForMeRequest> getSampleBuyForMeRequests() {
    final now = DateTime.now();
    
    return [
      BuyForMeRequest(
        id: 'buy_001',
        customerId: 'customer_006',
        description: 'Buy groceries from Shoprite',
        pickupLocation: LocationPoint(
          latitude: -6.8175,
          longitude: 39.2860,
          address: 'Mlimani City Mall, Sam Nujoma Road, Dar es Salaam',
          landmark: 'Shoprite Supermarket',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.7833,
          longitude: 39.2667,
          address: 'Upanga, Dar es Salaam',
          landmark: 'PSPF Towers Residential',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 12000,
        ),
        createdAt: now.subtract(const Duration(minutes: 20)),
        updatedAt: now.subtract(const Duration(minutes: 20)),
        storeLocation: 'Shoprite Mlimani City',
        items: [
          '2kg Rice (Pishori)',
          '1kg Sugar',
          '500ml Cooking Oil',
          '1 Bread (Blue Band)',
          '6 Eggs',
          '1kg Tomatoes',
          '1kg Onions',
          '500g Meat (Beef)',
        ],
        estimatedCost: 45000,
        maxBudget: 50000,
        allowSubstitutes: true,
        preferredBrands: 'Blue Band bread, Cowboy cooking oil',
        customerPhone: '+255756123456',
      ),
      
      BuyForMeRequest(
        id: 'buy_002',
        customerId: 'customer_007',
        description: 'Buy medicine from pharmacy',
        pickupLocation: LocationPoint(
          latitude: -6.7924,
          longitude: 39.2083,
          address: 'Masaki Peninsula, Dar es Salaam',
          landmark: 'Alpha Pharmacy Masaki',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.7500,
          longitude: 39.2800,
          address: 'Mbezi Beach, Dar es Salaam',
          landmark: 'Mbezi Beach Residential Area',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 8000,
          advance: 4000,
        ),
        createdAt: now.subtract(const Duration(minutes: 45)),
        updatedAt: now.subtract(const Duration(minutes: 45)),
        storeLocation: 'Alpha Pharmacy, Masaki',
        items: [
          'Panadol Extra (1 box)',
          'Amoxil 500mg (1 box)',
          'ORS Sachets (6 pieces)',
          'Betadine (1 bottle)',
        ],
        estimatedCost: 25000,
        maxBudget: 30000,
        allowSubstitutes: false,
        notes: 'Please bring receipt and change',
        customerPhone: '+255712987654',
      ),
      
      BuyForMeRequest(
        id: 'buy_003',
        customerId: 'customer_008',
        description: 'Buy office supplies from bookshop',
        pickupLocation: LocationPoint(
          latitude: -6.8235,
          longitude: 39.2695,
          address: 'Kariakoo Market, Dar es Salaam',
          landmark: 'Text Book Centre',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.7833,
          longitude: 39.2667,
          address: 'City Centre, Dar es Salaam',
          landmark: 'PPF Tower, 12th Floor',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 15000,
        ),
        createdAt: now.subtract(const Duration(hours: 1, minutes: 30)),
        updatedAt: now.subtract(const Duration(hours: 1, minutes: 30)),
        storeLocation: 'Text Book Centre, Kariakoo',
        items: [
          'A4 Paper (2 reams)',
          'Black Pens (10 pieces)',
          'Blue Pens (10 pieces)',
          'Stapler (1 piece)',
          'Staple Pins (2 boxes)',
          'Manila Files (10 pieces)',
          'Sticky Notes (5 pads)',
        ],
        estimatedCost: 35000,
        maxBudget: 40000,
        allowSubstitutes: true,
        customerPhone: '+255687654321',
      ),
      
      BuyForMeRequest(
        id: 'buy_004',
        customerId: 'customer_009',
        description: 'Buy baby items from supermarket',
        pickupLocation: LocationPoint(
          latitude: -6.7731,
          longitude: 39.2447,
          address: 'Ilala District, Dar es Salaam',
          landmark: 'Game Stores Nyerere Road',
        ),
        dropoffLocation: LocationPoint(
          latitude: -6.6928,
          longitude: 39.2156,
          address: 'Mikocheni, Dar es Salaam',
          landmark: 'Mikocheni B, House No. 123',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 18000,
        ),
        createdAt: now.subtract(const Duration(minutes: 10)),
        updatedAt: now.subtract(const Duration(minutes: 10)),
        storeLocation: 'Game Stores Nyerere Road',
        items: [
          'Pampers Size 3 (1 pack)',
          'Baby Formula (2 tins)',
          'Baby Wipes (3 packs)',
          'Baby Lotion (1 bottle)',
          'Feeding Bottles (2 pieces)',
        ],
        estimatedCost: 85000,
        maxBudget: 100000,
        allowSubstitutes: true,
        preferredBrands: 'Pampers, Nan Formula, Johnsons Baby',
        notes: 'Please check expiry dates on formula',
        customerPhone: '+255765432190',
      ),
    ];
  }
  
  static List<TechnicianRequest> getSampleTechnicianRequests() {
    final now = DateTime.now();
    
    return [
      TechnicianRequest(
        id: 'tech_001',
        customerId: 'customer_010',
        description: 'Fix electrical wiring issues in kitchen',
        pickupLocation: LocationPoint(
          latitude: -6.7833,
          longitude: 39.2667,
          address: 'Upanga West, Dar es Salaam',
          landmark: 'Near Shoppers Plaza',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 45000,
        ),
        createdAt: now.subtract(const Duration(minutes: 25)),
        updatedAt: now.subtract(const Duration(minutes: 25)),
        category: TechnicianCategory.electrician,
        skillRequired: 'Domestic electrical installation',
        hourlyRate: 15000,
        estimatedHours: 3,
        isUrgent: true,
        requiredTools: ['Electrical tester', 'Wire strippers', 'Screwdrivers', 'Electrical tape'],
        customerPhone: '+255712345678',
        notes: 'Power keeps tripping in kitchen area. Kitchen outlets not working.',
      ),
      
      TechnicianRequest(
        id: 'tech_002',
        customerId: 'customer_011',
        description: 'Repair water pump and fix piping',
        pickupLocation: LocationPoint(
          latitude: -6.7500,
          longitude: 39.2800,
          address: 'Mbezi Beach, Dar es Salaam',
          landmark: 'Mbezi Beach Estate Block D',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 65000,
        ),
        createdAt: now.subtract(const Duration(minutes: 40)),
        updatedAt: now.subtract(const Duration(minutes: 40)),
        category: TechnicianCategory.plumber,
        skillRequired: 'Water pump repair and plumbing',
        fixedRate: 65000,
        estimatedHours: 4,
        isUrgent: false,
        requiredTools: ['Pipe wrench', 'Plumbing tools', 'Sealants', 'Pipe fittings'],
        customerPhone: '+255787654321',
        notes: 'Water pump making noise and low pressure in upstairs bathroom.',
      ),
      
      TechnicianRequest(
        id: 'tech_003',
        customerId: 'customer_012',
        description: 'Service and repair air conditioning unit',
        pickupLocation: LocationPoint(
          latitude: -6.7924,
          longitude: 39.2083,
          address: 'Masaki Peninsula, Dar es Salaam',
          landmark: 'Masaki Towers Apartment',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 80000,
        ),
        createdAt: now.subtract(const Duration(hours: 1)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        category: TechnicianCategory.hvac,
        skillRequired: 'AC repair and servicing',
        hourlyRate: 20000,
        estimatedHours: 4,
        isUrgent: true,
        requiredTools: ['AC tools', 'Refrigerant', 'Gauges', 'Vacuum pump'],
        customerPhone: '+255723456789',
        notes: 'AC not cooling properly. Suspected refrigerant leak.',
      ),
      
      TechnicianRequest(
        id: 'tech_004',
        customerId: 'customer_013',
        description: 'Repair laptop and install software',
        pickupLocation: LocationPoint(
          latitude: -6.8175,
          longitude: 39.2860,
          address: 'Kinondoni District, Dar es Salaam',
          landmark: 'University of Dar es Salaam Campus',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 35000,
        ),
        createdAt: now.subtract(const Duration(minutes: 55)),
        updatedAt: now.subtract(const Duration(minutes: 55)),
        category: TechnicianCategory.computerRepair,
        skillRequired: 'Computer hardware and software repair',
        fixedRate: 35000,
        estimatedHours: 2,
        isUrgent: false,
        requiredTools: ['Laptop repair kit', 'Software CDs', 'External drive'],
        customerPhone: '+255765432109',
        notes: 'Laptop not booting up. Need OS reinstallation and data recovery.',
      ),
      
      TechnicianRequest(
        id: 'tech_005',
        customerId: 'customer_014',
        description: 'Build custom wooden shelves',
        pickupLocation: LocationPoint(
          latitude: -6.6928,
          longitude: 39.2156,
          address: 'Mikocheni A, Dar es Salaam',
          landmark: 'Near Mikocheni Primary School',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 120000,
        ),
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        category: TechnicianCategory.carpenter,
        skillRequired: 'Custom furniture making',
        hourlyRate: 12000,
        estimatedHours: 10,
        isUrgent: false,
        requiredTools: ['Carpentry tools', 'Wood working equipment', 'Measuring tools'],
        customerPhone: '+255654321098',
        notes: 'Need 3 floating shelves for living room. Customer will provide wood.',
        scheduledAt: now.add(const Duration(days: 2)),
      ),
      
      TechnicianRequest(
        id: 'tech_006',
        customerId: 'customer_015',
        description: 'Deep cleaning service for office',
        pickupLocation: LocationPoint(
          latitude: -6.7833,
          longitude: 39.2667,
          address: 'City Centre, Dar es Salaam',
          landmark: 'NIC Life House, 3rd Floor',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.mobileMoney,
          amount: 55000,
        ),
        createdAt: now.subtract(const Duration(minutes: 35)),
        updatedAt: now.subtract(const Duration(minutes: 35)),
        category: TechnicianCategory.cleaner,
        skillRequired: 'Professional office cleaning',
        fixedRate: 55000,
        estimatedHours: 6,
        isUrgent: false,
        requiredTools: ['Cleaning supplies', 'Vacuum cleaner', 'Floor polish'],
        customerPhone: '+255789012345',
        notes: 'Office space needs deep cleaning including windows and carpets.',
        scheduledAt: now.add(const Duration(hours: 24)),
      ),
      
      TechnicianRequest(
        id: 'tech_007',
        customerId: 'customer_016',
        description: 'Repair broken smartphone screen',
        pickupLocation: LocationPoint(
          latitude: -6.8235,
          longitude: 39.2695,
          address: 'Kariakoo Market, Dar es Salaam',
          landmark: 'Near Kariakoo Market main gate',
        ),
        payment: PaymentInfo(
          method: PaymentMethod.cash,
          amount: 45000,
        ),
        createdAt: now.subtract(const Duration(minutes: 15)),
        updatedAt: now.subtract(const Duration(minutes: 15)),
        category: TechnicianCategory.phoneRepair,
        skillRequired: 'Smartphone screen replacement',
        fixedRate: 45000,
        estimatedHours: 1,
        isUrgent: true,
        requiredTools: ['Phone repair kit', 'Replacement screen', 'Heat gun'],
        customerPhone: '+255756789012',
        notes: 'Samsung Galaxy A54 - cracked screen, touch working fine.',
      ),
    ];
  }
  
  static List<BaseRequest> getAllSampleRequests() {
    return [
      ...getSampleDeliveryRequests(),
      ...getSampleBuyForMeRequests(),
      ...getSampleTechnicianRequests(),
    ];
  }
  
  // Helper method to get requests by status
  static List<BaseRequest> getRequestsByStatus(RequestStatus status) {
    return getAllSampleRequests()
        .where((request) => request.status == status)
        .toList();
  }
  
  // Helper method to get requests by type
  static List<BaseRequest> getRequestsByType(RequestType type) {
    return getAllSampleRequests()
        .where((request) => request.type == type)
        .toList();
  }
  
  // Helper method to get urgent requests
  static List<BaseRequest> getUrgentRequests() {
    return getAllSampleRequests().where((request) {
      if (request is DeliveryRequest) {
        return request.priority == DeliveryPriority.urgent;
      } else if (request is TechnicianRequest) {
        return request.isUrgent;
      }
      return false;
    }).toList();
  }
  
  // Helper method to get requests within a price range
  static List<BaseRequest> getRequestsByPriceRange(double minPrice, double maxPrice) {
    return getAllSampleRequests()
        .where((request) => 
            request.payment.amount >= minPrice && 
            request.payment.amount <= maxPrice)
        .toList();
  }
}