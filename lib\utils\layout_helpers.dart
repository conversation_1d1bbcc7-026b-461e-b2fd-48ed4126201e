import 'package:flutter/material.dart';

/// Layout utility helpers to prevent overflow issues across the app
class LayoutHelpers {
  /// Creates a responsive scaffold body with SafeArea and SingleChildScrollView
  /// to prevent overflow issues on different screen sizes
  static Widget responsiveScaffoldBody({
    required Widget child,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    bool addBottomPadding = true,
  }) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            child,
            if (addBottomPadding) const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// Creates a dropdown with consistent overflow-safe properties
  static DropdownButtonFormField<T> safeDropdown<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
    required InputDecoration decoration,
    double? menuMaxHeight = 250,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: decoration,
      isExpanded: true,
      menuMaxHeight: menuMaxHeight,
      items: items.map((item) {
        // Wrap the child in a Flexible widget to prevent overflow
        return DropdownMenuItem<T>(
          value: item.value,
          child: item.child is Text
              ? Text(
                  (item.child as Text).data ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: (item.child as Text).style,
                )
              : Flexible(child: item.child),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  /// Standard form field decoration that works well across different screen sizes
  static InputDecoration standardFieldDecoration({
    required String labelText,
    String? hintText,
    String? helperText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    BuildContext? context,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      filled: context != null,
      fillColor: context?.let((ctx) => Theme.of(ctx).colorScheme.surface),
    );
  }

  /// Creates a container with consistent styling and responsive behavior
  static Widget responsiveContainer({
    required Widget child,
    Color? color,
    EdgeInsetsGeometry? padding = const EdgeInsets.all(16),
    EdgeInsetsGeometry? margin,
    double? borderRadius = 12,
    Color? borderColor,
    BuildContext? context,
  }) {
    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: color,
        borderRadius: borderRadius != null ? BorderRadius.circular(borderRadius) : null,
        border: borderColor != null ? Border.all(color: borderColor) : null,
      ),
      child: child,
    );
  }
}

/// Extension to add convenience methods to BuildContext
extension ContextExtensions on BuildContext {
  R? let<R>(R? Function(BuildContext) operation) => operation(this);
}