import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/services/maps_service.dart';

class LocationPickerScreen extends StatefulWidget {
  final String title;
  final LocationPoint? initialLocation;
  final bool showSearchBar;

  const LocationPickerScreen({
    super.key,
    required this.title,
    this.initialLocation,
    this.showSearchBar = true,
  });

  @override
  State<LocationPickerScreen> createState() => _LocationPickerScreenState();
}

class _LocationPickerScreenState extends State<LocationPickerScreen> {
  GoogleMapController? _controller;
  final MapsService _mapsService = MapsService.instance;
  final TextEditingController _searchController = TextEditingController();

  LatLng? _selectedLocation;
  String? _selectedAddress;
  bool _isLoading = false;
  bool _isSearching = false;
  MapType _currentMapType = MapType.normal;
  bool _showMapTypePicker = false;
  List<String> _searchSuggestions = [];
  bool _showSuggestions = false;

  Set<Marker> _markers = {};
  
  CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(-6.7924, 39.2083), // Default to Dar es Salaam, Tanzania
    zoom: 12,
  );

  @override
  void initState() {
    super.initState();
    _initializeLocation();
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    if (_searchController.text.isNotEmpty && _searchController.text.length > 2) {
      _generateSearchSuggestions(_searchController.text);
    } else {
      setState(() {
        _showSuggestions = false;
        _searchSuggestions.clear();
      });
    }
  }

  void _generateSearchSuggestions(String query) {
    // Basic suggestions - in a real app, you'd use Google Places API
    final suggestions = [
      '$query - Current Location',
      '$query Street, Dar es Salaam',
      '$query Avenue, Dar es Salaam',
      '$query Road, Mwanza',
      '$query Street, Arusha',
    ];
    
    setState(() {
      _searchSuggestions = suggestions.take(5).toList();
      _showSuggestions = true;
    });
  }

  void _initializeLocation() async {
    if (widget.initialLocation != null) {
      setState(() {
        _selectedLocation = LatLng(
          widget.initialLocation!.latitude,
          widget.initialLocation!.longitude,
        );
        _selectedAddress = widget.initialLocation!.address;
        _initialCameraPosition = CameraPosition(
          target: _selectedLocation!,
          zoom: 15,
        );
      });
    } else {
      // Try to get current location
      final currentLocation = await _mapsService.getCurrentLatLng();
      if (currentLocation != null) {
        final address = await _mapsService.getAddressFromCoordinates(
          currentLocation.latitude,
          currentLocation.longitude,
        );
        
        setState(() {
          _selectedLocation = currentLocation;
          _selectedAddress = address;
          _initialCameraPosition = CameraPosition(
            target: currentLocation,
            zoom: 15,
          );
        });
      }
    }
    
    _updateMarker();
    
    // Move camera to the new position if controller is available
    if (_controller != null && _selectedLocation != null) {
      await _mapsService.moveCamera(_selectedLocation!);
    }
  }

  void _updateMarker() {
    if (_selectedLocation != null) {
      setState(() {
        _markers = {
          Marker(
            markerId: const MarkerId('selected_location'),
            position: _selectedLocation!,
            infoWindow: InfoWindow(
              title: widget.title,
              snippet: _selectedAddress ?? 'Selected location',
            ),
            icon: BitmapDescriptor.defaultMarkerWithHue(
              BitmapDescriptor.hueRed,
            ),
          ),
        };
      });
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _controller = controller;
    _mapsService.setController(controller);
  }

  void _onMapTap(LatLng location) async {
    setState(() {
      _selectedLocation = location;
      _isLoading = true;
      _showSuggestions = false; // Hide suggestions when tapping map
    });

    // Get address for the selected location
    final address = await _mapsService.getAddressFromCoordinates(
      location.latitude,
      location.longitude,
    );

    setState(() {
      _selectedAddress = address;
      _isLoading = false;
    });

    _updateMarker();
  }

  void _onSearchSubmitted(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    final coordinates = await _mapsService.getCoordinatesFromAddress(query);
    if (coordinates != null) {
      setState(() {
        _selectedLocation = coordinates;
        _selectedAddress = query;
        _isSearching = false;
      });

      _updateMarker();
      await _mapsService.moveCamera(coordinates);
    } else {
      setState(() {
        _isSearching = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location not found. Please try a different search.'),
          ),
        );
      }
    }
  }

  void _goToCurrentLocation() async {
    final currentLocation = await _mapsService.getCurrentLatLng();
    if (currentLocation != null) {
      setState(() {
        _selectedLocation = currentLocation;
        _isLoading = true;
      });

      final address = await _mapsService.getAddressFromCoordinates(
        currentLocation.latitude,
        currentLocation.longitude,
      );

      setState(() {
        _selectedAddress = address;
        _isLoading = false;
      });

      _updateMarker();
      await _mapsService.moveCamera(currentLocation);
    }
  }

  void _toggleMapTypePicker() {
    setState(() {
      _showMapTypePicker = !_showMapTypePicker;
    });
  }

  void _changeMapType(MapType mapType) {
    setState(() {
      _currentMapType = mapType;
      _showMapTypePicker = false;
    });
  }

  Widget _buildMapTypeOption(String label, MapType mapType, IconData icon) {
    final isSelected = _currentMapType == mapType;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _changeMapType(mapType),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmLocation() {
    if (_selectedLocation != null) {
      final locationPoint = LocationPoint(
        latitude: _selectedLocation!.latitude,
        longitude: _selectedLocation!.longitude,
        address: _selectedAddress,
      );
      Navigator.of(context).pop(locationPoint);
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
      ),
      body: GestureDetector(
        onTap: () {
          if (_showMapTypePicker) {
            setState(() {
              _showMapTypePicker = false;
            });
          }
          if (_showSuggestions) {
            setState(() {
              _showSuggestions = false;
            });
          }
        },
        child: Stack(
          children: [
            GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: _initialCameraPosition,
            markers: _markers,
            onTap: _onMapTap,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            mapType: _currentMapType,
            zoomControlsEnabled: false,
            compassEnabled: true,
            rotateGesturesEnabled: true,
            scrollGesturesEnabled: true,
            tiltGesturesEnabled: true,
            zoomGesturesEnabled: true,
            mapToolbarEnabled: true,
            buildingsEnabled: true,
            trafficEnabled: false,
          ),
          
          // Search bar
          if (widget.showSearchBar)
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Column(
                children: [
                  Card(
                    elevation: 4,
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search for a location...',
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        prefixIcon: _isSearching 
                            ? const Padding(
                                padding: EdgeInsets.all(16),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                            : const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _showSuggestions = false;
                                    _searchSuggestions.clear();
                                  });
                                },
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                      ),
                      onSubmitted: (value) {
                        _onSearchSubmitted(value);
                        setState(() {
                          _showSuggestions = false;
                        });
                      },
                    ),
                  ),
                  
                  // Search suggestions
                  if (_showSuggestions && _searchSuggestions.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      child: Card(
                        elevation: 4,
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _searchSuggestions.length,
                          itemBuilder: (context, index) {
                            final suggestion = _searchSuggestions[index];
                            return ListTile(
                              leading: const Icon(Icons.location_on, size: 20),
                              title: Text(
                                suggestion,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              onTap: () {
                                _searchController.text = suggestion;
                                _onSearchSubmitted(suggestion);
                                setState(() {
                                  _showSuggestions = false;
                                });
                              },
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),
            ),

          // Current location and map type buttons
          Positioned(
            bottom: 180,
            right: 16,
            child: Column(
              children: [
                // Map type button
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: _toggleMapTypePicker,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Icon(
                          Icons.layers,
                          color: Theme.of(context).colorScheme.onSurface,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                // Current location button
                FloatingActionButton(
                  heroTag: 'current_location',
                  onPressed: _goToCurrentLocation,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  child: const Icon(Icons.my_location),
                ),
              ],
            ),
          ),

          // Map type picker
          if (_showMapTypePicker)
            Positioned(
              bottom: 280,
              right: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildMapTypeOption('Normal', MapType.normal, Icons.map),
                    _buildMapTypeOption('Satellite', MapType.satellite, Icons.satellite_alt),
                    _buildMapTypeOption('Hybrid', MapType.hybrid, Icons.layers),
                    _buildMapTypeOption('Terrain', MapType.terrain, Icons.terrain),
                  ],
                ),
              ),
            ),

          // Location details card
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Card(
              margin: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    Text(
                      'Selected Location',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    if (_isLoading)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Getting address...'),
                        ],
                      )
                    else if (_selectedAddress != null)
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _selectedAddress!,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      )
                    else
                      Text(
                        'Tap on the map to select a location',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    
                    const SizedBox(height: 16),
                    
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _selectedLocation != null ? _confirmLocation : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Theme.of(context).colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Confirm Location'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          ],
        ),
      ),
    );
  }
}