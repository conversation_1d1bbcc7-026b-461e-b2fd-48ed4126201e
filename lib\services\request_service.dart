import 'dart:math' as math;
import 'package:mlink/models/request.dart';
import 'package:mlink/supabase/supabase_config.dart';

class RequestService {
  static final RequestService _instance = RequestService._internal();
  factory RequestService() => _instance;
  RequestService._internal();

  static RequestService get instance => _instance;

  Future<List<BaseRequest>> getUserRequests(String userId) async {
    try {
      final response = await SupabaseConfig.getUserRequests(userId);
      return response.map((data) => _parseRequest(data)).toList();
    } catch (e) {
      print('Failed to get user requests from backend: $e');
      // Return empty list for now - in production this would be handled differently
      return [];
    }
  }

  Future<List<BaseRequest>> getAvailableRequests(RequestType type) async {
    try {
      final response = await SupabaseConfig.getAvailableRequests(type.name);
      return response.map((data) => _parseRequest(data)).toList();
    } catch (e) {
      print('Failed to get available requests from backend: $e');
      // Return empty list instead of sample data - backend should be the single source of truth
      return [];
    }
  }

  Future<BaseRequest?> getRequestDetails(String requestId) async {
    try {
      final data = await SupabaseConfig.getRequestDetails(requestId);
      if (data == null) return null;
      return _parseRequest(data);
    } catch (e) {
      throw Exception('Failed to get request details: $e');
    }
  }

  Future<String> createRequest(BaseRequest request) async {
    try {
      final response = await SupabaseConfig.client
          .from('requests')
          .insert(request.toJson())
          .select()
          .single();
      
      final requestId = response['id'];
      
      // Create specific request type data
      await _createSpecificRequestData(request, requestId);
      
      return requestId;
    } catch (e) {
      throw Exception('Failed to create request: $e');
    }
  }

  Future<void> updateRequestStatus(String requestId, RequestStatus status, String updatedBy) async {
    try {
      await SupabaseConfig.updateRequestStatus(requestId, status.name, updatedBy);
    } catch (e) {
      print('Failed to update request status in backend: $e');
      throw Exception('Failed to update request status: $e');
    }
  }

  Future<void> assignRequest(String requestId, String assigneeId, RequestType type) async {
    try {
      Map<String, dynamic> updateData = {
        'status': 'accepted',
        'updated_at': DateTime.now().toIso8601String(),
      };

      await SupabaseConfig.client
          .from('requests')
          .update(updateData)
          .eq('id', requestId);

      // Update specific request type with assignee
      await _updateSpecificRequestAssignee(requestId, assigneeId, type);
    } catch (e) {
      print('Failed to assign request in backend: $e');
      throw Exception('Failed to assign request: $e');
    }
  }

  Future<void> updateRequest(BaseRequest request) async {
    try {
      await SupabaseConfig.client
          .from('requests')
          .update(request.toJson())
          .eq('id', request.id);
      
      // Update specific request type data
      await _updateSpecificRequestData(request);
    } catch (e) {
      throw Exception('Failed to update request: $e');
    }
  }

  Future<void> deleteRequest(String requestId) async {
    try {
      await SupabaseConfig.client
          .from('requests')
          .delete()
          .eq('id', requestId);
    } catch (e) {
      throw Exception('Failed to delete request: $e');
    }
  }

  Future<List<BaseRequest>> getRequestsByStatus(RequestStatus status) async {
    try {
      final response = await SupabaseConfig.client
          .from('requests')
          .select('*, users!customer_id(full_name, phone_number)')
          .eq('status', status.name)
          .order('created_at', ascending: false);
      
      return response.map((data) => _parseRequest(data)).toList();
    } catch (e) {
      throw Exception('Failed to get requests by status: $e');
    }
  }

  Future<List<BaseRequest>> getRequestsByLocation(double latitude, double longitude, double radiusKm) async {
    try {
      // This is a simplified approach - in production you'd want to use PostGIS or similar
      final response = await SupabaseConfig.client
          .from('requests')
          .select('*, users!customer_id(full_name, phone_number)')
          .eq('status', 'pending')
          .order('created_at', ascending: false);
      
      return response
          .map((data) => _parseRequest(data))
          .where((request) => _isWithinRadius(request.pickupLocation, latitude, longitude, radiusKm))
          .toList();
    } catch (e) {
      throw Exception('Failed to get requests by location: $e');
    }
  }

  BaseRequest _parseRequest(Map<String, dynamic> data) {
    final type = RequestType.values.firstWhere(
      (t) => t.name == data['type'],
      orElse: () => RequestType.delivery,
    );

    switch (type) {
      case RequestType.delivery:
        return DeliveryRequest.fromJson(data);
      case RequestType.buyForMe:
        return BuyForMeRequest.fromJson(data);
      case RequestType.technicianService:
        return TechnicianRequest.fromJson(data);
    }
  }

  Future<void> _createSpecificRequestData(BaseRequest request, String requestId) async {
    switch (request.type) {
      case RequestType.delivery:
        final deliveryRequest = request as DeliveryRequest;
        await SupabaseConfig.client.from('delivery_requests').insert({
          'request_id': requestId,
          'preferred_vehicle': deliveryRequest.preferredVehicle.name,
          'priority': deliveryRequest.priority.name,
          'estimated_weight': deliveryRequest.estimatedWeight,
          'package_type': deliveryRequest.packageType,
          'requires_signature': deliveryRequest.requiresSignature,
          'recipient_name': deliveryRequest.recipientName,
          'recipient_phone': deliveryRequest.recipientPhone,
        });
        break;
      case RequestType.buyForMe:
        final buyForMeRequest = request as BuyForMeRequest;
        await SupabaseConfig.client.from('buy_for_me_requests').insert({
          'request_id': requestId,
          'store_location': buyForMeRequest.storeLocation,
          'items': buyForMeRequest.items,
          'estimated_cost': buyForMeRequest.estimatedCost,
          'max_budget': buyForMeRequest.maxBudget,
          'preferred_brands': buyForMeRequest.preferredBrands,
          'allow_substitutes': buyForMeRequest.allowSubstitutes,
        });
        break;
      case RequestType.technicianService:
        final technicianRequest = request as TechnicianRequest;
        await SupabaseConfig.client.from('technician_requests').insert({
          'request_id': requestId,
          'category': technicianRequest.category.name,
          'skill_required': technicianRequest.skillRequired,
          'hourly_rate': technicianRequest.hourlyRate,
          'fixed_rate': technicianRequest.fixedRate,
          'estimated_hours': technicianRequest.estimatedHours,
          'is_urgent': technicianRequest.isUrgent,
          'required_tools': technicianRequest.requiredTools,
        });
        break;
    }
  }

  Future<void> _updateSpecificRequestAssignee(String requestId, String assigneeId, RequestType type) async {
    switch (type) {
      case RequestType.delivery:
        await SupabaseConfig.client
            .from('delivery_requests')
            .update({'agent_id': assigneeId})
            .eq('request_id', requestId);
        break;
      case RequestType.buyForMe:
        await SupabaseConfig.client
            .from('buy_for_me_requests')
            .update({'agent_id': assigneeId})
            .eq('request_id', requestId);
        break;
      case RequestType.technicianService:
        await SupabaseConfig.client
            .from('technician_requests')
            .update({'technician_id': assigneeId})
            .eq('request_id', requestId);
        break;
    }
  }

  Future<void> _updateSpecificRequestData(BaseRequest request) async {
    switch (request.type) {
      case RequestType.delivery:
        final deliveryRequest = request as DeliveryRequest;
        await SupabaseConfig.client.from('delivery_requests').update({
          'preferred_vehicle': deliveryRequest.preferredVehicle.name,
          'priority': deliveryRequest.priority.name,
          'estimated_weight': deliveryRequest.estimatedWeight,
          'package_type': deliveryRequest.packageType,
          'requires_signature': deliveryRequest.requiresSignature,
          'recipient_name': deliveryRequest.recipientName,
          'recipient_phone': deliveryRequest.recipientPhone,
        }).eq('request_id', request.id);
        break;
      case RequestType.buyForMe:
        final buyForMeRequest = request as BuyForMeRequest;
        await SupabaseConfig.client.from('buy_for_me_requests').update({
          'store_location': buyForMeRequest.storeLocation,
          'items': buyForMeRequest.items,
          'estimated_cost': buyForMeRequest.estimatedCost,
          'max_budget': buyForMeRequest.maxBudget,
          'preferred_brands': buyForMeRequest.preferredBrands,
          'allow_substitutes': buyForMeRequest.allowSubstitutes,
        }).eq('request_id', request.id);
        break;
      case RequestType.technicianService:
        final technicianRequest = request as TechnicianRequest;
        await SupabaseConfig.client.from('technician_requests').update({
          'category': technicianRequest.category.name,
          'skill_required': technicianRequest.skillRequired,
          'hourly_rate': technicianRequest.hourlyRate,
          'fixed_rate': technicianRequest.fixedRate,
          'estimated_hours': technicianRequest.estimatedHours,
          'is_urgent': technicianRequest.isUrgent,
          'required_tools': technicianRequest.requiredTools,
        }).eq('request_id', request.id);
        break;
    }
  }

  bool _isWithinRadius(LocationPoint location, double centerLat, double centerLng, double radiusKm) {
    // Simplified distance calculation - in production use a proper geospatial library
    const double earthRadius = 6371; // km
    double dLat = _degreesToRadians(location.latitude - centerLat);
    double dLng = _degreesToRadians(location.longitude - centerLng);
    double a = math.sin(dLat/2) * math.sin(dLat/2) +
        math.cos(_degreesToRadians(centerLat)) * math.cos(_degreesToRadians(location.latitude)) *
        math.sin(dLng/2) * math.sin(dLng/2);
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a));
    double distance = earthRadius * c;
    return distance <= radiusKm;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

}