import '../models/service_rates.dart';
import '../supabase/lookup_service.dart';

/// Service for managing configuration data with Supa<PERSON> as primary source
/// and hardcoded data as fallback
class ConfigurationService {
  static bool _useSupabase = true;

  // VEHICLE TYPES

  /// Get vehicle type display name
  static Future<String> getVehicleDisplayName(String vehicleCode) async {
    if (_useSupabase) {
      try {
        final vehicleType = await LookupService.getVehicleTypeByCode(vehicleCode);
        if (vehicleType != null) {
          final icon = vehicleType['icon'] ?? '';
          final name = vehicleType['name'] ?? vehicleCode;
          return '$icon $name';
        }
      } catch (e) {
        print('ConfigurationService: Error getting vehicle type: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedVehicleDisplayName(vehicleCode);
  }

  /// Get vehicle type description
  static Future<String> getVehicleDescription(String vehicleCode) async {
    if (_useSupabase) {
      try {
        final vehicleType = await LookupService.getVehicleTypeByCode(vehicleCode);
        if (vehicleType != null) {
          return vehicleType['description'] ?? '';
        }
      } catch (e) {
        print('ConfigurationService: Error getting vehicle description: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedVehicleDescription(vehicleCode);
  }

  /// Get all vehicle types for dropdowns
  static Future<List<Map<String, dynamic>>> getVehicleTypes() async {
    if (_useSupabase) {
      try {
        return await LookupService.getActiveVehicleTypes();
      } catch (e) {
        print('ConfigurationService: Error getting vehicle types: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedVehicleTypes();
  }

  // TECHNICIAN CATEGORIES

  /// Get technician category display name
  static Future<String> getTechnicianCategoryDisplayName(String categoryCode) async {
    if (_useSupabase) {
      try {
        final category = await LookupService.getTechnicianCategoryByCode(categoryCode);
        if (category != null) {
          final icon = category['icon'] ?? '';
          final name = category['name'] ?? categoryCode;
          return '$icon $name';
        }
      } catch (e) {
        print('ConfigurationService: Error getting technician category: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedTechnicianDisplayName(categoryCode);
  }

  /// Get all technician categories for dropdowns
  static Future<List<Map<String, dynamic>>> getTechnicianCategories() async {
    if (_useSupabase) {
      try {
        return await LookupService.getActiveTechnicianCategories();
      } catch (e) {
        print('ConfigurationService: Error getting technician categories: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedTechnicianCategories();
  }

  // PAYMENT METHODS

  /// Get payment method display name
  static Future<String> getPaymentMethodDisplayName(String methodCode) async {
    if (_useSupabase) {
      try {
        final method = await LookupService.getPaymentMethodByCode(methodCode);
        if (method != null) {
          final icon = method['icon'] ?? '';
          final name = method['name'] ?? methodCode;
          return '$icon $name';
        }
      } catch (e) {
        print('ConfigurationService: Error getting payment method: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedPaymentDisplayName(methodCode);
  }

  /// Get all payment methods for dropdowns
  static Future<List<Map<String, dynamic>>> getPaymentMethods() async {
    if (_useSupabase) {
      try {
        return await LookupService.getActivePaymentMethods();
      } catch (e) {
        print('ConfigurationService: Error getting payment methods: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedPaymentMethods();
  }

  // DELIVERY PRIORITIES

  /// Get delivery priority description
  static Future<String> getDeliveryPriorityDescription(String priorityCode) async {
    if (_useSupabase) {
      try {
        final priority = await LookupService.getDeliveryPriorityByCode(priorityCode);
        if (priority != null) {
          return priority['description'] ?? '';
        }
      } catch (e) {
        print('ConfigurationService: Error getting delivery priority: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedPriorityDescription(priorityCode);
  }

  /// Get all delivery priorities for dropdowns
  static Future<List<Map<String, dynamic>>> getDeliveryPriorities() async {
    if (_useSupabase) {
      try {
        return await LookupService.getActiveDeliveryPriorities();
      } catch (e) {
        print('ConfigurationService: Error getting delivery priorities: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedDeliveryPriorities();
  }

  // REQUEST STATUSES

  /// Get request status display info
  static Future<Map<String, dynamic>> getRequestStatusInfo(String statusCode) async {
    if (_useSupabase) {
      try {
        final status = await LookupService.getRequestStatusByCode(statusCode);
        if (status != null) {
          return {
            'name': status['name'],
            'color_hex': status['color_hex'],
            'icon': status['icon'],
          };
        }
      } catch (e) {
        print('ConfigurationService: Error getting request status: $e');
      }
    }

    // Hardcoded fallback
    return _getHardcodedStatusInfo(statusCode);
  }

  // SERVICE RATES

  /// Get hourly rates
  static Future<List<HourlyRate>> getCommonHourlyRates() async {
    if (_useSupabase) {
      try {
        return await LookupService.getCommonHourlyRates();
      } catch (e) {
        print('ConfigurationService: Error getting hourly rates: $e');
      }
    }

    // Hardcoded fallback
    return ServiceRates.commonHourlyRates;
  }

  /// Get delivery rates
  static Future<Map<String, double>> getDeliveryRates() async {
    if (_useSupabase) {
      try {
        final rates = await LookupService.getDeliveryRates();
        return {
          'base_cost': rates['base_cost'],
          'cost_per_km': rates['cost_per_km'],
        };
      } catch (e) {
        print('ConfigurationService: Error getting delivery rates: $e');
      }
    }

    // Hardcoded fallback
    return {
      'base_cost': DeliveryCostCalculator.baseCost,
      'cost_per_km': DeliveryCostCalculator.costPerKm,
    };
  }

  // HARDCODED FALLBACK METHODS

  static String _getHardcodedVehicleDisplayName(String vehicleCode) {
    switch (vehicleCode) {
      case 'bodaboda': return '🏍️ Bodaboda';
      case 'bicycle': return '🚲 Bicycle';
      case 'bajaji': return '🛺 Bajaji';
      case 'car': return '🚗 Car';
      case 'pickup': return '🛻 Pickup';
      case 'van': return '🚐 Van';
      case 'truck': return '🚚 Truck';
      default: return '🚛 Other';
    }
  }

  static String _getHardcodedVehicleDescription(String vehicleCode) {
    switch (vehicleCode) {
      case 'bodaboda': return 'Small packages, documents';
      case 'bicycle': return 'Light packages, eco-friendly';
      case 'bajaji': return 'Medium packages, quick delivery';
      case 'car': return 'Medium packages, comfortable';
      case 'pickup': return 'Large packages, furniture';
      case 'van': return 'Large packages, bulk items';
      case 'truck': return 'Heavy items, long distance';
      default: return 'Special requirements';
    }
  }

  static List<Map<String, dynamic>> _getHardcodedVehicleTypes() {
    return [
      {'code': 'bodaboda', 'name': 'Bodaboda', 'icon': '🏍️', 'description': 'Small packages, documents'},
      {'code': 'bicycle', 'name': 'Bicycle', 'icon': '🚲', 'description': 'Light packages, eco-friendly'},
      {'code': 'bajaji', 'name': 'Bajaji', 'icon': '🛺', 'description': 'Medium packages, quick delivery'},
      {'code': 'car', 'name': 'Car', 'icon': '🚗', 'description': 'Medium packages, comfortable'},
      {'code': 'pickup', 'name': 'Pickup', 'icon': '🛻', 'description': 'Large packages, furniture'},
      {'code': 'van', 'name': 'Van', 'icon': '🚐', 'description': 'Large packages, bulk items'},
      {'code': 'truck', 'name': 'Truck', 'icon': '🚚', 'description': 'Heavy items, long distance'},
    ];
  }

  static String _getHardcodedTechnicianDisplayName(String categoryCode) {
    switch (categoryCode) {
      case 'electrician': return '⚡ Electrician';
      case 'plumber': return '🔧 Plumber';
      case 'mechanic': return '🔩 Mechanic';
      case 'carpenter': return '🪚 Carpenter';
      case 'painter': return '🎨 Painter';
      case 'cleaner': return '🧹 Cleaner';
      case 'gardener': return '🌱 Gardener';
      case 'applianceRepair': return '🔨 Appliance Repair';
      case 'computerRepair': return '💻 Computer Repair';
      case 'phoneRepair': return '📱 Phone Repair';
      case 'hvac': return '🌡️ HVAC';
      default: return '🛠️ Other';
    }
  }

  static List<Map<String, dynamic>> _getHardcodedTechnicianCategories() {
    return [
      {'code': 'electrician', 'name': 'Electrician', 'icon': '⚡'},
      {'code': 'plumber', 'name': 'Plumber', 'icon': '🔧'},
      {'code': 'mechanic', 'name': 'Mechanic', 'icon': '🔩'},
      {'code': 'carpenter', 'name': 'Carpenter', 'icon': '🪚'},
      {'code': 'painter', 'name': 'Painter', 'icon': '🎨'},
      {'code': 'cleaner', 'name': 'Cleaner', 'icon': '🧹'},
      {'code': 'gardener', 'name': 'Gardener', 'icon': '🌱'},
      {'code': 'applianceRepair', 'name': 'Appliance Repair', 'icon': '🔨'},
      {'code': 'computerRepair', 'name': 'Computer Repair', 'icon': '💻'},
      {'code': 'phoneRepair', 'name': 'Phone Repair', 'icon': '📱'},
      {'code': 'hvac', 'name': 'HVAC', 'icon': '🌡️'},
    ];
  }

  static String _getHardcodedPaymentDisplayName(String methodCode) {
    switch (methodCode) {
      case 'cash': return '💵 Cash on Delivery';
      case 'mobile_money': return '📱 Mobile Money';
      case 'card': return '💳 Card';
      case 'wallet': return '🏦 M-Link Wallet';
      default: return methodCode;
    }
  }

  static List<Map<String, dynamic>> _getHardcodedPaymentMethods() {
    return [
      {'code': 'cash', 'name': 'Cash on Delivery', 'icon': '💵'},
      {'code': 'mobile_money', 'name': 'Mobile Money', 'icon': '📱'},
      {'code': 'card', 'name': 'Card Payment', 'icon': '💳'},
      {'code': 'wallet', 'name': 'M-Link Wallet', 'icon': '🏦'},
    ];
  }

  static String _getHardcodedPriorityDescription(String priorityCode) {
    switch (priorityCode) {
      case 'normal': return 'Standard delivery within the day';
      case 'urgent': return 'Priority delivery - faster service';
      case 'scheduled': return 'Deliver at specific date and time';
      default: return '';
    }
  }

  static List<Map<String, dynamic>> _getHardcodedDeliveryPriorities() {
    return [
      {'code': 'normal', 'name': 'Normal Delivery', 'description': 'Standard delivery within the day'},
      {'code': 'urgent', 'name': 'Urgent Delivery', 'description': 'Priority delivery - faster service'},
      {'code': 'scheduled', 'name': 'Scheduled Delivery', 'description': 'Deliver at specific date and time'},
    ];
  }

  static Map<String, dynamic> _getHardcodedStatusInfo(String statusCode) {
    switch (statusCode) {
      case 'pending': return {'name': 'Pending', 'color_hex': '#FF9800', 'icon': 'pending'};
      case 'accepted': return {'name': 'Accepted', 'color_hex': '#2196F3', 'icon': 'check_circle'};
      case 'in_progress': return {'name': 'In Progress', 'color_hex': '#9C27B0', 'icon': 'work'};
      case 'completed': return {'name': 'Completed', 'color_hex': '#4CAF50', 'icon': 'done'};
      case 'cancelled': return {'name': 'Cancelled', 'color_hex': '#F44336', 'icon': 'cancel'};
      case 'failed': return {'name': 'Failed', 'color_hex': '#FF5722', 'icon': 'error'};
      case 'payment_pending': return {'name': 'Payment Pending', 'color_hex': '#FFC107', 'icon': 'payment'};
      default: return {'name': statusCode, 'color_hex': '#757575', 'icon': 'help'};
    }
  }

  /// Force refresh from Supabase
  static void forceSupabaseRefresh() {
    _useSupabase = true;
  }
}