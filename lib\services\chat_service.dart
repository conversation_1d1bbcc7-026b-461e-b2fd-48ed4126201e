import 'dart:async';
import 'package:mlink/supabase/supabase_config.dart';
import 'package:mlink/models/chat.dart';
import 'package:mlink/models/message.dart';
import 'package:mlink/models/user.dart' as app_user;
import 'package:mlink/models/user.dart' show OnlineStatus;
import 'package:mlink/services/backend_api_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ChatService {
  static final ChatService _instance = ChatService._internal();
  factory ChatService() => _instance;
  ChatService._internal();

  static ChatService get instance => _instance;

  final Map<String, StreamSubscription> _chatSubscriptions = {};
  final Map<String, StreamSubscription> _messageSubscriptions = {};
  final StreamController<List<Chat>> _chatsController = StreamController<List<Chat>>.broadcast();
  final StreamController<Message> _messagesController = StreamController<Message>.broadcast();

  Stream<List<Chat>> get chatsStream => _chatsController.stream;
  Stream<Message> get messagesStream => _messagesController.stream;

  // Get all chats for a user
  Future<List<Chat>> getUserChats(String userId) async {
    try {
      final response = await BackendApiService.getUserChats(userId);
      
      if (response['success'] == true && response['chats'] != null) {
        final chatsData = response['chats'] as List;
        final chats = chatsData.map((data) => Chat.fromJson(data)).toList();
        
        return chats;
      } else {
        throw Exception(response['error'] ?? 'Failed to get user chats');
      }
    } catch (e) {
      throw Exception('Failed to get user chats: $e');
    }
  }

  // Get or create a chat between two users
  Future<Chat> getOrCreateChat(String user1Id, String user2Id, {String? requestId}) async {
    try {
      final chatData = {
        'user1_id': user1Id,
        'user2_id': user2Id,
        if (requestId != null) 'request_id': requestId,
      };
      
      final response = await BackendApiService.createChat(chatData);
      
      if (response['success'] == true && response['chat'] != null) {
        return Chat.fromJson(response['chat']);
      } else {
        throw Exception(response['error'] ?? 'Failed to get or create chat');
      }
    } catch (e) {
      throw Exception('Failed to get or create chat: $e');
    }
  }

  // Send a message
  Future<Message> sendMessage({
    required String chatId,
    required String senderId,
    required String content,
    MessageType type = MessageType.text,
    String? fileUrl,
    String? fileName,
    int? fileSize,
  }) async {
    try {
      final messageData = {
        'chat_id': chatId,
        'sender_id': senderId,
        'content': content,
        'message_type': type.name,
        if (fileUrl != null) 'file_url': fileUrl,
        if (fileName != null) 'file_name': fileName,
        if (fileSize != null) 'file_size': fileSize,
      };

      final response = await BackendApiService.sendMessage(messageData);
      
      if (response['success'] == true && response['message'] != null) {
        final message = Message.fromJson(response['message']);
        _messagesController.add(message);
        return message;
      } else {
        throw Exception(response['error'] ?? 'Failed to send message');
      }
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  // Get messages for a chat
  Future<List<Message>> getChatMessages(String chatId) async {
    try {
      final response = await BackendApiService.getChatMessages(chatId);
      
      if (response['success'] == true && response['messages'] != null) {
        final messagesData = response['messages'] as List;
        return messagesData.map((data) => Message.fromJson(data)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to get chat messages');
      }
    } catch (e) {
      throw Exception('Failed to get chat messages: $e');
    }
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      final response = await BackendApiService.markMessagesAsRead(chatId, userId);
      
      if (response['success'] != true) {
        throw Exception(response['error'] ?? 'Failed to mark messages as read');
      }
    } catch (e) {
      throw Exception('Failed to mark messages as read: $e');
    }
  }

  // Get unread message count for a chat
  Future<int> getUnreadMessageCount(String chatId, String userId) async {
    try {
      final response = await SupabaseConfig.client
          .from('messages')
          .select('id')
          .eq('chat_id', chatId)
          .eq('is_read', false)
          .neq('sender_id', userId);

      return response.length;
    } catch (e) {
      return 0;
    }
  }

  // Get total unread message count for a user
  Future<int> getTotalUnreadMessageCount(String userId) async {
    try {
      final response = await SupabaseConfig.client
          .rpc('get_unread_message_count', params: {'user_id': userId});
      return response as int? ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // Update user status
  Future<void> updateUserStatus(String userId, OnlineStatus status) async {
    try {
      final response = await BackendApiService.updateUserStatus(userId, status.name);
      
      if (response['success'] != true) {
        throw Exception(response['error'] ?? 'Failed to update user status');
      }
    } catch (e) {
      throw Exception('Failed to update user status: $e');
    }
  }

  // Subscribe to chat updates
  void subscribeToChats(String userId) {
    _chatSubscriptions[userId]?.cancel();
    _chatSubscriptions[userId] = SupabaseConfig.client
        .from('chats')
        .stream(primaryKey: ['id'])
        .eq('user1_id', userId)
        .listen((data) {
          _refreshChatsForUser(userId);
        });
  }

  // Subscribe to message updates for a chat
  void subscribeToMessages(String chatId) {
    _messageSubscriptions[chatId]?.cancel();
    _messageSubscriptions[chatId] = SupabaseConfig.client
        .from('messages')
        .stream(primaryKey: ['id'])
        .eq('chat_id', chatId)
        .listen((data) {
          if (data.isNotEmpty) {
            final message = Message.fromJson(data.last);
            _messagesController.add(message);
          }
        });
  }

  // Unsubscribe from chat updates
  void unsubscribeFromChats(String userId) {
    _chatSubscriptions[userId]?.cancel();
    _chatSubscriptions.remove(userId);
  }

  // Unsubscribe from message updates
  void unsubscribeFromMessages(String chatId) {
    _messageSubscriptions[chatId]?.cancel();
    _messageSubscriptions.remove(chatId);
  }

  // Refresh chats for a user
  Future<void> _refreshChatsForUser(String userId) async {
    try {
      final chats = await getUserChats(userId);
      _chatsController.add(chats);
    } catch (e) {
      // Handle error silently
    }
  }

  // Dispose all subscriptions
  void dispose() {
    for (final subscription in _chatSubscriptions.values) {
      subscription.cancel();
    }
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    _chatSubscriptions.clear();
    _messageSubscriptions.clear();
    _chatsController.close();
    _messagesController.close();
  }
}