enum ConfigValueType { string, number, boolean, json }

class AppConfig {
  final String id;
  final String key;
  final String? value;
  final ConfigValueType valueType;
  final String? description;
  final String category;
  final bool isSensitive;
  final bool isPublic;
  final DateTime createdAt;
  final DateTime updatedAt;

  AppConfig({
    required this.id,
    required this.key,
    this.value,
    required this.valueType,
    this.description,
    required this.category,
    required this.isSensitive,
    required this.isPublic,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AppConfig.fromJson(Map<String, dynamic> json) {
    return AppConfig(
      id: json['id'],
      key: json['key'],
      value: json['value'],
      valueType: ConfigValueType.values.firstWhere(
        (e) => e.name == json['value_type'],
        orElse: () => ConfigValueType.string,
      ),
      description: json['description'],
      category: json['category'] ?? 'general',
      isSensitive: json['is_sensitive'] ?? false,
      isPublic: json['is_public'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'value': value,
      'value_type': valueType.name,
      'description': description,
      'category': category,
      'is_sensitive': isSensitive,
      'is_public': isPublic,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper methods to get typed values
  String get stringValue => value ?? '';
  
  double get numberValue {
    if (value == null) return 0.0;
    return double.tryParse(value!) ?? 0.0;
  }
  
  bool get booleanValue {
    if (value == null) return false;
    return value!.toLowerCase() == 'true';
  }
  
  Map<String, dynamic>? get jsonValue {
    if (value == null) return null;
    try {
      return Map<String, dynamic>.from(value as Map);
    } catch (e) {
      return null;
    }
  }

  AppConfig copyWith({
    String? id,
    String? key,
    String? value,
    ConfigValueType? valueType,
    String? description,
    String? category,
    bool? isSensitive,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppConfig(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      valueType: valueType ?? this.valueType,
      description: description ?? this.description,
      category: category ?? this.category,
      isSensitive: isSensitive ?? this.isSensitive,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}