import 'package:flutter/material.dart';
import 'package:mlink/services/admin/admin_service.dart';
import 'package:intl/intl.dart';
import 'package:data_table_2/data_table_2.dart';

class PaymentsManager extends StatefulWidget {
  const PaymentsManager({super.key});

  @override
  State<PaymentsManager> createState() => _PaymentsManagerState();
}

class _PaymentsManagerState extends State<PaymentsManager>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic> subscriptionStats = {};
  double commissionRate = 15.0;
  bool isLoading = true;
  final currencyFormatter = NumberFormat.currency(locale: 'sw_TZ', symbol: 'TSh ');

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadPaymentData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPaymentData() async {
    try {
      final data = await AdminService.instance.getSubscriptionStats();
      setState(() {
        subscriptionStats = data;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading payment data: $e');
      setState(() => isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Payments & Subscriptions',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.subscriptions),
              text: 'Subscriptions',
            ),
            Tab(
              icon: Icon(Icons.monetization_on),
              text: 'Agent Earnings',
            ),
          ],
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSubscriptionsTab(),
                _buildEarningsTab(),
              ],
            ),
    );
  }

  Widget _buildSubscriptionsTab() {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subscription Stats Cards
          _buildSubscriptionStatsGrid(),
          
          const SizedBox(height: 24),
          
          // Active Subscriptions Table
          _buildSubscriptionsTable(),
        ],
      ),
    );
  }

  Widget _buildSubscriptionStatsGrid() {
    final theme = Theme.of(context);
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = constraints.maxWidth > 800 ? 4 : 2;
        
        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              title: 'Total Subscriptions',
              value: (subscriptionStats['total_subscriptions'] ?? 0).toString(),
              subtitle: '+${subscriptionStats['new_this_month'] ?? 0} this month',
              icon: Icons.subscriptions,
              color: Colors.blue,
            ),
            _buildStatCard(
              title: 'Monthly Revenue',
              value: currencyFormatter.format(subscriptionStats['monthly_revenue'] ?? 0),
              subtitle: '+${subscriptionStats['revenue_growth_percent'] ?? 0}% from last month',
              icon: Icons.trending_up,
              color: Colors.green,
            ),
            _buildStatCard(
              title: 'Active Plans',
              value: (subscriptionStats['active_plans'] ?? 0).toString(),
              subtitle: subscriptionStats['plan_names'] ?? 'No plans',
              icon: Icons.card_membership,
              color: Colors.orange,
            ),
            _buildStatCard(
              title: 'Renewal Rate',
              value: '${subscriptionStats['renewal_rate'] ?? 0}%',
              subtitle: 'Last 30 days',
              icon: Icons.refresh,
              color: Colors.purple,
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionsTable() {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Active Subscriptions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                OutlinedButton.icon(
                  onPressed: () {
                    // Export subscriptions data
                  },
                  icon: const Icon(Icons.download),
                  label: const Text('Export'),
                ),
              ],
            ),
          ),
          FutureBuilder<List<Map<String, dynamic>>>(
            future: _getActiveSubscriptions(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              
              final subscriptions = snapshot.data ?? [];
              
              if (subscriptions.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      const SizedBox(height: 40),
                      Icon(
                        Icons.subscriptions_outlined,
                        size: 64,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No active subscriptions found',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      const SizedBox(height: 40),
                    ],
                  ),
                );
              }
              
              return DataTable2(
                columnSpacing: 12,
                horizontalMargin: 12,
                minWidth: 800,
                columns: const [
                  DataColumn2(
                    label: Text('User'),
                    size: ColumnSize.M,
                  ),
                  DataColumn2(
                    label: Text('Plan'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Amount'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Status'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Next Billing'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Actions'),
                    size: ColumnSize.S,
                  ),
                ],
                rows: subscriptions.map((sub) => DataRow2(
              cells: [
                DataCell(
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.blue.withValues(alpha: 0.1),
                        child: const Icon(Icons.person, size: 16),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            sub['userName'],
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            sub['userEmail'],
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                DataCell(
                  Chip(
                    label: Text(sub['plan']),
                    backgroundColor: _getPlanColor(sub['plan']).withValues(alpha: 0.1),
                    side: BorderSide.none,
                  ),
                ),
                DataCell(
                  Text(
                    currencyFormatter.format(sub['amount']),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                DataCell(
                  Chip(
                    label: Text(sub['status']),
                    backgroundColor: _getStatusColor(sub['status']).withValues(alpha: 0.1),
                    side: BorderSide.none,
                  ),
                ),
                DataCell(
                  Text(
                    sub['nextBilling'],
                    style: theme.textTheme.bodySmall,
                  ),
                ),
                DataCell(
                  IconButton(
                    onPressed: () => _showSubscriptionActions(sub),
                    icon: const Icon(Icons.more_vert, size: 18),
                  ),
                ),
              ],
            )).toList(),
              );
            }
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsTab() {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Commission Settings Card
          _buildCommissionSettings(),
          
          const SizedBox(height: 24),
          
          // Agent Earnings Table
          _buildAgentEarningsTable(),
        ],
      ),
    );
  }

  Widget _buildCommissionSettings() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Commission Settings',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Icon(
                Icons.settings,
                color: theme.colorScheme.primary,
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Agent Commission Rate',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        SizedBox(
                          width: 100,
                          child: TextField(
                            controller: TextEditingController(
                              text: commissionRate.toString(),
                            ),
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              suffixText: '%',
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              final rate = double.tryParse(value);
                              if (rate != null && rate >= 0 && rate <= 100) {
                                setState(() => commissionRate = rate);
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: _updateCommissionRate,
                          child: const Text('Update'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This rate applies to all agent earnings from completed deliveries',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 32),
              
              // Commission Summary
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      'Current Rate',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    Text(
                      '${commissionRate.toStringAsFixed(1)}%',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAgentEarningsTable() {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Agent Earnings',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    OutlinedButton.icon(
                      onPressed: () {
                        // Filter earnings
                      },
                      icon: const Icon(Icons.filter_list),
                      label: const Text('Filter'),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: () {
                        // Export earnings data
                      },
                      icon: const Icon(Icons.download),
                      label: const Text('Export'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          FutureBuilder<List<Map<String, dynamic>>>(
            future: _getAgentEarnings(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              
              final earnings = snapshot.data ?? [];
              
              if (earnings.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      const SizedBox(height: 40),
                      Icon(
                        Icons.monetization_on_outlined,
                        size: 64,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No agent earnings data found',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      const SizedBox(height: 40),
                    ],
                  ),
                );
              }
              
              return DataTable2(
                columnSpacing: 12,
                horizontalMargin: 12,
                minWidth: 900,
                columns: const [
                  DataColumn2(
                    label: Text('Agent'),
                    size: ColumnSize.M,
                  ),
                  DataColumn2(
                    label: Text('Deliveries'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Total Earned'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Commission'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Rating'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Status'),
                    size: ColumnSize.S,
                  ),
                  DataColumn2(
                    label: Text('Actions'),
                    size: ColumnSize.S,
                  ),
                ],
                rows: earnings.map((earning) => DataRow2(
              cells: [
                DataCell(
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.green.withValues(alpha: 0.1),
                        child: const Icon(Icons.delivery_dining, size: 16),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            earning['agentName'],
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            earning['agentId'],
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                DataCell(
                  Text(
                    earning['deliveries'].toString(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    currencyFormatter.format(earning['totalEarned']),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.green,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    currencyFormatter.format(earning['commission']),
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                DataCell(
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        earning['rating'].toStringAsFixed(1),
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                DataCell(
                  Chip(
                    label: Text(earning['status']),
                    backgroundColor: earning['status'] == 'Active' 
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    side: BorderSide.none,
                  ),
                ),
                DataCell(
                  IconButton(
                    onPressed: () => _showEarningsActions(earning),
                    icon: const Icon(Icons.more_vert, size: 18),
                  ),
                ),
              ],
            )).toList(),
              );
            }
          ),
        ],
      ),
    );
  }

  Color _getPlanColor(String plan) {
    switch (plan.toLowerCase()) {
      case 'basic':
        return Colors.blue;
      case 'pro':
        return Colors.green;
      case 'premium':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'expired':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Future<List<Map<String, dynamic>>> _getActiveSubscriptions() async {
    try {
      // TODO: Implement proper backend call to get active subscriptions
      // For now, return empty list until backend is ready
      return [];
    } catch (e) {
      print('Error getting active subscriptions: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _getAgentEarnings() async {
    try {
      // TODO: Implement proper backend call to get agent earnings
      // For now, return empty list until backend is ready
      return [];
    } catch (e) {
      print('Error getting agent earnings: $e');
      return [];
    }
  }

  void _showSubscriptionActions(Map<String, dynamic> subscription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Subscription Actions'),
        content: Text('Manage subscription for ${subscription['userName']}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showEarningsActions(Map<String, dynamic> earning) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Agent Actions'),
        content: Text('Manage earnings for ${earning['agentName']}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateCommissionRate() async {
    try {
      await AdminService.instance.updateCommissionRate(commissionRate);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Commission rate updated successfully'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating commission rate: $e')),
      );
    }
  }
}