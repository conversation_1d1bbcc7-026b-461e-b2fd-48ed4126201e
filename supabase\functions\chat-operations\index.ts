import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatPayload {
  action: 'create_chat' | 'send_message' | 'get_chats' | 'get_messages' | 'mark_read' | 'update_status'
  chatData?: any
  messageData?: any
  chatId?: string
  userId?: string
  messageId?: string
  status?: 'online' | 'offline' | 'typing'
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, chatData, messageData, chatId, userId, messageId, status } = await req.json() as ChatPayload

    switch (action) {
      case 'create_chat':
        return await createChat(supabase, chatData)
      case 'send_message':
        return await sendMessage(supabase, messageData)
      case 'get_chats':
        return await getUserChats(supabase, userId!)
      case 'get_messages':
        return await getChatMessages(supabase, chatId!)
      case 'mark_read':
        return await markMessagesAsRead(supabase, chatId!, userId!)
      case 'update_status':
        return await updateUserStatus(supabase, userId!, status!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Chat operation error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function createChat(supabase: any, chatData: any) {
  // Check if chat already exists between these users
  const { data: existingChat } = await supabase
    .from('chats')
    .select('*')
    .or(`and(user1_id.eq.${chatData.user1_id},user2_id.eq.${chatData.user2_id}),and(user1_id.eq.${chatData.user2_id},user2_id.eq.${chatData.user1_id})`)
    .maybeSingle()

  if (existingChat) {
    return new Response(
      JSON.stringify({ success: true, chat: existingChat }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Create new chat
  const { data: chat, error } = await supabase
    .from('chats')
    .insert({
      ...chatData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .select(`
      *,
      user1:users!chats_user1_id_fkey(id, full_name, avatar_url),
      user2:users!chats_user2_id_fkey(id, full_name, avatar_url)
    `)
    .single()

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, chat }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function sendMessage(supabase: any, messageData: any) {
  const { data: message, error } = await supabase
    .from('messages')
    .insert({
      ...messageData,
      created_at: new Date().toISOString(),
      is_read: false,
    })
    .select(`
      *,
      sender:users!messages_sender_id_fkey(id, full_name, avatar_url)
    `)
    .single()

  if (error) throw error

  // Update chat's last message and timestamp
  await supabase
    .from('chats')
    .update({
      last_message: messageData.content,
      last_message_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', messageData.chat_id)

  // Get the other user in the chat to send notification
  const { data: chat } = await supabase
    .from('chats')
    .select('user1_id, user2_id')
    .eq('id', messageData.chat_id)
    .single()

  if (chat) {
    const recipientId = chat.user1_id === messageData.sender_id ? chat.user2_id : chat.user1_id

    // Send notification to recipient
    await supabase.from('notifications').insert({
      user_id: recipientId,
      title: 'New Message',
      message: `You have a new message: ${messageData.content.substring(0, 50)}${messageData.content.length > 50 ? '...' : ''}`,
      type: 'new_message',
      chat_id: messageData.chat_id,
      created_at: new Date().toISOString(),
    })
  }

  return new Response(
    JSON.stringify({ success: true, message }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getUserChats(supabase: any, userId: string) {
  const { data: chats, error } = await supabase
    .from('chats')
    .select(`
      *,
      user1:users!chats_user1_id_fkey(id, full_name, avatar_url),
      user2:users!chats_user2_id_fkey(id, full_name, avatar_url),
      unread_count:messages!messages_chat_id_fkey(count)
    `)
    .or(`user1_id.eq.${userId},user2_id.eq.${userId}`)
    .order('updated_at', { ascending: false })

  if (error) throw error

  // Calculate unread messages for each chat
  const chatsWithUnreadCount = await Promise.all(
    chats?.map(async (chat) => {
      const { count: unreadCount } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('chat_id', chat.id)
        .eq('is_read', false)
        .neq('sender_id', userId)

      return {
        ...chat,
        unread_count: unreadCount || 0,
        other_user: chat.user1_id === userId ? chat.user2 : chat.user1,
      }
    }) || []
  )

  return new Response(
    JSON.stringify({ success: true, chats: chatsWithUnreadCount }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function getChatMessages(supabase: any, chatId: string) {
  const { data: messages, error } = await supabase
    .from('messages')
    .select(`
      *,
      sender:users!messages_sender_id_fkey(id, full_name, avatar_url)
    `)
    .eq('chat_id', chatId)
    .order('created_at', { ascending: true })

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true, messages }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function markMessagesAsRead(supabase: any, chatId: string, userId: string) {
  const { error } = await supabase
    .from('messages')
    .update({
      is_read: true,
      read_at: new Date().toISOString(),
    })
    .eq('chat_id', chatId)
    .eq('is_read', false)
    .neq('sender_id', userId)

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function updateUserStatus(supabase: any, userId: string, status: string) {
  const { error } = await supabase
    .from('users')
    .update({
      status: status,
      last_seen: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)

  if (error) throw error

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}