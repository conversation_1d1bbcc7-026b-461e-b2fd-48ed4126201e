import 'dart:convert';
import 'dart:typed_data';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:mlink/config/environment.dart';

class GoogleAIConfig {
  static GenerativeModel? _model;
  static GenerativeModel? _visionModel;

  // Default model configurations
  static const String defaultTextModel = 'gemini-1.5-flash';
  static const String defaultVisionModel = 'gemini-1.5-flash';
  
  static GenerativeModel get model {
    if (_model == null) {
      final apiKey = Environment.googleAIApiKey;
      if (apiKey.isEmpty) {
        throw GoogleAIException('Google AI API key not configured. Please set GOOGLE_AI_API_KEY in your environment.');
      }
      _model = GenerativeModel(
        model: defaultTextModel,
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1000,
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
      );
    }
    return _model!;
  }

  static GenerativeModel get visionModel {
    if (_visionModel == null) {
      final apiKey = Environment.googleAIApiKey;
      if (apiKey.isEmpty) {
        throw GoogleAIException('Google AI API key not configured. Please set GOOGLE_AI_API_KEY in your environment.');
      }
      _visionModel = GenerativeModel(
        model: defaultVisionModel,
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.4,
          topK: 32,
          topP: 1.0,
          maxOutputTokens: 1000,
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
      );
    }
    return _visionModel!;
  }

  static Future<String> generateResponse({
    required String prompt,
    double temperature = 0.7,
    int maxTokens = 1000,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        // Create a custom model with specific parameters if different from default
        final customModel = GenerativeModel(
          model: defaultTextModel,
          apiKey: Environment.googleAIApiKey,
          generationConfig: GenerationConfig(
            temperature: temperature,
            maxOutputTokens: maxTokens,
            topK: 40,
            topP: 0.95,
          ),
          safetySettings: [
            SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
            SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
            SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
            SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
          ],
        );

        final content = [Content.text(prompt)];
        final response = await customModel.generateContent(content);

        if (response.text != null && response.text!.isNotEmpty) {
          return response.text!;
        } else {
          throw GoogleAIException('Empty response from Gemini API');
        }
      } catch (e) {
        retryCount++;
        
        if (e.toString().contains('RATE_LIMIT_EXCEEDED')) {
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount * 2));
            continue;
          }
          throw GoogleAIException('Rate limit exceeded. Please try again later.');
        } else if (e.toString().contains('UNAVAILABLE')) {
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount));
            continue;
          }
          throw GoogleAIException('Gemini service is temporarily unavailable. Please try again later.');
        } else if (e is GoogleAIException) {
          rethrow;
        }
        
        if (retryCount >= maxRetries) {
          throw GoogleAIException('Failed to generate response: ${e.toString()}');
        }
        
        await Future.delayed(Duration(seconds: retryCount));
      }
    }
    
    throw GoogleAIException('Maximum retries exceeded. Please try again later.');
  }

  static Future<String> generateChatResponse({
    required List<Content> history,
    required String message,
    double temperature = 0.7,
    int maxTokens = 1000,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        final customModel = GenerativeModel(
          model: defaultTextModel,
          apiKey: Environment.googleAIApiKey,
          generationConfig: GenerationConfig(
            temperature: temperature,
            maxOutputTokens: maxTokens,
            topK: 40,
            topP: 0.95,
          ),
          safetySettings: [
            SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
            SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
            SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
            SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
          ],
        );

        final chat = customModel.startChat(history: history);
        final response = await chat.sendMessage(Content.text(message));

        if (response.text != null && response.text!.isNotEmpty) {
          return response.text!;
        } else {
          throw GoogleAIException('Empty response from Gemini API');
        }
      } catch (e) {
        retryCount++;
        
        if (e.toString().contains('RATE_LIMIT_EXCEEDED')) {
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount * 2));
            continue;
          }
          throw GoogleAIException('Rate limit exceeded. Please try again later.');
        } else if (e.toString().contains('UNAVAILABLE')) {
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(seconds: retryCount));
            continue;
          }
          throw GoogleAIException('Gemini service is temporarily unavailable. Please try again later.');
        } else if (e is GoogleAIException) {
          rethrow;
        }
        
        if (retryCount >= maxRetries) {
          throw GoogleAIException('Failed to generate chat response: ${e.toString()}');
        }
        
        await Future.delayed(Duration(seconds: retryCount));
      }
    }
    
    throw GoogleAIException('Maximum retries exceeded. Please try again later.');
  }

  static Future<String> generateResponseWithImage({
    required String prompt,
    required Uint8List imageBytes,
    String mimeType = 'image/jpeg',
    double temperature = 0.4,
    int maxTokens = 1000,
  }) async {
    try {
      final content = [
        Content.multi([
          TextPart(prompt),
          DataPart(mimeType, imageBytes),
        ])
      ];

      final response = await visionModel.generateContent(content);

      if (response.text != null && response.text!.isNotEmpty) {
        return response.text!;
      } else {
        throw GoogleAIException('Empty response from Gemini Vision API');
      }
    } catch (e) {
      throw GoogleAIException('Failed to generate image response: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>> generateStructuredResponse({
    required String prompt,
    required Map<String, dynamic> schema,
    double temperature = 0.3,
    int maxTokens = 1000,
  }) async {
    try {
      final structuredPrompt = '''
$prompt

Please respond with a valid JSON object that follows this exact schema:
${json.encode(schema)}

Respond only with the JSON object, no additional text.
''';

      final response = await generateResponse(
        prompt: structuredPrompt,
        temperature: temperature,
        maxTokens: maxTokens,
      );

      // Try to parse the response as JSON
      try {
        final cleanedResponse = response.trim();
        final jsonStart = cleanedResponse.indexOf('{');
        final jsonEnd = cleanedResponse.lastIndexOf('}') + 1;
        
        if (jsonStart != -1 && jsonEnd > jsonStart) {
          final jsonString = cleanedResponse.substring(jsonStart, jsonEnd);
          return Map<String, dynamic>.from(json.decode(jsonString));
        } else {
          throw GoogleAIException('Response is not valid JSON format');
        }
      } catch (e) {
        throw GoogleAIException('Failed to parse structured response as JSON: ${e.toString()}');
      }
    } catch (e) {
      if (e is GoogleAIException) {
        rethrow;
      }
      throw GoogleAIException('Failed to generate structured response: ${e.toString()}');
    }
  }

  static String getUserFriendlyError(dynamic error) {
    if (error is GoogleAIException) {
      final message = error.message.toLowerCase();
      
      if (message.contains('rate limit')) {
        return 'AI services are temporarily busy. Please try again in a few moments.';
      } else if (message.contains('unavailable') || message.contains('service')) {
        return 'AI services are temporarily unavailable. Please try again later.';
      } else if (message.contains('api key')) {
        return 'AI service configuration error. Please contact support.';
      } else if (message.contains('safety') || message.contains('blocked')) {
        return 'Your request was blocked by safety filters. Please try rephrasing your message.';
      } else if (message.contains('empty response')) {
        return 'AI service returned an empty response. Please try again.';
      } else if (message.contains('json')) {
        return 'AI service response format error. Please try again.';
      }
    }
    
    return 'AI assistant is temporarily unavailable. Please try again later.';
  }

  // Helper method to convert OpenAI-style messages to Gemini Content format
  static List<Content> convertMessagesToContent(List<Map<String, dynamic>> messages) {
    final List<Content> content = [];
    
    for (final message in messages) {
      final role = message['role'] as String;
      final messageContent = message['content'] as String;
      
      if (role == 'system') {
        // Gemini doesn't have a system role, so we'll prepend system messages to the first user message
        // or create a user message if it's the only message
        content.add(Content.text('System: $messageContent'));
      } else if (role == 'user') {
        content.add(Content.text(messageContent));
      } else if (role == 'assistant') {
        content.add(Content.model([TextPart(messageContent)]));
      }
    }
    
    return content;
  }

  // Helper method to create conversation history for chat
  static List<Content> createChatHistory(List<Map<String, dynamic>> messages) {
    final List<Content> history = [];
    
    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];
      final role = message['role'] as String;
      final content = message['content'] as String;
      
      if (role == 'user') {
        history.add(Content.text(content));
      } else if (role == 'assistant') {
        history.add(Content.model([TextPart(content)]));
      }
      // Skip system messages as they should be handled separately
    }
    
    return history;
  }
}

class GoogleAIException implements Exception {
  final String message;
  GoogleAIException(this.message);
  
  @override
  String toString() => 'GoogleAIException: $message';
}