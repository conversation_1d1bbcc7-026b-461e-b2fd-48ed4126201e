import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AuthPayload {
  action: 'signup' | 'signin' | 'signout' | 'reset_password' | 'verify_email' | 'refresh_token'
  email?: string
  password?: string
  userData?: any
  token?: string
  refreshToken?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { action, email, password, userData, token, refreshToken } = await req.json() as AuthPayload

    switch (action) {
      case 'signup':
        return await signUp(supabase, email!, password!, userData)
      case 'signin':
        return await signIn(supabase, email!, password!)
      case 'signout':
        return await signOut(supabase, req)
      case 'reset_password':
        return await resetPassword(supabase, email!)
      case 'verify_email':
        return await verifyEmail(supabase, token!)
      case 'refresh_token':
        return await refreshAuthToken(supabase, refreshToken!)
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Auth operation error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function signUp(supabase: any, email: string, password: string, userData?: any) {
  console.log('Auth: Attempting signup for:', email)
  
  try {
    // Create user in auth.users
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true, // Auto-confirm email for now
      user_metadata: userData || {}
    })

    if (authError) {
      console.error('Auth: Signup error -', authError.message)
      throw authError
    }

    console.log('Auth: User created successfully -', authData.user.id)

    // Create user profile in public.users table
    if (userData) {
      const userProfile = {
        id: authData.user.id,
        email: email,
        full_name: userData.full_name || userData.fullName || 'User',
        phone_number: userData.phone_number || userData.phoneNumber || null,
        role: userData.role || 'customer',
        region: userData.region || null,
        district: userData.district || null,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      const { error: profileError } = await supabase
        .from('users')
        .insert(userProfile)

      if (profileError) {
        console.error('Auth: Profile creation error -', profileError.message)
        // If profile creation fails, delete the auth user
        await supabase.auth.admin.deleteUser(authData.user.id)
        throw new Error('Failed to create user profile')
      }

      console.log('Auth: User profile created successfully')

      // Create role-specific profile if needed
      if (userData.role === 'agent') {
        await supabase.from('agents').insert({
          user_id: authData.user.id,
          is_available: false,
          is_verified: false,
          rating: 0,
          total_requests: 0,
          success_rate: 0,
          created_at: new Date().toISOString(),
        })
      } else if (userData.role === 'technician') {
        await supabase.from('technicians').insert({
          user_id: authData.user.id,
          category: userData.category || null,
          is_available: false,
          is_verified: false,
          rating: 0,
          total_requests: 0,
          success_rate: 0,
          hourly_rate: userData.hourly_rate || 0,
          created_at: new Date().toISOString(),
        })
      }
    }

    // Generate session tokens for the user
    const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'signup',
      email: email,
    })

    if (sessionError) {
      console.error('Auth: Session generation error -', sessionError.message)
    }

    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: authData.user.id,
          email: authData.user.email,
          ...userData,
        },
        message: 'User created successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Auth: Signup failed -', error.message)
    throw error
  }
}

async function signIn(supabase: any, email: string, password: string) {
  console.log('Auth: Attempting signin for:', email)
  
  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    })

    if (authError) {
      console.error('Auth: Signin error -', authError.message)
      throw authError
    }

    // Get user profile
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (profileError) {
      console.error('Auth: Profile fetch error -', profileError.message)
      throw new Error('Failed to fetch user profile')
    }

    console.log('Auth: Signin successful for user -', authData.user.id)

    return new Response(
      JSON.stringify({
        success: true,
        user: userProfile,
        session: {
          access_token: authData.session.access_token,
          refresh_token: authData.session.refresh_token,
          expires_at: authData.session.expires_at,
        },
        message: 'Signed in successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Auth: Signin failed -', error.message)
    throw error
  }
}

async function signOut(supabase: any, req: Request) {
  try {
    const authHeader = req.headers.get('Authorization')
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')
      await supabase.auth.admin.signOut(token)
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Signed out successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Auth: Signout failed -', error.message)
    throw error
  }
}

async function resetPassword(supabase: any, email: string) {
  try {
    const { error } = await supabase.auth.admin.generateLink({
      type: 'recovery',
      email: email,
    })

    if (error) throw error

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Password reset email sent'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Auth: Password reset failed -', error.message)
    throw error
  }
}

async function verifyEmail(supabase: any, token: string) {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'email'
    })

    if (error) throw error

    return new Response(
      JSON.stringify({
        success: true,
        user: data.user,
        message: 'Email verified successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Auth: Email verification failed -', error.message)
    throw error
  }
}

async function refreshAuthToken(supabase: any, refreshToken: string) {
  try {
    const { data, error } = await supabase.auth.refreshSession({
      refresh_token: refreshToken
    })

    if (error) throw error

    return new Response(
      JSON.stringify({
        success: true,
        session: {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at,
        },
        message: 'Token refreshed successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Auth: Token refresh failed -', error.message)
    throw error
  }
}