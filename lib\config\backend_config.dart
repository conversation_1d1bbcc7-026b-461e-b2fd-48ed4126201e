import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Backend configuration for M-Link application
/// This file manages all backend-related configurations and endpoints
class BackendConfig {
  // Supabase configuration - now used primarily for auth state management
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';
  
  // Edge Functions base URL
  static String get edgeFunctionsBaseUrl => '$supabaseUrl/functions/v1';
  
  // Edge Function endpoints
  static const Map<String, String> endpoints = {
    'auth': 'auth-operations',
    'users': 'user-management',
    'requests': 'request-operations',
    'chats': 'chat-operations',
    'notifications': 'notification-operations',
    'reviews': 'review-operations',
    'lookup': 'lookup-operations',
    'analytics': 'analytics-operations',
  };
  
  // API configuration
  static const Duration requestTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  
  // Feature flags for gradual migration
  static const Map<String, bool> featureFlags = {
    'useBackendAuth': true,
    'useBackendChats': true,
    'useBackendRequests': true,
    'useBackendNotifications': true,
    'useBackendReviews': true,
    'useBackendLookup': true,
    'enableRealTimeUpdates': false, // Disabled for backend-first architecture
  };
  
  // Get full endpoint URL
  static String getEndpointUrl(String endpoint) {
    final endpointName = endpoints[endpoint];
    if (endpointName == null) {
      throw ArgumentError('Unknown endpoint: $endpoint');
    }
    return '$edgeFunctionsBaseUrl/$endpointName';
  }
  
  // Check if feature is enabled
  static bool isFeatureEnabled(String feature) {
    return featureFlags[feature] ?? false;
  }
  
  // Environment-specific configurations
  static bool get isDevelopment => const bool.fromEnvironment('dart.vm.product') == false;
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product') == true;
  
  // Logging configuration
  static bool get enableApiLogging => isDevelopment;
  static bool get enableErrorReporting => isProduction;
  
  // Cache configuration
  static const Duration cacheTimeout = Duration(minutes: 5);
  static const int maxCacheSize = 100;
  
  // Real-time configuration (for future use)
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const Duration reconnectInterval = Duration(seconds: 5);
  static const int maxReconnectAttempts = 10;
}