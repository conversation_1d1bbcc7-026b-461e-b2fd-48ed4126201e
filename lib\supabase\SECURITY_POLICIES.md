# Security Policies Documentation

## Overview
This document outlines the comprehensive security policies implemented to ensure that only authenticated users can access their personal data using their unique ID. The policies follow the principle of least privilege and implement strict data isolation.

## Key Security Improvements

### 1. User Profile Access Control
- **Own Data Only**: Users can only view and modify their own profile data
- **Immutable Fields**: Critical fields like `id`, `role`, and `created_at` are protected from modification
- **Service Provider Visibility**: Users can only view service provider profiles when they have active requests with them

### 2. Request Data Isolation
- **Ownership-Based Access**: Users can only access requests they own or are assigned to
- **Status-Based Restrictions**: Users can only delete pending requests
- **Controlled Status Transitions**: Customers can only make certain status changes

### 3. Service Provider Policies
- **Profile Visibility**: Service providers are only visible to users with relevant pending requests
- **Financial Data Protection**: Earnings and statistics are protected from direct user modification
- **Active Request Constraints**: Service providers cannot delete profiles while they have active requests

### 4. Service-Specific Request Policies
- **Type-Specific Access**: Each service type (delivery, buy_for_me, technician) has specific access controls
- **Assignment-Based Visibility**: Service providers can only view requests assigned to them
- **Creation Restrictions**: Service requests can only be created for matching request types

### 5. Review System Security
- **Relationship-Based Access**: Users can only view reviews they wrote, received, or for their requests
- **Completion Requirements**: Reviews can only be created for completed requests
- **Time-Limited Modifications**: Reviews can only be edited within 24 hours of creation

### 6. Notification Security
- **User-Specific Access**: Users can only view their own notifications
- **System-Only Creation**: Notifications can only be created by the service layer, not direct user input
- **Limited Modifications**: Users can only mark notifications as read, not modify content

### 7. Audit Trail Protection
- **Immutable Request Updates**: Request updates cannot be modified or deleted once created
- **Comprehensive Logging**: All request status changes are logged with user attribution
- **Access-Based Visibility**: Users can only view updates for requests they have access to

## Security Functions

### auth.can_access_user_data(target_user_id UUID)
Validates if the current user can access another user's data based on:
- Own data access
- Admin privileges
- Active service relationships

### auth.can_access_request(request_id UUID)
Validates if the current user can access a specific request based on:
- Request ownership
- Service provider assignment
- Admin privileges

## Admin Privileges
Admin users have elevated access for:
- Viewing all users (for support purposes)
- Accessing all requests (for monitoring)
- Creating system notifications

## Implementation Notes

### Row Level Security (RLS)
All tables have RLS enabled with comprehensive policies that:
- Authenticate users using `auth.uid()`
- Validate relationships through JOIN operations
- Implement role-based access control
- Protect sensitive operations

### Performance Considerations
- Indexes are created on frequently queried columns
- Policies use efficient JOIN operations
- Complex checks are implemented as reusable functions

### Data Integrity
- Foreign key constraints ensure data consistency
- Triggers maintain audit trails
- Immutable fields prevent tampering

## Policy Categories

### 1. Identity-Based Policies
- Use `auth.uid()` for user identification
- Ensure authenticated access only
- Validate user roles and permissions

### 2. Relationship-Based Policies
- Verify user-to-request relationships
- Validate service provider assignments
- Check active request status

### 3. Time-Based Policies
- Restrict modifications after time limits
- Validate request status transitions
- Prevent stale data access

### 4. Role-Based Policies
- Implement admin privileges
- Separate customer and service provider access
- Enforce service-specific permissions

## Security Best Practices Implemented

1. **Principle of Least Privilege**: Users have minimal required access
2. **Defense in Depth**: Multiple layers of security checks
3. **Explicit Deny**: Default deny with explicit allow rules
4. **Audit Trail**: Comprehensive logging of all actions
5. **Data Minimization**: Only necessary data is accessible
6. **Temporal Controls**: Time-based access restrictions
7. **Immutable Logs**: Protected audit trails
8. **Role Segregation**: Clear separation of user types

## Testing Recommendations

1. **Access Control Testing**: Verify users cannot access unauthorized data
2. **Privilege Escalation Testing**: Ensure users cannot elevate privileges
3. **Data Leakage Testing**: Confirm no cross-user data exposure
4. **Performance Testing**: Validate policy efficiency under load
5. **Audit Testing**: Verify all actions are properly logged

## Monitoring and Compliance

- All policies generate audit logs
- Failed access attempts are tracked
- Admin actions are logged separately
- Regular security reviews are recommended
- Compliance with data protection regulations is maintained