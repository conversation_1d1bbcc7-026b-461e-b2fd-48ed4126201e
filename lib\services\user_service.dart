import 'package:mlink/models/user.dart';
import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/services/legacy_supabase_wrapper.dart';
import 'package:mlink/services/base_service.dart';
import 'package:mlink/supabase/supabase_config.dart';

class UserService extends BaseService {
  // Private constructor for singleton pattern
  UserService._internal();
  
  static UserService? _instance;
  static UserService get instance => _instance ??= UserService._internal();

  Future<User?> getUserProfile(String userId) async {
    if (!BaseService.isValidId(userId)) {
      throw Exception('Invalid user ID format');
    }

    return BaseService.handleApiCall(
      () => BackendApiService.getUserProfile(userId, 'customer'),
      (response) {
        if (response['success'] == true && response['user'] != null) {
          return User.fromJson(response['user']);
        }
        return null;
      },
      'get user profile',
    );
  }

  Future<void> createUserProfile(User user) async {
    // Validate required fields
    BaseService.validateRequiredFields(
      user.toJson(),
      ['id', 'email', 'role'],
      'create user profile',
    );

    return BaseService.handleApiCall(
      () => BackendApiService.createUserProfile(user.toJson(), user.role.name),
      (response) => BaseService.validateAndExtractData(response, 'create user profile'),
      'create user profile',
    );
  }

  Future<void> updateUserProfile(User user) async {
    if (!BaseService.isValidId(user.id)) {
      throw Exception('Invalid user ID format');
    }

    return BaseService.handleApiCall(
      () => BackendApiService.updateUserProfile(user.id, user.toJson(), user.role.name),
      (response) => BaseService.validateAndExtractData(response, 'update user profile'),
      'update user profile',
    );
  }

  Future<List<User>> getPublicProfiles(UserRole role) async {
    try {
      final response = await LegacySupabaseWrapper.getPublicProfiles(role.name);
      return response.map((data) => User.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get public profiles: $e');
    }
  }

  Future<void> deleteUserProfile(String userId) async {
    try {
      await LegacySupabaseWrapper.deleteUserProfile(userId);
    } catch (e) {
      throw Exception('Failed to delete user profile: $e');
    }
  }

  Future<void> updateUserStatus(String userId, UserStatus status) async {
    try {
      await SupabaseConfig.client
          .from('users')
          .update({
            'status': status.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
    } catch (e) {
      throw Exception('Failed to update user status: $e');
    }
  }

  Future<List<User>> searchUsers(String query, {UserRole? role}) async {
    try {
      var queryBuilder = SupabaseConfig.client
          .from('users')
          .select()
          .or('full_name.ilike.%$query%,email.ilike.%$query%,phone_number.ilike.%$query%');
      
      if (role != null) {
        queryBuilder = queryBuilder.eq('role', role.name);
      }
      
      final response = await queryBuilder;
      return response.map((data) => User.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      return await LegacySupabaseWrapper.getUserStats(userId);
    } catch (e) {
      throw Exception('Failed to get user stats: $e');
    }
  }
}