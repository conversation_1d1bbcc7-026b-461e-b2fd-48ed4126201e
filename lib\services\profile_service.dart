import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/services/user_service.dart';
import 'package:mlink/models/user.dart';

/// Service for handling enhanced profile operations with dynamic data
class ProfileService {
  static final ProfileService _instance = ProfileService._internal();
  factory ProfileService() => _instance;
  ProfileService._internal();

  static ProfileService get instance => _instance;

  /// Get enhanced profile data with dynamic metrics and real-time information
  Future<EnhancedProfile> getEnhancedProfile(String userId, UserRole userRole) async {
    try {
      // Fetch data in parallel for better performance
      final results = await Future.wait([
        _getBaseProfile(userId, userRole),
        _getPerformanceMetrics(userId, userRole),
        _getActivitySummary(userId),
        _getUserSettings(userId),
        _getNotificationPreferences(userId),
      ]);

      final baseProfile = results[0];
      final performanceMetrics = results[1];
      final activitySummary = results[2];
      final userSettings = results[3];
      final notificationPrefs = results[4];

      return EnhancedProfile(
        user: User.fromJson(baseProfile['user'] ?? {}),
        performanceMetrics: PerformanceMetrics.fromJson(performanceMetrics),
        activitySummary: ActivitySummary.fromJson(activitySummary),
        userSettings: UserSettings.fromJson(userSettings),
        notificationPreferences: NotificationPreferences.fromJson(notificationPrefs),
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Failed to get enhanced profile: $e');
    }
  }

  /// Get base profile information
  Future<Map<String, dynamic>> _getBaseProfile(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getEnhancedProfile(userId, userRole.name);
      
      if (response['success'] == true) {
        return response;
      } else {
        throw Exception(response['error'] ?? 'Failed to get base profile');
      }
    } catch (e) {
      // Fallback to basic user service
      final user = await UserService.instance.getUserProfile(userId);
      return {
        'user': user?.toJson() ?? {},
        'success': true,
      };
    }
  }

  /// Get performance metrics
  Future<Map<String, dynamic>> _getPerformanceMetrics(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getUserPerformanceMetrics(userId, userRole.name);
      
      if (response['success'] == true && response['metrics'] != null) {
        return response['metrics'];
      }
      return {};
    } catch (e) {
      return {};
    }
  }

  /// Get activity summary
  Future<Map<String, dynamic>> _getActivitySummary(String userId) async {
    try {
      final response = await BackendApiService.getUserActivitySummary(userId, 'last_30_days');
      
      if (response['success'] == true && response['summary'] != null) {
        return response['summary'];
      }
      return {};
    } catch (e) {
      return {};
    }
  }

  /// Get user settings
  Future<Map<String, dynamic>> _getUserSettings(String userId) async {
    try {
      final response = await BackendApiService.getUserSettings(userId);
      
      if (response['success'] == true && response['settings'] != null) {
        return response['settings'];
      }
      return {};
    } catch (e) {
      return {};
    }
  }

  /// Get notification preferences
  Future<Map<String, dynamic>> _getNotificationPreferences(String userId) async {
    try {
      // This would be part of user settings but separated for clarity
      final settings = await _getUserSettings(userId);
      return settings['notifications'] ?? {};
    } catch (e) {
      return {};
    }
  }

  /// Update user availability (for agents and technicians)
  Future<void> updateAvailability(
    String userId, 
    UserRole userRole, 
    bool isAvailable, 
    {String? location, List<String>? serviceAreas}
  ) async {
    try {
      final availabilityData = {
        'is_available': isAvailable,
        if (location != null) 'location': location,
        if (serviceAreas != null) 'service_areas': serviceAreas,
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await BackendApiService.updateProfileAvailability(
        userId, 
        userRole.name, 
        availabilityData
      );

      if (response['success'] != true) {
        throw Exception(response['error'] ?? 'Failed to update availability');
      }
    } catch (e) {
      throw Exception('Failed to update availability: $e');
    }
  }

  /// Update user settings
  Future<void> updateUserSettings(String userId, Map<String, dynamic> settings) async {
    try {
      final response = await BackendApiService.updateUserSettings(userId, settings);
      
      if (response['success'] != true) {
        throw Exception(response['error'] ?? 'Failed to update settings');
      }
    } catch (e) {
      throw Exception('Failed to update user settings: $e');
    }
  }

  /// Update notification preferences
  Future<void> updateNotificationPreferences(
    String userId, 
    NotificationPreferences preferences
  ) async {
    try {
      final settings = {
        'notifications': preferences.toJson(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await updateUserSettings(userId, settings);
    } catch (e) {
      throw Exception('Failed to update notification preferences: $e');
    }
  }

  /// Get user stats summary
  Future<Map<String, dynamic>> getUserStatsSummary(String userId, UserRole userRole) async {
    try {
      final response = await BackendApiService.getUserAnalytics(userId);
      
      if (response['success'] == true && response['analytics'] != null) {
        return response['analytics'];
      }
      return {};
    } catch (e) {
      return {};
    }
  }

  /// Calculate profile completion percentage
  double calculateProfileCompletion(User user, EnhancedProfile? enhancedProfile) {
    double completionScore = 0.0;
    int totalFields = 10; // Total profile fields to check

    // Basic profile fields
    if (user.fullName?.isNotEmpty == true) completionScore += 1;
    if (user.email.isNotEmpty) completionScore += 1;
    if (user.phoneNumber?.isNotEmpty == true) completionScore += 1;
    if (user.avatarUrl?.isNotEmpty == true) completionScore += 1;

    // Role-specific fields
    switch (user.role) {
      case UserRole.agent:
      case UserRole.technician:
        // Additional fields for service providers
        if (enhancedProfile?.userSettings.serviceAreas.isNotEmpty == true) completionScore += 1;
        if (enhancedProfile?.userSettings.workingHours.isNotEmpty == true) completionScore += 1;
        if (enhancedProfile?.userSettings.vehicleInfo.isNotEmpty == true) completionScore += 1;
        totalFields += 3;
        break;
      default:
        break;
    }

    // Settings completion
    if (enhancedProfile?.notificationPreferences.isConfigured == true) completionScore += 1;
    if (enhancedProfile?.userSettings.language?.isNotEmpty == true) completionScore += 1;
    if (enhancedProfile?.userSettings.timezone?.isNotEmpty == true) completionScore += 1;

    return (completionScore / totalFields) * 100;
  }
}

/// Enhanced profile model containing all user data
class EnhancedProfile {
  final User user;
  final PerformanceMetrics performanceMetrics;
  final ActivitySummary activitySummary;
  final UserSettings userSettings;
  final NotificationPreferences notificationPreferences;
  final DateTime lastUpdated;

  EnhancedProfile({
    required this.user,
    required this.performanceMetrics,
    required this.activitySummary,
    required this.userSettings,
    required this.notificationPreferences,
    required this.lastUpdated,
  });
}

/// Performance metrics for the profile
class PerformanceMetrics {
  final double averageRating;
  final int totalReviews;
  final int completedJobs;
  final double successRate;
  final int responseTime; // in minutes
  final double customerSatisfaction;
  final Map<String, dynamic> additionalMetrics;

  PerformanceMetrics({
    required this.averageRating,
    required this.totalReviews,
    required this.completedJobs,
    required this.successRate,
    required this.responseTime,
    required this.customerSatisfaction,
    required this.additionalMetrics,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) {
    return PerformanceMetrics(
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      completedJobs: json['completed_jobs'] ?? 0,
      successRate: (json['success_rate'] ?? 0.0).toDouble(),
      responseTime: json['response_time'] ?? 0,
      customerSatisfaction: (json['customer_satisfaction'] ?? 0.0).toDouble(),
      additionalMetrics: json['additional_metrics'] ?? {},
    );
  }
}

/// Activity summary for the profile
class ActivitySummary {
  final int totalRequests;
  final int thisMonthRequests;
  final int thisWeekRequests;
  final DateTime lastActivity;
  final List<String> recentActions;
  final Map<String, int> activityBreakdown;

  ActivitySummary({
    required this.totalRequests,
    required this.thisMonthRequests,
    required this.thisWeekRequests,
    required this.lastActivity,
    required this.recentActions,
    required this.activityBreakdown,
  });

  factory ActivitySummary.fromJson(Map<String, dynamic> json) {
    return ActivitySummary(
      totalRequests: json['total_requests'] ?? 0,
      thisMonthRequests: json['this_month_requests'] ?? 0,
      thisWeekRequests: json['this_week_requests'] ?? 0,
      lastActivity: json['last_activity'] != null 
          ? DateTime.parse(json['last_activity'])
          : DateTime.now().subtract(const Duration(days: 30)),
      recentActions: List<String>.from(json['recent_actions'] ?? []),
      activityBreakdown: Map<String, int>.from(json['activity_breakdown'] ?? {}),
    );
  }
}

/// User settings and preferences
class UserSettings {
  final String? language;
  final String? timezone;
  final bool darkMode;
  final List<String> serviceAreas;
  final Map<String, String> workingHours;
  final Map<String, dynamic> vehicleInfo;
  final Map<String, bool> privacySettings;

  UserSettings({
    this.language,
    this.timezone,
    required this.darkMode,
    required this.serviceAreas,
    required this.workingHours,
    required this.vehicleInfo,
    required this.privacySettings,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      language: json['language'],
      timezone: json['timezone'],
      darkMode: json['dark_mode'] ?? false,
      serviceAreas: List<String>.from(json['service_areas'] ?? []),
      workingHours: Map<String, String>.from(json['working_hours'] ?? {}),
      vehicleInfo: Map<String, dynamic>.from(json['vehicle_info'] ?? {}),
      privacySettings: Map<String, bool>.from(json['privacy_settings'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'timezone': timezone,
      'dark_mode': darkMode,
      'service_areas': serviceAreas,
      'working_hours': workingHours,
      'vehicle_info': vehicleInfo,
      'privacy_settings': privacySettings,
    };
  }
}

/// Notification preferences
class NotificationPreferences {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool jobAlerts;
  final bool messageNotifications;
  final bool promotionalEmails;
  final Map<String, bool> categoryPreferences;

  NotificationPreferences({
    required this.pushNotifications,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.jobAlerts,
    required this.messageNotifications,
    required this.promotionalEmails,
    required this.categoryPreferences,
  });

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    return NotificationPreferences(
      pushNotifications: json['push_notifications'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      smsNotifications: json['sms_notifications'] ?? false,
      jobAlerts: json['job_alerts'] ?? true,
      messageNotifications: json['message_notifications'] ?? true,
      promotionalEmails: json['promotional_emails'] ?? false,
      categoryPreferences: Map<String, bool>.from(json['category_preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'push_notifications': pushNotifications,
      'email_notifications': emailNotifications,
      'sms_notifications': smsNotifications,
      'job_alerts': jobAlerts,
      'message_notifications': messageNotifications,
      'promotional_emails': promotionalEmails,
      'category_preferences': categoryPreferences,
    };
  }

  bool get isConfigured {
    // Check if user has made any changes to default notification settings
    return !pushNotifications || 
           !emailNotifications || 
           smsNotifications || 
           !jobAlerts || 
           !messageNotifications || 
           promotionalEmails ||
           categoryPreferences.isNotEmpty;
  }
}