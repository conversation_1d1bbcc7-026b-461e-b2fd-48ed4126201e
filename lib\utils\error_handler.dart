import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Centralized error handler for the M-Link application
class ErrorHandler {
  // Private constructor to prevent instantiation
  ErrorHandler._();
  
  /// Handle authentication errors and return user-friendly messages
  static String handleAuthError(dynamic error) {
    if (error is AuthException) {
      switch (error.message.toLowerCase()) {
        case 'invalid login credentials':
        case 'invalid email or password':
          return 'Invalid email or password. Please check your credentials and try again.';
        case 'email not confirmed':
          return 'Please verify your email address by clicking the link sent to your inbox.';
        case 'user not found':
          return 'No account found with this email address.';
        case 'password should be at least 6 characters':
          return 'Password must be at least 6 characters long.';
        case 'signup disabled':
          return 'Account registration is temporarily disabled. Please try again later.';
        case 'email rate limit exceeded':
          return 'Too many requests. Please wait a moment and try again.';
        default:
          return 'Authentication failed. Please try again.';
      }
    } else if (error is PostgrestException) {
      return 'Database error occurred. Please try again later.';
    } else if (error.toString().contains('network') || 
               error.toString().contains('connection')) {
      return 'Network error. Please check your internet connection and try again.';
    }
    
    // Generic fallback
    return 'An unexpected error occurred. Please try again.';
  }
  
  /// Handle database operation errors
  static String handleDatabaseError(dynamic error) {
    if (error is PostgrestException) {
      switch (error.code) {
        case '23505': // Unique constraint violation
          return 'This data already exists. Please use different values.';
        case '23503': // Foreign key violation
          return 'Invalid reference. Please check your data and try again.';
        case '42501': // Insufficient privilege
          return 'You do not have permission to perform this action.';
        default:
          return 'Database operation failed. Please try again.';
      }
    } else if (error.toString().contains('timeout')) {
      return 'Operation timed out. Please try again.';
    } else if (error.toString().contains('network') || 
               error.toString().contains('connection')) {
      return 'Network error. Please check your internet connection.';
    }
    
    return 'Operation failed. Please try again later.';
  }
  
  /// Handle file upload errors
  static String handleStorageError(dynamic error) {
    if (error is StorageException) {
      switch (error.message.toLowerCase()) {
        case 'file size too large':
          return 'File is too large. Please select a smaller file.';
        case 'invalid file type':
          return 'Invalid file type. Please select a supported file format.';
        case 'upload failed':
          return 'File upload failed. Please check your connection and try again.';
        default:
          return 'File operation failed. Please try again.';
      }
    }
    
    return 'File operation failed. Please try again.';
  }
  
  /// Handle network/connectivity errors
  static String handleNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    } else if (errorString.contains('no internet') || 
               errorString.contains('network unavailable')) {
      return 'No internet connection. Please check your network settings.';
    } else if (errorString.contains('server error') || 
               errorString.contains('500')) {
      return 'Server error. Please try again later.';
    } else if (errorString.contains('not found') || 
               errorString.contains('404')) {
      return 'Resource not found. Please try again.';
    } else if (errorString.contains('forbidden') || 
               errorString.contains('403')) {
      return 'Access denied. Please check your permissions.';
    }
    
    return 'Network error occurred. Please try again.';
  }
  
  /// General error handler that routes to specific handlers
  static String handleError(dynamic error, {String? context}) {
    // Log error for debugging (only in debug mode)
    if (kDebugMode) {
      print('Error in $context: ${error.toString()}');
    }
    
    if (error is AuthException) {
      return handleAuthError(error);
    } else if (error is PostgrestException) {
      return handleDatabaseError(error);
    } else if (error is StorageException) {
      return handleStorageError(error);
    } else {
      return handleNetworkError(error);
    }
  }
  
  /// Validation helper for email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  /// Validation helper for password strength
  static String? validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    } else if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    } else if (!RegExp(r'^(?=.*[a-zA-Z])').hasMatch(password)) {
      return 'Password must contain at least one letter';
    }
    return null; // Password is valid
  }
  
  /// Validation helper for phone number format
  static bool isValidPhoneNumber(String phone) {
    // Basic validation for Tanzanian phone numbers
    return RegExp(r'^\+255[0-9]{9}$').hasMatch(phone) ||
           RegExp(r'^0[0-9]{9}$').hasMatch(phone);
  }
  
  /// Format phone number to international format
  static String formatPhoneNumber(String phone) {
    phone = phone.replaceAll(RegExp(r'\s+'), ''); // Remove spaces
    if (phone.startsWith('0') && phone.length == 10) {
      return '+255${phone.substring(1)}';
    }
    return phone;
  }
}