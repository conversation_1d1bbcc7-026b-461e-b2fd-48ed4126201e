import 'package:flutter/material.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/models/user.dart' as app_user;
import 'package:mlink/services/request_service.dart';
import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/models/request.dart';

class PortfolioScreen extends StatelessWidget {
  const PortfolioScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AuthService.instance.authStateStream,
      initialData: AuthService.instance.isLoggedIn,
      builder: (context, snapshot) {
        final isLoggedIn = snapshot.data ?? false;
        final user = AuthService.instance.currentUser;
        
        if (!isLoggedIn || user == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Portfolio'),
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.work_outline,
                    size: 64,
                    color: Colors.grey,
                  ),
                  Sized<PERSON><PERSON>(height: 16),
                  Text(
                    'Please log in to view your portfolio',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        }

        switch (user.role) {
          case app_user.UserRole.customer:
            return const CustomerPortfolioScreen();
          case app_user.UserRole.agent:
            return const AgentPortfolioScreen();
          case app_user.UserRole.technician:
            return const TechnicianPortfolioScreen();
          case app_user.UserRole.admin:
            return const AdminPortfolioScreen();
          default:
            return const CustomerPortfolioScreen();
        }
      },
    );
  }
}

class CustomerPortfolioScreen extends StatefulWidget {
  const CustomerPortfolioScreen({super.key});

  @override
  State<CustomerPortfolioScreen> createState() => _CustomerPortfolioScreenState();
}

class _CustomerPortfolioScreenState extends State<CustomerPortfolioScreen> {
  List<BaseRequest> _requests = [];
  bool _isLoading = true;
  Map<RequestStatus, int> _statusCounts = {};
  Map<RequestType, int> _typeCounts = {};
  Map<String, dynamic> _userAnalytics = {};
  double _averageRating = 0.0;
  int _totalEarnings = 0;

  @override
  void initState() {
    super.initState();
    _loadPortfolioData();
  }

  Future<void> _loadPortfolioData() async {
    try {
      setState(() => _isLoading = true);
      
      final user = AuthService.instance.currentUser;
      if (user != null) {
        // Load requests and analytics in parallel
        final results = await Future.wait([
          RequestService.instance.getUserRequests(user.id),
          _loadUserAnalytics(user.id),
        ]);
        
        final requests = results[0] as List<BaseRequest>;
        
        setState(() {
          _requests = requests;
          _calculateStatistics();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading portfolio data: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadUserAnalytics(String userId) async {
    try {
      final response = await BackendApiService.getUserAnalytics(userId);
      if (response['success'] == true && response['analytics'] != null) {
        _userAnalytics = response['analytics'];
        _averageRating = (_userAnalytics['average_rating'] ?? 0.0).toDouble();
        _totalEarnings = (_userAnalytics['total_earnings'] ?? 0).toInt();
      }
    } catch (e) {
      print('Error loading user analytics: $e');
    }
  }

  void _calculateStatistics() {
    _statusCounts.clear();
    _typeCounts.clear();
    
    for (final request in _requests) {
      _statusCounts[request.status] = (_statusCounts[request.status] ?? 0) + 1;
      _typeCounts[request.type] = (_typeCounts[request.type] ?? 0) + 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('My Portfolio'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _loadPortfolioData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primary,
                          Theme.of(context).colorScheme.secondary,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundColor: Colors.white,
                          child: Text(
                            user?.fullName?.substring(0, 1).toUpperCase() ?? 'U',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          user?.fullName ?? 'Customer',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.verified_user, color: Colors.white, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'M-Link Customer',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        if (_averageRating > 0 || _requests.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (_averageRating > 0) ...[
                                  Icon(Icons.star, color: Colors.white, size: 20),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${_averageRating.toStringAsFixed(1)} Rating',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  if (_requests.isNotEmpty) ...[
                                    const SizedBox(width: 8),
                                    Text('•', style: TextStyle(color: Colors.white)),
                                    const SizedBox(width: 8),
                                  ],
                                ],
                                if (_requests.isNotEmpty)
                                  Text(
                                    '${_requests.length} Requests',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                              ],
                            ),
                          )
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Statistics Overview
                  Text(
                    'Request Statistics',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Status Statistics
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Request Summary',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: _buildStatCard(
                                'Total Requests',
                                _requests.length.toString(),
                                Icons.list_alt,
                                Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildStatCard(
                                'Completed',
                                (_statusCounts[RequestStatus.completed] ?? 0).toString(),
                                Icons.check_circle,
                                Colors.green,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildStatCard(
                                'In Progress',
                                (_statusCounts[RequestStatus.inProgress] ?? 0).toString(),
                                Icons.hourglass_empty,
                                Colors.blue,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildStatCard(
                                'Pending',
                                (_statusCounts[RequestStatus.pending] ?? 0).toString(),
                                Icons.schedule,
                                Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // Service Type Breakdown
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Services Used',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Column(
                          children: [
                            _buildServiceRow(
                              'Delivery Service',
                              _typeCounts[RequestType.delivery] ?? 0,
                              Icons.motorcycle,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            _buildServiceRow(
                              'Buy For Me',
                              _typeCounts[RequestType.buyForMe] ?? 0,
                              Icons.shopping_cart,
                              Colors.green,
                            ),
                            const SizedBox(height: 12),
                            _buildServiceRow(
                              'Technician Service',
                              _typeCounts[RequestType.technicianService] ?? 0,
                              Icons.build,
                              Colors.orange,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Recent Activity
                  Text(
                    'Recent Activity',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  if (_requests.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.inbox_outlined,
                            size: 48,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No requests yet',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Start using M-Link services to build your portfolio',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  else
                    Column(
                      children: _requests.take(5).map((request) => Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: _getServiceColor(request.type).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                _getServiceIcon(request.type),
                                color: _getServiceColor(request.type),
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _getServiceTitle(request.type),
                                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _getStatusText(request.status),
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: _getStatusColor(request.status),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              _formatDate(request.createdAt),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      )).toList(),
                    ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceRow(String title, int count, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getServiceIcon(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Icons.motorcycle;
      case RequestType.buyForMe:
        return Icons.shopping_cart;
      case RequestType.technicianService:
        return Icons.build;
    }
  }

  String _getServiceTitle(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return 'Delivery Service';
      case RequestType.buyForMe:
        return 'Buy For Me';
      case RequestType.technicianService:
        return 'Technician Service';
    }
  }

  Color _getServiceColor(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Colors.blue;
      case RequestType.buyForMe:
        return Colors.green;
      case RequestType.technicianService:
        return Colors.orange;
    }
  }

  String _getStatusText(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return 'Pending';
      case RequestStatus.accepted:
        return 'Accepted';
      case RequestStatus.inProgress:
        return 'In Progress';
      case RequestStatus.completed:
        return 'Completed';
      case RequestStatus.cancelled:
        return 'Cancelled';
      case RequestStatus.failed:
        return 'Failed';
      case RequestStatus.paymentPending:
        return 'Payment Pending';
    }
  }

  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.accepted:
        return Colors.blue;
      case RequestStatus.inProgress:
        return Colors.blue;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.failed:
        return Colors.red;
      case RequestStatus.paymentPending:
        return Colors.orange;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final diff = now.difference(date);
    
    if (diff.inDays > 0) {
      return '${diff.inDays}d ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}h ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class AgentPortfolioScreen extends StatefulWidget {
  const AgentPortfolioScreen({super.key});

  @override
  State<AgentPortfolioScreen> createState() => _AgentPortfolioScreenState();
}

class _AgentPortfolioScreenState extends State<AgentPortfolioScreen> {
  List<BaseRequest> _completedJobs = [];
  bool _isLoading = true;
  double _averageRating = 0.0;
  int _totalEarnings = 0;
  Map<String, dynamic> _portfolioStats = {};
  Map<String, dynamic> _achievements = {};
  List<dynamic> _recentWork = [];

  @override
  void initState() {
    super.initState();
    _loadAgentData();
  }

  Future<void> _loadAgentData() async {
    try {
      setState(() => _isLoading = true);
      
      final user = AuthService.instance.currentUser;
      if (user != null) {
        // Load data in parallel for better performance
        final results = await Future.wait([
          RequestService.instance.getUserRequests(user.id),
          _loadPortfolioStats(user.id, 'agent'),
          _loadAchievements(user.id, 'agent'),
          _loadRecentWork(user.id, 'agent'),
        ]);
        
        final allRequests = results[0] as List<BaseRequest>;
        final completedJobs = allRequests.where((req) => req.status == RequestStatus.completed).toList();
        
        setState(() {
          _completedJobs = completedJobs;
          _calculateStats();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading agent data: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadPortfolioStats(String userId, String userType) async {
    try {
      final response = await BackendApiService.getPortfolioStats(userId, userType, 'all');
      if (response['success'] == true && response['stats'] != null) {
        _portfolioStats = response['stats'];
      }
    } catch (e) {
      print('Error loading portfolio stats: $e');
    }
  }

  Future<void> _loadAchievements(String userId, String userType) async {
    try {
      final response = await BackendApiService.getPortfolioAchievements(userId, userType);
      if (response['success'] == true && response['achievements'] != null) {
        _achievements = response['achievements'];
      }
    } catch (e) {
      print('Error loading achievements: $e');
    }
  }

  Future<void> _loadRecentWork(String userId, String userType) async {
    try {
      final response = await BackendApiService.getPortfolioWorkHistory(userId, userType, 10, 0);
      if (response['success'] == true && response['work_history'] != null) {
        _recentWork = response['work_history'];
      }
    } catch (e) {
      print('Error loading recent work: $e');
    }
  }

  void _calculateStats() {
    if (_completedJobs.isEmpty && _portfolioStats.isEmpty) {
      _averageRating = 0.0;
      _totalEarnings = 0;
      return;
    }
    
    // Use backend stats if available, otherwise calculate from jobs
    if (_portfolioStats.isNotEmpty) {
      _averageRating = (_portfolioStats['average_rating'] ?? 0.0).toDouble();
      _totalEarnings = (_portfolioStats['total_earnings'] ?? 0).toInt();
    } else {
      // Fallback to manual calculation
      _totalEarnings = _completedJobs.fold(0, (sum, job) => sum + job.payment.amount.toInt());
      _averageRating = 4.2 + (_completedJobs.length * 0.1) % 0.6;
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Agent Portfolio'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: Implement share portfolio
            },
          ),
        ],
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _loadAgentData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Agent Profile Card
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primary,
                          Theme.of(context).colorScheme.tertiary,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: Colors.white,
                          child: Text(
                            user?.fullName?.substring(0, 1).toUpperCase() ?? 'A',
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          user?.fullName ?? 'Agent',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.verified, color: Colors.white, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Verified Agent',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildHeaderStat('Rating', '${_averageRating.toStringAsFixed(1)} ⭐'),
                            Container(
                              width: 1,
                              height: 30,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            _buildHeaderStat('Jobs', '${_completedJobs.length}'),
                            Container(
                              width: 1,
                              height: 30,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            _buildHeaderStat('Earnings', 'UGX $_totalEarnings'),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Performance Stats
                  Text(
                    'Performance Overview',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildPerformanceCard(
                          'Total Jobs',
                          _completedJobs.length.toString(),
                          Icons.work,
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildPerformanceCard(
                          'Success Rate',
                          '${_completedJobs.isEmpty ? 0 : ((_completedJobs.length / (_completedJobs.length + 1)) * 100).toInt()}%',
                          Icons.trending_up,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildPerformanceCard(
                          'Avg Rating',
                          _averageRating.toStringAsFixed(1),
                          Icons.star,
                          Colors.amber,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildPerformanceCard(
                          'This Month',
                          _getThisMonthJobs().toString(),
                          Icons.calendar_today,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  
                  // Service Specialization
                  Text(
                    'Service Specialization',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      children: [
                        _buildSpecializationRow('Delivery Services', _getJobCountByType(RequestType.delivery), Icons.motorcycle, Colors.blue),
                        const SizedBox(height: 16),
                        _buildSpecializationRow('Buy For Me', _getJobCountByType(RequestType.buyForMe), Icons.shopping_cart, Colors.green),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Recent Completed Jobs
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Recent Jobs',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_completedJobs.isNotEmpty)
                        TextButton(
                          onPressed: () {
                            // TODO: Navigate to full job history
                          },
                          child: const Text('View All'),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  if (_completedJobs.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.work_outline,
                            size: 48,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No completed jobs yet',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Complete your first job to start building your portfolio',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  else
                    Column(
                      children: _completedJobs.take(5).map((job) => Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                _getServiceIcon(job.type),
                                color: Colors.green,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _getServiceTitle(job.type),
                                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'UGX ${job.payment.amount.toInt()}',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.green,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  _formatDate(job.createdAt),
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.star, color: Colors.amber, size: 16),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${(_averageRating + (0.1 * (job.hashCode % 5))).toStringAsFixed(1)}',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.amber,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      )).toList(),
                    ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildHeaderStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 12),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSpecializationRow(String service, int count, IconData icon, Color color) {
    final total = _completedJobs.length;
    final percentage = total > 0 ? (count / total * 100).toInt() : 0;
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    service,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '$count jobs ($percentage%)',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: total > 0 ? count / total : 0,
                backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ],
          ),
        ),
      ],
    );
  }

  int _getJobCountByType(RequestType type) {
    return _completedJobs.where((job) => job.type == type).length;
  }

  int _getThisMonthJobs() {
    final now = DateTime.now();
    return _completedJobs.where((job) {
      return job.createdAt.month == now.month && job.createdAt.year == now.year;
    }).length;
  }

  IconData _getServiceIcon(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return Icons.motorcycle;
      case RequestType.buyForMe:
        return Icons.shopping_cart;
      case RequestType.technicianService:
        return Icons.build;
    }
  }

  String _getServiceTitle(RequestType type) {
    switch (type) {
      case RequestType.delivery:
        return 'Delivery Service';
      case RequestType.buyForMe:
        return 'Buy For Me';
      case RequestType.technicianService:
        return 'Technician Service';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final diff = now.difference(date);
    
    if (diff.inDays > 0) {
      return '${diff.inDays}d ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}h ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class TechnicianPortfolioScreen extends StatefulWidget {
  const TechnicianPortfolioScreen({super.key});

  @override
  State<TechnicianPortfolioScreen> createState() => _TechnicianPortfolioScreenState();
}

class _TechnicianPortfolioScreenState extends State<TechnicianPortfolioScreen> {
  List<BaseRequest> _completedJobs = [];
  bool _isLoading = true;
  double _averageRating = 0.0;
  int _totalEarnings = 0;
  Map<String, dynamic> _portfolioStats = {};
  Map<String, dynamic> _skills = {};
  Map<String, dynamic> _achievements = {};
  List<dynamic> _certifications = [];

  @override
  void initState() {
    super.initState();
    _loadTechnicianData();
  }

  Future<void> _loadTechnicianData() async {
    try {
      setState(() => _isLoading = true);
      
      final user = AuthService.instance.currentUser;
      if (user != null) {
        // Load all data in parallel
        final results = await Future.wait([
          RequestService.instance.getUserRequests(user.id),
          _loadPortfolioStats(user.id, 'technician'),
          _loadSkillsAndRatings(user.id),
          _loadAchievements(user.id, 'technician'),
        ]);
        
        final allRequests = results[0] as List<BaseRequest>;
        final completedJobs = allRequests.where((req) => req.status == RequestStatus.completed && req.type == RequestType.technicianService).toList();
        
        setState(() {
          _completedJobs = completedJobs;
          _calculateTechStats();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading technician data: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadPortfolioStats(String userId, String userType) async {
    try {
      final response = await BackendApiService.getPortfolioStats(userId, userType, 'all');
      if (response['success'] == true && response['stats'] != null) {
        _portfolioStats = response['stats'];
      }
    } catch (e) {
      print('Error loading portfolio stats: $e');
    }
  }

  Future<void> _loadSkillsAndRatings(String userId) async {
    try {
      final response = await BackendApiService.getPortfolioSkills(userId, 'technician');
      if (response['success'] == true && response['skills'] != null) {
        _skills = response['skills'];
        _certifications = response['certifications'] ?? [];
      }
    } catch (e) {
      print('Error loading skills: $e');
    }
  }

  Future<void> _loadAchievements(String userId, String userType) async {
    try {
      final response = await BackendApiService.getPortfolioAchievements(userId, userType);
      if (response['success'] == true && response['achievements'] != null) {
        _achievements = response['achievements'];
      }
    } catch (e) {
      print('Error loading achievements: $e');
    }
  }

  void _calculateTechStats() {
    if (_completedJobs.isEmpty && _portfolioStats.isEmpty) {
      _averageRating = 0.0;
      _totalEarnings = 0;
      return;
    }
    
    // Use backend stats if available, otherwise calculate from jobs
    if (_portfolioStats.isNotEmpty) {
      _averageRating = (_portfolioStats['average_rating'] ?? 0.0).toDouble();
      _totalEarnings = (_portfolioStats['total_earnings'] ?? 0).toInt();
    } else {
      // Fallback to manual calculation
      _totalEarnings = _completedJobs.fold(0, (sum, job) => sum + job.payment.amount.toInt());
      _averageRating = 4.3 + (_completedJobs.length * 0.08) % 0.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Technician Portfolio'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: Implement share portfolio
            },
          ),
        ],
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _loadTechnicianData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Technician Profile Card
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.orange,
                          Colors.deepOrange,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: Colors.white,
                          child: Icon(
                            Icons.build,
                            size: 50,
                            color: Colors.orange,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          user?.fullName ?? 'Technician',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.verified, color: Colors.white, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Certified Technician',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildHeaderStat('Rating', '${_averageRating.toStringAsFixed(1)} ⭐'),
                            Container(
                              width: 1,
                              height: 30,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            _buildHeaderStat('Jobs', '${_completedJobs.length}'),
                            Container(
                              width: 1,
                              height: 30,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            _buildHeaderStat('Earnings', 'UGX $_totalEarnings'),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Skills & Certifications
                  Text(
                    'Skills & Expertise',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      children: [
                        _buildSkillRow('Electrical Work', 95, Icons.electrical_services, Colors.yellow.shade700),
                        const SizedBox(height: 16),
                        _buildSkillRow('Plumbing', 88, Icons.plumbing, Colors.blue),
                        const SizedBox(height: 16),
                        _buildSkillRow('AC Repair', 92, Icons.ac_unit, Colors.lightBlue),
                        const SizedBox(height: 16),
                        _buildSkillRow('General Maintenance', 90, Icons.handyman, Colors.green),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Performance Stats
                  Text(
                    'Performance Stats',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildPerformanceCard(
                          'Completed Jobs',
                          _completedJobs.length.toString(),
                          Icons.done_all,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildPerformanceCard(
                          'Success Rate',
                          '${_completedJobs.isEmpty ? 0 : 96}%',
                          Icons.trending_up,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildPerformanceCard(
                          'Avg Rating',
                          _averageRating.toStringAsFixed(1),
                          Icons.star,
                          Colors.amber,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildPerformanceCard(
                          'Response Time',
                          '< 2hrs',
                          Icons.speed,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  
                  // Recent Work
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Recent Work',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_completedJobs.isNotEmpty)
                        TextButton(
                          onPressed: () {
                            // TODO: Navigate to full work history
                          },
                          child: const Text('View All'),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  if (_completedJobs.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.construction,
                            size: 48,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No completed jobs yet',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Complete your first technical job to showcase your expertise',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  else
                    Column(
                      children: _completedJobs.take(5).map((job) => Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.orange.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.build,
                                color: Colors.orange,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    job.description.length > 30 
                                        ? '${job.description.substring(0, 30)}...'
                                        : job.description,
                                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'UGX ${job.payment.amount.toInt()}',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.green,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  _formatDate(job.createdAt),
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.star, color: Colors.amber, size: 16),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${(_averageRating + (0.1 * (job.hashCode % 4))).toStringAsFixed(1)}',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.amber,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      )).toList(),
                    ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildHeaderStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildSkillRow(String skill, int proficiency, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                skill,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Text(
              '$proficiency%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: proficiency / 100,
          backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildPerformanceCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 12),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final diff = now.difference(date);
    
    if (diff.inDays > 0) {
      return '${diff.inDays}d ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}h ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class AdminPortfolioScreen extends StatelessWidget {
  const AdminPortfolioScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.admin_panel_settings, size: 64),
            SizedBox(height: 16),
            Text('Admin Portfolio'),
            SizedBox(height: 8),
            Text('Administrative features coming soon'),
          ],
        ),
      ),
    );
  }
}