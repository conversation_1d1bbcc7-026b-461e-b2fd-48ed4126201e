import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mlink/models/user.dart' as app_user;
import 'package:mlink/supabase/supabase_config.dart';
import 'package:mlink/services/user_service.dart';
import 'package:mlink/services/notification_service.dart';
import 'package:mlink/utils/error_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AuthService {
  static const String _keyUserId = 'user_id';
  static const String _keyUserRole = 'user_role';
  static const String _keyIsLoggedIn = 'is_logged_in';

  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  AuthService._();

  app_user.User? _currentUser;
  app_user.User? get currentUser => _currentUser;

  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();
  Stream<bool> get authStateStream => _authStateController.stream;

  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Check if user is already authenticated with Supabase
      final supabaseUser = SupabaseConfig.currentUser;
      if (supabaseUser != null) {
        await _loadUserProfile(supabaseUser.id);
      }
      
      // Listen to auth state changes
      SupabaseConfig.authStateStream.listen((data) async {
        final authUser = data.session?.user;
        if (authUser != null) {
          await _loadUserProfile(authUser.id);
          _authStateController.add(true);
        } else {
          _currentUser = null;
          final prefs = await SharedPreferences.getInstance();
          await prefs.clear();
          _authStateController.add(false);
        }
      });
      
      _isInitialized = true;
      _authStateController.add(isLoggedIn);
    } catch (e) {
      // Log error without exposing sensitive information
      print('Auth initialization failed');
      _authStateController.add(false);
    }
  }
  
  Future<void> _loadUserProfile(String userId) async {
    try {
      final user = await UserService.instance.getUserProfile(userId);
      if (user != null) {
        _currentUser = user;
        
        // Save to SharedPreferences for offline access
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_keyIsLoggedIn, true);
        await prefs.setString(_keyUserId, _currentUser!.id);
        await prefs.setString(_keyUserRole, _currentUser!.role.name);
      }
    } catch (e) {
      // Log error without exposing sensitive information
      print('Error loading user profile');
      // Clear any potentially corrupted session data
      _currentUser = null;
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    }
  }

  Future<bool> login(String email, String password, app_user.UserRole role) async {
    try {
      final response = await SupabaseConfig.signInWithEmail(email: email, password: password);
      if (response.user != null) {
        // Load user profile
        await _loadUserProfile(response.user!.id);
        _authStateController.add(true);
        return true;
      }
      return false;
    } catch (e) {
      // Log error without exposing sensitive information
      print('Login failed');
      _authStateController.add(false);
      return false;
    }
  }

  Future<Map<String, dynamic>> register(String email, String password, String fullName, app_user.UserRole role, {String? phoneNumber}) async {
    // Input validation
    if (!ErrorHandler.isValidEmail(email)) {
      return {'success': false, 'error': 'Please enter a valid email address.'};
    }
    
    final passwordError = ErrorHandler.validatePassword(password);
    if (passwordError != null) {
      return {'success': false, 'error': passwordError};
    }
    
    if (fullName.trim().isEmpty) {
      return {'success': false, 'error': 'Full name is required.'};
    }
    
    if (phoneNumber != null && phoneNumber.isNotEmpty && !ErrorHandler.isValidPhoneNumber(phoneNumber)) {
      return {'success': false, 'error': 'Please enter a valid phone number.'};
    }
    
    // Format phone number if provided
    final formattedPhoneNumber = phoneNumber != null && phoneNumber.isNotEmpty 
        ? ErrorHandler.formatPhoneNumber(phoneNumber)
        : null;
    
    try {
      print('Starting registration for email: $email');
      
      // First, sign up the user with Supabase Auth
      final authResponse = await SupabaseConfig.signUpWithEmail(email: email, password: password);
      print('Supabase signup response - User ID: ${authResponse.user?.id}');
      
      if (authResponse.user != null) {
        print('User authenticated successfully, creating user profile...');
        
        // Create user profile in the users table
        final userData = {
          'id': authResponse.user!.id,
          'email': authResponse.user!.email!,
          'full_name': fullName,
          'phone_number': formattedPhoneNumber,
          'role': role.name,
          'status': 'active',
          'region': null,
          'district': null,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };
        
        try {
          await SupabaseConfig.createUserProfile(
            userId: authResponse.user!.id,
            name: fullName,
            email: authResponse.user!.email!,
            role: role.name,
            phone: formattedPhoneNumber,
          );
          print('User profile created successfully');
          
          final user = app_user.User(
            id: authResponse.user!.id,
            email: authResponse.user!.email!,
            fullName: fullName,
            phoneNumber: formattedPhoneNumber,
            role: role,
            status: app_user.UserStatus.active,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          
          _currentUser = user;
          
          // Save to SharedPreferences
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_keyIsLoggedIn, true);
          await prefs.setString(_keyUserId, user.id);
          await prefs.setString(_keyUserRole, user.role.name);
          
          // Create welcome notification
          try {
            await SupabaseConfig.createNotification({
              'user_id': user.id,
              'title': 'Welcome to M-Link!',
              'message': 'Your account has been created successfully. Start exploring our services.',
              'type': 'welcome',
              'data': {'welcome': true},
            });
            print('Welcome notification created successfully');
          } catch (e) {
            print('Warning: Could not create welcome notification: $e');
            // Don't fail registration if notification creation fails
          }
          
          // Create role-specific profile if needed
          if (role == app_user.UserRole.agent) {
            try {
              await SupabaseConfig.client.from('agents').insert({
                'user_id': user.id,
                'is_available': true,
                'is_verified': false,
                'rating': 0.0,
                'total_deliveries': 0,
                'created_at': DateTime.now().toIso8601String(),
                'updated_at': DateTime.now().toIso8601String(),
              });
              print('Agent profile created successfully');
            } catch (e) {
              print('Warning: Could not create agent profile: $e');
            }
          } else if (role == app_user.UserRole.technician) {
            try {
              await SupabaseConfig.client.from('technicians').insert({
                'user_id': user.id,
                'category': 'general', // Default category
                'is_available': true,
                'is_verified': false,
                'rating': 0.0,
                'total_jobs': 0,
                'created_at': DateTime.now().toIso8601String(),
                'updated_at': DateTime.now().toIso8601String(),
              });
              print('Technician profile created successfully');
            } catch (e) {
              print('Warning: Could not create technician profile: $e');
            }
          }
          
          _authStateController.add(true);
          return {'success': true, 'user': user};
        } catch (e) {
          print('Error creating user profile: $e');
          // Clean up the auth user if profile creation fails
          try {
            await SupabaseConfig.signOut();
          } catch (signOutError) {
            print('Error signing out after profile creation failure: $signOutError');
          }
          throw e;
        }
      }
      
      return {'success': false, 'error': 'Account creation failed. Please try again.'};
    } catch (e) {
      final errorMessage = ErrorHandler.handleError(e, context: 'user registration');
      return {'success': false, 'error': errorMessage};
    }
  }

  Future<void> logout() async {
    try {
      await SupabaseConfig.signOut();
      _currentUser = null;
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      _authStateController.add(false);
    } catch (e) {
      print('Logout error: $e');
      // Even if logout fails, clear local state
      _currentUser = null;
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      _authStateController.add(false);
    }
  }

  bool get isLoggedIn => _currentUser != null && _isSessionValid();
  
  // Check if the current session is valid
  bool _isSessionValid() {
    final supabaseUser = SupabaseConfig.currentUser;
    return supabaseUser != null;
  }
  
  // Refresh the session if needed
  Future<bool> refreshSession() async {
    try {
      final session = SupabaseConfig.client.auth.currentSession;
      if (session != null) {
        final response = await SupabaseConfig.client.auth.refreshSession();
        return response.session != null;
      }
      return false;
    } catch (e) {
      print('Session refresh failed');
      return false;
    }
  }

  // Google Sign-In methods
  Future<bool> signInWithGoogle() async {
    try {
      // For now, we'll use a simple OAuth flow with Supabase
      // The actual Google Sign-In implementation would need platform-specific setup
      await SupabaseConfig.signInWithGoogle();
      // The auth state stream will be updated automatically through the listener
      return true;
    } catch (e) {
      print('Google Sign-In error: $e');
      _authStateController.add(false);
      return false;
    }
  }
  
  // Complete Google Sign-In profile creation
  Future<Map<String, dynamic>> completeGoogleProfile(String fullName, app_user.UserRole role, {String? phoneNumber}) async {
    try {
      final supabaseUser = SupabaseConfig.currentUser;
      if (supabaseUser != null) {
        final user = app_user.User(
          id: supabaseUser.id,
          email: supabaseUser.email!,
          fullName: fullName,
          phoneNumber: phoneNumber,
          role: role,
          status: app_user.UserStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await UserService.instance.createUserProfile(user);
        await _loadUserProfile(user.id);
        
        // Create welcome notification
        await NotificationService.instance.createSystemNotification(
          userId: user.id,
          title: 'Welcome to M-Link!',
          message: 'Your Google account has been connected successfully.',
          data: {'google_signin': true},
        );
        
        return {'success': true, 'user': user};
      }
      return {'success': false, 'error': 'No Google user found'};
    } catch (e) {
      print('Complete Google profile error: $e');
      return {'success': false, 'error': 'Failed to complete Google profile: ${e.toString()}'};
    }
  }

  Future<void> signOutGoogle() async {
    // Google Sign-Out would be handled by Supabase
    await logout();
  }

  // Update user profile
  Future<bool> updateUserProfile(app_user.User user) async {
    try {
      await UserService.instance.updateUserProfile(user);
      _currentUser = user;
      
      // Update SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyUserRole, user.role.name);
      
      return true;
    } catch (e) {
      print('Update profile error: $e');
      return false;
    }
  }
  
  // Check if user needs to complete profile (for Google Sign-In)
  bool get needsProfileCompletion {
    final supabaseUser = SupabaseConfig.currentUser;
    return supabaseUser != null && _currentUser == null;
  }
  
  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await SupabaseConfig.client.auth.resetPasswordForEmail(email);
      return true;
    } catch (e) {
      print('Reset password error: $e');
      return false;
    }
  }
  
  // Change password
  Future<bool> changePassword(String newPassword) async {
    try {
      await SupabaseConfig.client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return true;
    } catch (e) {
      print('Change password error: $e');
      return false;
    }
  }
  
  // Update email
  Future<bool> updateEmail(String newEmail) async {
    try {
      await SupabaseConfig.client.auth.updateUser(
        UserAttributes(email: newEmail),
      );
      return true;
    } catch (e) {
      print('Update email error: $e');
      return false;
    }
  }
  
  // Get user stats
  Future<Map<String, int>> getUserStats() async {
    if (_currentUser == null) return {};
    try {
      return await UserService.instance.getUserStats(_currentUser!.id);
    } catch (e) {
      print('Get user stats error: $e');
      return {};
    }
  }
  
  // Delete account
  Future<bool> deleteAccount() async {
    try {
      if (_currentUser != null) {
        await UserService.instance.deleteUserProfile(_currentUser!.id);
        await logout();
        return true;
      }
      return false;
    } catch (e) {
      print('Delete account error: $e');
      return false;
    }
  }
  
  void dispose() {
    _authStateController.close();
  }
}