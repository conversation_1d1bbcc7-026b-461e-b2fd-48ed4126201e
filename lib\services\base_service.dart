import 'package:mlink/utils/error_handler.dart';
import 'package:flutter/foundation.dart';

/// Base service class that provides common functionality for all services
abstract class BaseService {
  
  /// Generic method to handle API responses with consistent error handling
  static T handleResponse<T>(
    Map<String, dynamic> response,
    T Function(dynamic data) successHandler,
    String operation,
  ) {
    try {
      if (response['success'] == true) {
        return successHandler(response['data'] ?? response);
      } else {
        final errorMessage = response['error'] ?? 'Operation failed';
        throw Exception(errorMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in $operation: $e');
      }
      rethrow;
    }
  }
  
  /// Generic method to handle API calls with consistent error handling
  static Future<T> handleApiCall<T>(
    Future<Map<String, dynamic>> Function() apiCall,
    T Function(Map<String, dynamic> response) responseHandler,
    String operation,
  ) async {
    try {
      final response = await apiCall();
      return responseHandler(response);
    } catch (e) {
      final userFriendlyError = ErrorHandler.handleError(e, context: operation);
      throw Exception(userFriendlyError);
    }
  }
  
  /// Validate response and extract data
  static Map<String, dynamic> validateAndExtractData(
    Map<String, dynamic> response,
    String operation,
  ) {
    if (response['success'] != true) {
      final errorMessage = response['error'] ?? '$operation failed';
      throw Exception(errorMessage);
    }
    return response['data'] ?? response;
  }
  
  /// Convert list response to typed list
  static List<T> convertToTypedList<T>(
    dynamic listData,
    T Function(Map<String, dynamic>) fromJson,
    String operation,
  ) {
    if (listData == null) return [];
    
    if (listData is! List) {
      throw Exception('Invalid data format for $operation');
    }
    
    return listData
        .cast<Map<String, dynamic>>()
        .map(fromJson)
        .toList();
  }
  
  /// Validate required fields in data
  static void validateRequiredFields(
    Map<String, dynamic> data,
    List<String> requiredFields,
    String operation,
  ) {
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        throw Exception('Missing required field: $field for $operation');
      }
    }
  }
  
  /// Generic pagination helper
  static Map<String, dynamic> buildPaginationParams({
    int page = 0,
    int limit = 20,
    String? orderBy,
    bool ascending = false,
  }) {
    final params = <String, dynamic>{
      'page': page,
      'limit': limit,
    };
    
    if (orderBy != null) {
      params['order_by'] = orderBy;
      params['ascending'] = ascending;
    }
    
    return params;
  }
  
  /// Generic filter helper
  static Map<String, dynamic> buildFilterParams(Map<String, dynamic>? filters) {
    if (filters == null || filters.isEmpty) return {};
    
    // Remove null values
    filters.removeWhere((key, value) => value == null);
    
    return {'filters': filters};
  }
  
  /// Rate limiting helper (basic implementation)
  static final Map<String, List<DateTime>> _rateLimitTracker = {};
  
  static bool checkRateLimit(
    String operation,
    {int maxRequests = 100, Duration window = const Duration(hours: 1)}
  ) {
    final now = DateTime.now();
    final cutoff = now.subtract(window);
    
    _rateLimitTracker[operation] = _rateLimitTracker[operation]
        ?.where((time) => time.isAfter(cutoff))
        .toList() ?? [];
    
    if (_rateLimitTracker[operation]!.length >= maxRequests) {
      return false;
    }
    
    _rateLimitTracker[operation]!.add(now);
    return true;
  }
  
  /// Validate ID format (UUID)
  static bool isValidId(String? id) {
    if (id == null || id.isEmpty) return false;
    
    // Basic UUID validation
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    );
    
    return uuidRegex.hasMatch(id);
  }
  
  /// Generic cache implementation
  static final Map<String, CacheEntry> _cache = {};
  
  static Future<T> getCached<T>(
    String cacheKey,
    Future<T> Function() dataLoader,
    {Duration ttl = const Duration(minutes: 5)}
  ) async {
    final entry = _cache[cacheKey];
    
    if (entry != null && !entry.isExpired) {
      return entry.value as T;
    }
    
    final data = await dataLoader();
    _cache[cacheKey] = CacheEntry(data, DateTime.now().add(ttl));
    
    // Clean up expired entries periodically
    if (_cache.length > 1000) {
      _cleanupCache();
    }
    
    return data;
  }
  
  static void clearCache([String? pattern]) {
    if (pattern == null) {
      _cache.clear();
    } else {
      _cache.removeWhere((key, value) => key.contains(pattern));
    }
  }
  
  static void _cleanupCache() {
    _cache.removeWhere((key, entry) => entry.isExpired);
  }
}

/// Cache entry helper class
class CacheEntry {
  final dynamic value;
  final DateTime expiresAt;
  
  CacheEntry(this.value, this.expiresAt);
  
  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

