import 'package:mlink/supabase/supabase_config.dart';

/// Legacy wrapper for direct Supabase client calls
/// This is a temporary compatibility layer during the migration to backend-first architecture
/// 
/// TODO: Remove this class once all services are migrated to use BackendApiService
@Deprecated('Use BackendApiService instead of direct Supabase client calls')
class LegacySupabaseWrapper {
  
  /// Get public profiles by role
  /// This is a temporary compatibility method
  static Future<List<Map<String, dynamic>>> getPublicProfiles(String role) async {
    try {
      final response = await SupabaseConfig.client
          .from('users')
          .select()
          .eq('role', role)
          .eq('status', 'active');
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to get public profiles: $e');
    }
  }
  
  /// Get user stats
  /// This is a temporary compatibility method
  static Future<Map<String, int>> getUserStats(String userId) async {
    try {
      // This would typically be handled by a proper analytics backend API
      // For now, using direct queries as a temporary measure
      
      final requestsCount = await SupabaseConfig.client
          .from('requests')
          .select('id')
          .eq('customer_id', userId)
          .count();
          
      final completedRequestsCount = await SupabaseConfig.client
          .from('requests')
          .select('id')
          .eq('customer_id', userId)
          .eq('status', 'completed')
          .count();
          
      final reviewsCount = await SupabaseConfig.client
          .from('reviews')
          .select('id')
          .eq('reviewee_id', userId)
          .count();
      
      return {
        'total_requests': requestsCount.count,
        'completed_requests': completedRequestsCount.count,
        'total_reviews': reviewsCount.count,
      };
    } catch (e) {
      print('Error getting user stats: $e');
      return {
        'total_requests': 0,
        'completed_requests': 0,
        'total_reviews': 0,
      };
    }
  }
  
  /// Delete user profile
  /// This is a temporary compatibility method
  static Future<void> deleteUserProfile(String userId) async {
    try {
      // Delete from users table
      await SupabaseConfig.client
          .from('users')
          .delete()
          .eq('id', userId);
          
      // Delete from auth.users (admin operation)
      // This would typically be handled by the backend
      print('User profile deleted for $userId');
    } catch (e) {
      throw Exception('Failed to delete user profile: $e');
    }
  }
}