import 'package:flutter/material.dart';
import 'package:mlink/services/chat_service.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/services/ai_assistant_service.dart';
import 'package:mlink/openai/openai_config.dart';
import 'package:mlink/models/chat.dart';
import 'package:mlink/models/message.dart';
import 'package:mlink/models/user.dart';

class ChatConversationScreen extends StatefulWidget {
  final Chat chat;

  const ChatConversationScreen({super.key, required this.chat});

  @override
  State<ChatConversationScreen> createState() => _ChatConversationScreenState();
}

class _ChatConversationScreenState extends State<ChatConversationScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Message> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;
  bool _showAIAssistant = false;
  bool _isTranslating = false;
  List<String> _messageSuggestions = [];

  @override
  void initState() {
    super.initState();
    _loadMessages();
    _subscribeToMessages();
    _markMessagesAsRead();
  }

  @override
  void dispose() {
    ChatService.instance.unsubscribeFromMessages(widget.chat.id);
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final messages = await ChatService.instance.getChatMessages(widget.chat.id);
      setState(() {
        _messages = messages;
        _isLoading = false;
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError(e.toString());
    }
  }

  void _subscribeToMessages() {
    ChatService.instance.subscribeToMessages(widget.chat.id);
    ChatService.instance.messagesStream.listen((message) {
      if (message.chatId == widget.chat.id) {
        setState(() {
          _messages.add(message);
        });
        _scrollToBottom();
        _markMessagesAsRead();
      }
    });
  }

  Future<void> _markMessagesAsRead() async {
    final user = AuthService.instance.currentUser;
    if (user != null) {
      await ChatService.instance.markMessagesAsRead(widget.chat.id, user.id);
    }
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty || _isSending) return;

    final user = AuthService.instance.currentUser;
    if (user == null) return;

    final messageText = _messageController.text.trim();
    _messageController.clear();

    setState(() {
      _isSending = true;
      _messageSuggestions.clear();
    });

    try {
      // Optional: Analyze message sentiment for moderation
      if (_showAIAssistant) {
        final analysis = await AIAssistantService.instance.analyzeMessageSentiment(
          message: messageText,
          currentUser: user,
        );
        
        if (analysis['needs_moderation'] == true) {
          _showModerationWarning(analysis['reason'] ?? 'Message may contain inappropriate content');
          setState(() {
            _isSending = false;
          });
          return;
        }
      }

      await ChatService.instance.sendMessage(
        chatId: widget.chat.id,
        senderId: user.id,
        content: messageText,
      );

      _scrollToBottom();
      if (_showAIAssistant) {
        _loadMessageSuggestions();
      }
    } catch (e) {
      _showError(e.toString());
    } finally {
      setState(() {
        _isSending = false;
      });
    }
  }

  Future<void> _loadMessageSuggestions() async {
    try {
      final lastMessages = _messages.take(5).map((msg) => msg.content).join(' ');
      final otherUserName = widget.chat.otherUser?.fullName ?? 'the other person';
      
      final suggestions = await AIAssistantService.instance.generateContextualResponse(
        userMessage: 'Generate 3 short, friendly follow-up message suggestions based on this conversation with $otherUserName',
        userContext: 'Recent conversation: $lastMessages',
        currentUser: AuthService.instance.currentUser,
      );

      // Parse suggestions (assuming they come as numbered list)
      final suggestionList = suggestions.split('\n')
          .where((line) => line.trim().isNotEmpty)
          .map((line) => line.replaceAll(RegExp(r'^\d+\.?\s*'), '').trim())
          .where((line) => line.isNotEmpty)
          .take(3)
          .toList();

      setState(() {
        _messageSuggestions = suggestionList;
      });
    } catch (e) {
      // Fail silently for suggestions
    }
  }

  Future<void> _translateMessage(String message) async {
    if (_isTranslating) return;
    
    setState(() {
      _isTranslating = true;
    });

    try {
      final response = await AIAssistantService.instance.generateContextualResponse(
        userMessage: 'Translate this message to English: "$message"',
        userContext: 'Please provide only the translation without explanation',
        currentUser: AuthService.instance.currentUser,
      );

      _showTranslationDialog(message, response);
    } catch (e) {
      final userFriendlyError = OpenAIConfig.getUserFriendlyError(e);
      _showError('Translation failed: $userFriendlyError');
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  void _showTranslationDialog(String original, String translation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Translation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Original:', style: Theme.of(context).textTheme.titleSmall),
            Text(original),
            const SizedBox(height: 12),
            Text('Translation:', style: Theme.of(context).textTheme.titleSmall),
            Text(translation),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showModerationWarning(String reason) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            Text('Content Warning'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your message was not sent because it may contain inappropriate content.'),
            const SizedBox(height: 12),
            Text('Reason: $reason'),
            const SizedBox(height: 12),
            Text('Please review and modify your message to follow community guidelines.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final otherUser = widget.chat.otherUser;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              backgroundImage: otherUser?.avatarUrl != null
                  ? NetworkImage(otherUser!.avatarUrl!)
                  : null,
              child: otherUser?.avatarUrl == null
                  ? Text(
                      otherUser?.fullName?.substring(0, 1).toUpperCase() ?? '?',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    otherUser?.fullName ?? 'Unknown User',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (otherUser?.onlineStatus != null)
                    Text(
                      _getStatusText(otherUser!.onlineStatus!),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(otherUser.onlineStatus!),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(_showAIAssistant ? Icons.smart_toy : Icons.smart_toy_outlined),
            onPressed: () {
              setState(() {
                _showAIAssistant = !_showAIAssistant;
              });
            },
            tooltip: 'AI Assistant',
          ),
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: () {
              // TODO: Implement call functionality
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // TODO: Implement more options
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _messages.isEmpty
                    ? _buildEmptyState()
                    : _buildMessageList(),
          ),
          if (_showAIAssistant && _messageSuggestions.isNotEmpty)
            _buildMessageSuggestions(),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No messages yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start the conversation by sending a message',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isCurrentUser = message.senderId == AuthService.instance.currentUser?.id;
        final showTimestamp = index == 0 || 
            _messages[index - 1].createdAt.difference(message.createdAt).inMinutes > 5;

        return Column(
          children: [
            if (showTimestamp)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  _formatMessageTime(message.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ),
            MessageBubble(
              message: message,
              isCurrentUser: isCurrentUser,
              onTranslate: (msg) => _translateMessage(msg),
              isTranslating: _isTranslating,
            ),
          ],
        );
      },
    );
  }

  Widget _buildMessageSuggestions() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.smart_toy,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 4),
              Text(
                'AI Suggestions',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _messageSuggestions.map((suggestion) {
              return GestureDetector(
                onTap: () {
                  _messageController.text = suggestion;
                  setState(() {
                    _messageSuggestions.clear();
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    suggestion,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: _showAIAssistant ? 'Type a message (AI-assisted)...' : 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: null,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          FloatingActionButton(
            onPressed: _isSending ? null : _sendMessage,
            mini: true,
            child: _isSending
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.send),
          ),
        ],
      ),
    );
  }

  String _getStatusText(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return 'Online';
      case OnlineStatus.offline:
        return 'Offline';
      case OnlineStatus.typing:
        return 'Typing...';
    }
  }

  Color _getStatusColor(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return Colors.green;
      case OnlineStatus.offline:
        return Colors.grey;
      case OnlineStatus.typing:
        return Colors.blue;
    }
  }

  String _formatMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}

class MessageBubble extends StatelessWidget {
  final Message message;
  final bool isCurrentUser;
  final Function(String)? onTranslate;
  final bool isTranslating;

  const MessageBubble({
    super.key,
    required this.message,
    required this.isCurrentUser,
    this.onTranslate,
    this.isTranslating = false,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isCurrentUser
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onLongPress: onTranslate != null ? () => onTranslate!(message.content) : null,
              child: Text(
                message.content,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isCurrentUser
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _formatTime(message.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isCurrentUser
                        ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
                        : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                  ),
                ),
                if (onTranslate != null) ...[
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: isTranslating ? null : () => onTranslate!(message.content),
                    child: Icon(
                      isTranslating ? Icons.hourglass_empty : Icons.translate,
                      size: 14,
                      color: isCurrentUser
                          ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
                          : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                    ),
                  ),
                ],
                if (isCurrentUser) ...[
                  const SizedBox(width: 4),
                  Icon(
                    message.isRead ? Icons.done_all : Icons.done,
                    size: 16,
                    color: message.isRead
                        ? Colors.blue
                        : Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}