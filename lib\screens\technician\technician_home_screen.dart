import 'package:flutter/material.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/services/request_service.dart';
import 'package:mlink/screens/auth/login_screen.dart';
import 'package:mlink/widgets/app_logo.dart';
import 'package:mlink/screens/chat/chat_screen.dart';
import 'package:mlink/screens/portfolio/portfolio_screen.dart';
import 'package:mlink/screens/profile/profile_screen.dart';
import 'package:mlink/models/request.dart';
import 'package:mlink/models/technician.dart';
import 'package:mlink/widgets/job_cards.dart';

class TechnicianHomeScreen extends StatefulWidget {
  const TechnicianHomeScreen({super.key});

  @override
  State<TechnicianHomeScreen> createState() => _TechnicianHomeScreenState();
}

class _TechnicianHomeScreenState extends State<TechnicianHomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const TechnicianDashboard(),
    const TechnicianJobsScreen(),
    const ChatScreen(),
    const PortfolioScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined),
            activeIcon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.work_outline),
            activeIcon: Icon(Icons.work),
            label: 'Jobs',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble_outline),
            activeIcon: Icon(Icons.chat_bubble),
            label: 'Chat',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.photo_library_outlined),
            activeIcon: Icon(Icons.photo_library),
            label: 'Portfolio',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}

class TechnicianDashboard extends StatefulWidget {
  const TechnicianDashboard({super.key});

  @override
  State<TechnicianDashboard> createState() => _TechnicianDashboardState();
}

class _TechnicianDashboardState extends State<TechnicianDashboard> {
  bool _isAvailable = false;

  @override
  Widget build(BuildContext context) {
    final user = AuthService.instance.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      user?.fullName?.substring(0, 1).toUpperCase() ?? 'T',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Technician Dashboard',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Hello, ${user?.fullName?.split(' ').first ?? 'Technician'}!',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.notifications_outlined,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    onPressed: () {
                      // TODO: Implement notifications
                    },
                  ),
                  const SizedBox(width: 8),
                  AppLogo(
                    width: AppLogoSizes.small,
                    height: AppLogoSizes.small,
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Availability Status Toggle
              Card(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: (_isAvailable ? Colors.green : Colors.grey).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          _isAvailable ? Icons.check_circle : Icons.schedule,
                          color: _isAvailable ? Colors.green : Colors.grey,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _isAvailable ? 'Available for Work' : 'Not Available',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _isAvailable ? 'Ready to receive service requests' : 'Turn on to start receiving job requests',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Switch(
                        value: _isAvailable,
                        onChanged: (value) {
                          setState(() => _isAvailable = value);
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Stats Cards
              Text(
                'Your Performance',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'Jobs Completed',
                      value: '0',
                      icon: Icons.task_alt,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: StatsCard(
                      title: 'Earnings',
                      value: 'TSh 0',
                      icon: Icons.account_balance_wallet,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'Rating',
                      value: '0.0',
                      icon: Icons.star,
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: StatsCard(
                      title: 'Response Time',
                      value: '0 min',
                      icon: Icons.speed,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Skills Section
              Text(
                'Your Skills',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.build,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Add your skills and expertise',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Complete your profile to start receiving job requests',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // Available Jobs
              Text(
                'Available Service Requests',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _isAvailable ? 'No requests available' : 'Set availability to see requests',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isAvailable ? 'Service requests will appear here' : 'Complete your profile and set availability to receive requests',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TechnicianJobsScreen extends StatefulWidget {
  const TechnicianJobsScreen({super.key});

  @override
  State<TechnicianJobsScreen> createState() => _TechnicianJobsScreenState();
}

class _TechnicianJobsScreenState extends State<TechnicianJobsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  TechnicianCategory _selectedCategory = TechnicianCategory.other;
  bool _urgentOnly = false;
  String _sortBy = 'created_at';
  bool _sortAscending = false;
  List<TechnicianRequest> _availableJobs = [];
  List<TechnicianRequest> _myActiveJobs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadJobs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadJobs() async {
    setState(() => _isLoading = true);
    try {
      // Load available technician service requests
      final requests = await RequestService.instance.getAvailableRequests(RequestType.technicianService);
      _availableJobs = requests.cast<TechnicianRequest>();
      
      // Load technician's active jobs
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final userRequests = await RequestService.instance.getUserRequests(user.id);
        _myActiveJobs = userRequests
            .where((r) => r.type == RequestType.technicianService &&
                         (r.status == RequestStatus.accepted || r.status == RequestStatus.inProgress))
            .cast<TechnicianRequest>()
            .toList();
      }
      
      _applyFiltersAndSort();
    } catch (e) {
      print('Error loading jobs: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load jobs: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _applyFiltersAndSort() {
    List<TechnicianRequest> filteredJobs = List.from(_availableJobs);
    
    // Apply category filter
    if (_selectedCategory != TechnicianCategory.other) {
      filteredJobs = filteredJobs.where((job) => job.category == _selectedCategory).toList();
    }
    
    // Apply urgency filter
    if (_urgentOnly) {
      filteredJobs = filteredJobs.where((job) => job.isUrgent).toList();
    }
    
    // Apply sorting
    filteredJobs.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'created_at':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'payment':
          final aRate = a.fixedRate ?? (a.hourlyRate ?? 0) * (a.estimatedHours ?? 1);
          final bRate = b.fixedRate ?? (b.hourlyRate ?? 0) * (b.estimatedHours ?? 1);
          comparison = aRate.compareTo(bRate);
          break;
        case 'urgency':
          comparison = (a.isUrgent ? 1 : 0).compareTo(b.isUrgent ? 1 : 0);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });
    
    setState(() {
      _availableJobs = filteredJobs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Service Jobs',
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.refresh,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                        onPressed: _loadJobs,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Category Filter
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildCategoryFilter(),
                        const SizedBox(width: 8),
                        _buildUrgentFilter(),
                        const SizedBox(width: 8),
                        _buildSortButton(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Tab Bar
            TabBar(
              controller: _tabController,
              indicatorColor: Theme.of(context).colorScheme.primary,
              labelColor: Theme.of(context).colorScheme.primary,
              unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              tabs: [
                Tab(
                  text: 'Available (${_availableJobs.length})',
                  icon: const Icon(Icons.work_outline),
                ),
                Tab(
                  text: 'My Jobs (${_myActiveJobs.length})',
                  icon: const Icon(Icons.assignment),
                ),
              ],
            ),
            
            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAvailableJobsTab(),
                  _buildMyJobsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return PopupMenuButton<TechnicianCategory>(
      icon: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.category,
              size: 16,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            const SizedBox(width: 4),
            Text(
              _selectedCategory.name.replaceAll('_', ' ').toUpperCase(),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimary,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ],
        ),
      ),
      onSelected: (category) {
        setState(() => _selectedCategory = category);
        _applyFiltersAndSort();
      },
      itemBuilder: (context) => TechnicianCategory.values.map((category) {
        return PopupMenuItem(
          value: category,
          child: Row(
            children: [
              Icon(_getCategoryIcon(category), size: 16),
              const SizedBox(width: 8),
              Text(category.name.replaceAll('_', ' ').toUpperCase()),
              if (_selectedCategory == category) ...
              [
                const Spacer(),
                Icon(Icons.check, size: 16, color: Theme.of(context).colorScheme.primary),
              ],
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildUrgentFilter() {
    return GestureDetector(
      onTap: () {
        setState(() => _urgentOnly = !_urgentOnly);
        _applyFiltersAndSort();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: _urgentOnly 
            ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2)
            : Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _urgentOnly ? Icons.priority_high : Icons.flash_on,
              size: 16,
              color: _urgentOnly ? Colors.red : Theme.of(context).colorScheme.onPrimary,
            ),
            const SizedBox(width: 4),
            Text(
              'URGENT',
              style: TextStyle(
                color: _urgentOnly ? Colors.red : Theme.of(context).colorScheme.onPrimary,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              size: 16,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            const SizedBox(width: 4),
            Text(
              'SORT',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimary,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      onSelected: (value) {
        setState(() {
          if (_sortBy == value) {
            _sortAscending = !_sortAscending;
          } else {
            _sortBy = value;
            _sortAscending = false;
          }
        });
        _applyFiltersAndSort();
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'created_at',
          child: Row(
            children: [
              Icon(Icons.access_time, size: 16),
              const SizedBox(width: 8),
              const Text('Date Posted'),
              if (_sortBy == 'created_at') ...
              [
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
        PopupMenuItem(
          value: 'payment',
          child: Row(
            children: [
              Icon(Icons.payments, size: 16),
              const SizedBox(width: 8),
              const Text('Payment Rate'),
              if (_sortBy == 'payment') ...
              [
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
        PopupMenuItem(
          value: 'urgency',
          child: Row(
            children: [
              Icon(Icons.priority_high, size: 16),
              const SizedBox(width: 8),
              const Text('Urgency'),
              if (_sortBy == 'urgency') ...
              [
                const Spacer(),
                Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvailableJobsTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (_availableJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Available Jobs',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check your category filters or try again later',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _availableJobs.length,
        itemBuilder: (context, index) {
          final job = _availableJobs[index];
          return TechnicianJobCard(
            job: job,
            onAccept: () => _acceptJob(job),
            onViewDetails: () => _viewJobDetails(job),
          );
        },
      ),
    );
  }

  Widget _buildMyJobsTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (_myActiveJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_turned_in,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Active Jobs',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Accept jobs from the Available tab',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myActiveJobs.length,
        itemBuilder: (context, index) {
          final job = _myActiveJobs[index];
          return MyJobCard(
            job: job,
            onUpdateStatus: (status) => _updateJobStatus(job, status),
            onViewDetails: () => _viewJobDetails(job),
          );
        },
      ),
    );
  }

  IconData _getCategoryIcon(TechnicianCategory category) {
    switch (category) {
      case TechnicianCategory.electrician:
        return Icons.electrical_services;
      case TechnicianCategory.plumber:
        return Icons.plumbing;
      case TechnicianCategory.mechanic:
        return Icons.build;
      case TechnicianCategory.carpenter:
        return Icons.carpenter;
      case TechnicianCategory.painter:
        return Icons.format_paint;
      case TechnicianCategory.cleaner:
        return Icons.cleaning_services;
      case TechnicianCategory.gardener:
        return Icons.yard;
      case TechnicianCategory.applianceRepair:
        return Icons.home_repair_service;
      case TechnicianCategory.computerRepair:
        return Icons.computer;
      case TechnicianCategory.phoneRepair:
        return Icons.phone_android;
      case TechnicianCategory.hvac:
        return Icons.ac_unit;
      case TechnicianCategory.other:
        return Icons.category;
    }
  }

  Future<void> _acceptJob(TechnicianRequest job) async {
    try {
      final user = AuthService.instance.currentUser;
      if (user == null) return;
      
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Accept Service Job'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Accept this ${job.category.name.replaceAll('_', ' ')} job?'),
              const SizedBox(height: 8),
              if (job.fixedRate != null) ...[
                Text('Fixed Rate: TSh ${job.fixedRate!.toStringAsFixed(0)}'),
              ] else if (job.hourlyRate != null) ...[
                Text('Hourly Rate: TSh ${job.hourlyRate!.toStringAsFixed(0)}/hr'),
                if (job.estimatedHours != null) ...[
                  Text('Estimated: ${job.estimatedHours}h (≈TSh ${(job.hourlyRate! * job.estimatedHours!).toStringAsFixed(0)})'),
                ],
              ],
              if (job.isUrgent) ...[
                const SizedBox(height: 4),
                const Text(
                  '⚠️ This is an urgent job',
                  style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Accept'),
            ),
          ],
        ),
      );
      
      if (confirmed == true) {
        await RequestService.instance.assignRequest(job.id, user.id, job.type);
        await _loadJobs();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Service job accepted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to accept job: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateJobStatus(BaseRequest job, RequestStatus status) async {
    try {
      final user = AuthService.instance.currentUser;
      if (user == null) return;
      
      await RequestService.instance.updateRequestStatus(job.id, status, user.id);
      await _loadJobs();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Job status updated to ${status.name}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _viewJobDetails(TechnicianRequest job) {
    showDialog(
      context: context,
      builder: (context) => JobDetailsDialog(job: job),
    );
  }
}


