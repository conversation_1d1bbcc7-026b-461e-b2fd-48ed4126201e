import 'package:flutter/material.dart';
import 'package:mlink/services/chat_service.dart';
import 'package:mlink/services/auth_service.dart';
import 'package:mlink/services/backend_api_service.dart';
import 'package:mlink/models/chat.dart';
import 'package:mlink/models/message.dart';
import 'package:mlink/models/user.dart';
import 'package:mlink/screens/chat/chat_conversation_screen.dart';
import 'package:mlink/screens/ai_assistant/ai_assistant_screen.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  List<Chat> _chats = [];
  bool _isLoading = true;
  String? _error;
  Map<String, dynamic> _chatOverview = {};
  Map<String, dynamic> _messageStats = {};
  final TextEditingController _searchController = TextEditingController();
  List<Chat> _filteredChats = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadChats();
    _subscribeToChats();
    _loadChatAnalytics();
  }

  @override
  void dispose() {
    _searchController.dispose();
    final user = AuthService.instance.currentUser;
    if (user != null) {
      ChatService.instance.unsubscribeFromChats(user.id);
    }
    super.dispose();
  }


  Future<void> _loadChats() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final user = AuthService.instance.currentUser;
      if (user != null) {
        final chats = await ChatService.instance.getUserChats(user.id);
        setState(() {
          _chats = chats;
          _filteredChats = chats;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadChatAnalytics() async {
    try {
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final results = await Future.wait([
          _loadChatOverview(user.id),
          _loadMessageStats(user.id),
        ]);
      }
    } catch (e) {
      print('Error loading chat analytics: $e');
    }
  }

  Future<void> _loadChatOverview(String userId) async {
    try {
      final response = await BackendApiService.getChatOverview(userId);
      if (response['success'] == true && response['overview'] != null) {
        setState(() {
          _chatOverview = response['overview'];
        });
      }
    } catch (e) {
      print('Error loading chat overview: $e');
    }
  }

  Future<void> _loadMessageStats(String userId) async {
    try {
      final response = await BackendApiService.getMessageStats(userId, null);
      if (response['success'] == true && response['stats'] != null) {
        setState(() {
          _messageStats = response['stats'];
        });
      }
    } catch (e) {
      print('Error loading message stats: $e');
    }
  }

  void _performSearch(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredChats = _chats;
        _isSearching = false;
      } else {
        _isSearching = true;
        _filteredChats = _chats.where((chat) {
          final otherUser = chat.otherUser;
          final userName = otherUser?.fullName?.toLowerCase() ?? '';
          final lastMessage = chat.lastMessage?.toLowerCase() ?? '';
          final searchQuery = query.toLowerCase();
          
          return userName.contains(searchQuery) || lastMessage.contains(searchQuery);
        }).toList();
      }
    });
  }

  void _subscribeToChats() {
    final user = AuthService.instance.currentUser;
    if (user != null) {
      ChatService.instance.subscribeToChats(user.id);
      ChatService.instance.chatsStream.listen((chats) {
        setState(() {
          _chats = chats;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Messages'),
        actions: [
          IconButton(
            icon: const Icon(Icons.assistant),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AIAssistantScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // TODO: Implement more options
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorState()
              : _chats.isEmpty
                  ? _buildEmptyState()
                  : Column(
                      children: [
                        if (_chatOverview.isNotEmpty) _buildChatOverviewStats(),
                        Expanded(child: _buildChatList()),
                      ],
                    ),
    );
  }

  Widget _buildErrorState() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                prefixIcon: Icon(
                  Icons.search,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              ),
              onChanged: _performSearch,
            ),
          ),
          const SizedBox(height: 32),
          
          // Main content area
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.smart_toy_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'Start a conversation',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Ask me anything or start chatting with someone',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                
                // Quick action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildQuickActionCard(
                      context: context,
                      icon: Icons.assistant,
                      title: 'AI Assistant',
                      subtitle: 'Get help with anything',
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const AIAssistantScreen(),
                          ),
                        );
                      },
                    ),
                    _buildQuickActionCard(
                      context: context,
                      icon: Icons.refresh,
                      title: 'Refresh',
                      subtitle: 'Try loading again',
                      onTap: _loadChats,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Message input area
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Type a message to get started...',
                      border: InputBorder.none,
                      hintStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    maxLines: null,
                    onSubmitted: (value) {
                      if (value.trim().isNotEmpty) {
                        // Navigate to AI Assistant with the message
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => AIAssistantScreen(initialMessage: value.trim()),
                          ),
                        );
                      }
                    },
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AIAssistantScreen(),
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.send,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                prefixIcon: Icon(
                  Icons.search,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              ),
              onChanged: _performSearch,
            ),
          ),
          const SizedBox(height: 32),
          
          // Main content area
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.chat_bubble_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'No conversations yet',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Conversations will appear here when you start interacting with agents and technicians',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                
                // Quick action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildQuickActionCard(
                      context: context,
                      icon: Icons.assistant,
                      title: 'AI Assistant',
                      subtitle: 'Get help with anything',
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const AIAssistantScreen(),
                          ),
                        );
                      },
                    ),
                    _buildQuickActionCard(
                      context: context,
                      icon: Icons.motorcycle,
                      title: 'Make Request',
                      subtitle: 'Start a new service',
                      onTap: () {
                        // Navigate to home tab to create request
                        Navigator.of(context).pop(); // Go back to home
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Message input area
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Type a message to get started...',
                      border: InputBorder.none,
                      hintStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    maxLines: null,
                    onSubmitted: (value) {
                      if (value.trim().isNotEmpty) {
                        // Navigate to AI Assistant with the message
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => AIAssistantScreen(initialMessage: value.trim()),
                          ),
                        );
                      }
                    },
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AIAssistantScreen(),
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.send,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatList() {
    final chatsToShow = _filteredChats;
    
    if (_isSearching && chatsToShow.isEmpty) {
      return _buildNoSearchResults();
    }
    
    return RefreshIndicator(
      onRefresh: () async {
        await Future.wait([_loadChats(), _loadChatAnalytics()]);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: chatsToShow.length,
        itemBuilder: (context, index) {
          final chat = chatsToShow[index];
          return ChatTile(
            chat: chat,
            onTap: () => _openChat(chat),
            messageStats: _messageStats,
          );
        },
      ),
    );
  }

  void _openChat(Chat chat) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChatConversationScreen(chat: chat),
      ),
    );
  }

  Widget _buildChatOverviewStats() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem(
            'Total Chats',
            '${_chatOverview['total_chats'] ?? 0}',
            Icons.chat,
          ),
          _buildStatItem(
            'Unread',
            '${_chatOverview['unread_count'] ?? 0}',
            Icons.mark_chat_unread,
          ),
          _buildStatItem(
            'Active Today',
            '${_chatOverview['active_today'] ?? 0}',
            Icons.today,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Messages'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Search across all conversations...',
            prefixIcon: Icon(Icons.search),
          ),
          onSubmitted: (query) async {
            Navigator.of(context).pop();
            if (query.isNotEmpty) {
              await _performGlobalSearch(query);
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _performGlobalSearch(String query) async {
    try {
      final user = AuthService.instance.currentUser;
      if (user != null) {
        final response = await BackendApiService.searchMessages(user.id, query);
        if (response['success'] == true) {
          // Handle search results
          _showSearchResults(response['results']);
        }
      }
    } catch (e) {
      print('Error performing global search: $e');
    }
  }

  void _showSearchResults(List<dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Search Results (${results.length})'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: results.isEmpty
              ? const Center(child: Text('No messages found'))
              : ListView.builder(
                  itemCount: results.length,
                  itemBuilder: (context, index) {
                    final result = results[index];
                    return ListTile(
                      title: Text(result['content'] ?? ''),
                      subtitle: Text(result['sender_name'] ?? 'Unknown'),
                      trailing: Text(result['created_at'] ?? ''),
                      onTap: () {
                        Navigator.of(context).pop();
                        // Navigate to the specific chat
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No conversations found',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Try a different search term',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }
}

class ChatTile extends StatelessWidget {
  final Chat chat;
  final VoidCallback onTap;
  final Map<String, dynamic>? messageStats;

  const ChatTile({
    super.key,
    required this.chat,
    required this.onTap,
    this.messageStats,
  });

  @override
  Widget build(BuildContext context) {
    final otherUser = chat.otherUser;
    final hasUnread = chat.unreadCount > 0;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: CircleAvatar(
          radius: 28,
          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          backgroundImage: otherUser?.avatarUrl != null
              ? NetworkImage(otherUser!.avatarUrl!)
              : null,
          child: otherUser?.avatarUrl == null
              ? Text(
                  otherUser?.fullName?.substring(0, 1).toUpperCase() ?? '?',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                otherUser?.fullName ?? 'Unknown User',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: hasUnread ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (hasUnread)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  chat.unreadCount.toString(),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (chat.lastMessage != null)
              Text(
                chat.lastMessage!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(
                    alpha: hasUnread ? 0.9 : 0.7,
                  ),
                  fontWeight: hasUnread ? FontWeight.w500 : FontWeight.normal,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                if (otherUser?.onlineStatus != null)
                  Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.only(right: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor(otherUser!.onlineStatus!),
                      shape: BoxShape.circle,
                    ),
                  ),
                if (messageStats != null && messageStats!['response_time'] != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    margin: const EdgeInsets.only(right: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${messageStats!['response_time']}m',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                if (chat.lastMessageAt != null)
                  Text(
                    _formatTime(chat.lastMessageAt!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
              ],
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Color _getStatusColor(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return Colors.green;
      case OnlineStatus.offline:
        return Colors.grey;
      case OnlineStatus.typing:
        return Colors.blue;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}